# 试卷下载 API 文档

## 1. API 基础信息

- **基础路径**: `/api/papers/download/{id}`
- **支持格式**: PDF、Word (docx)
- **认证要求**: 需要登录验证
- **错误处理**: 使用统一的错误响应格式

## 2. 下载接口说明

### 2.1 基础下载接口
**接口说明**: 下载试卷文件（支持PDF和Word两种格式）

- **请求路径**: `GET /api/papers/download/{id}`
- **请求参数**:
  - `id`: 试卷ID（路径参数）
  - `format`: 文件格式（可选，默认pdf）
    - `pdf`: PDF格式（默认）
    - `word` 或 `docx`: Word文档格式
  - `paperType`: 试卷类型（可选）

**请求示例**:
```http
GET /api/papers/download/123?format=pdf&paperType=regular
```

**响应说明**:
- 成功响应：返回文件流，浏览器会自动下载
- 失败响应：返回错误文本文件

### 2.2 PDF 版本下载
**特点**:
1. 支持PDF格式
2. 支持下载代理工具（迅雷、IDM等）
3. 支持断点续传
4. 文件名格式：`试卷标题.pdf` 或 `paper_123.pdf`

**响应头**:
```http
Content-Type: application/pdf
Content-Disposition: attachment; filename="试卷标题.pdf"
Cache-Control: no-cache, no-store, must-revalidate
Pragma: no-cache
Expires: 0
Accept-Ranges: bytes  // 支持断点续传
Content-Length: [文件大小]  // 文件大小
```

### 2.3 Word 版本下载
**特点**:
1. 支持Word文档格式（.docx）
2. 保持试卷格式和排版
3. 文件名格式：`试卷标题_123.docx`

**响应头**:
```http
Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document
Content-Disposition: attachment; filename="试卷标题_123.docx"
Cache-Control: no-cache, no-store, must-revalidate
Pragma: no-cache
Expires: 0
```

## 3. 错误处理

### 3.1 常见错误码

- `404 Not Found`: 试卷不存在
- `500 Internal Server Error`: 文件生成失败
- `401 Unauthorized`: 未登录或权限不足

### 3.2 错误响应格式

当文件生成失败时，会返回一个错误文本文件，包含以下信息：
```text
=== 文件生成错误 ===

试卷ID: [ID]
格式: [格式]
错误时间: [时间]
错误信息: [错误描述]

请稍后重试或联系系统管理员。
```

## 4. 下载统计

1. 记录每次下载：
   - 试卷ID
   - 下载用户ID
   - 文件格式
   - 下载时间
   - IP地址

2. 更新试卷统计：
   - 下载次数
   - 最后下载时间

3. 防重复机制：
   - 同一用户对同一试卷的重复下载会被记录
   - 使用缓存机制防止短时间内重复记录

## 5. 特殊处理

1. 下载代理工具兼容性：
   - 支持迅雷、IDM等下载工具
   - 提供断点续传支持
   - 优化响应头设置

2. 文件名处理：
   - 使用试卷标题作为文件名
   - 对特殊字符进行转义
   - 添加试卷ID作为备份

3. 安全性：
   - 防止缓存
   - 防止XSS攻击
   - 记录IP地址

## 6. 使用示例

### 6.1 下载PDF版本
```javascript
// JavaScript示例
fetch(`/api/papers/download/${paperId}?format=pdf`, {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer ' + token
    }
}).then(response => {
    if (!response.ok) {
        throw new Error('下载失败');
    }
    return response.blob();
}).then(blob => {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '试卷标题.pdf';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
});
```

### 6.2 下载Word版本
```javascript
// JavaScript示例
fetch(`/api/papers/download/${paperId}?format=word`, {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer ' + token
    }
}).then(response => {
    if (!response.ok) {
        throw new Error('下载失败');
    }
    return response.blob();
}).then(blob => {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '试卷标题_123.docx';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
});
```

## 7. 注意事项

1. 下载前需要登录验证
2. 文件名会根据试卷标题生成
3. 支持PDF和Word两种格式
4. 下载过程会记录统计信息
5. 建议使用浏览器默认下载功能
6. 下载代理工具需要特殊处理
7. 大文件下载建议使用断点续传
