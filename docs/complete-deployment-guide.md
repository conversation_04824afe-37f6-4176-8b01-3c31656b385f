# 麦子教育系统完整部署指南

## 系统概述

麦子教育系统是一个完整的在线教育平台，包含题目管理、智能组卷、用户管理和管理员后台等功能。

### 主要功能模块
- **智能出题**: AI辅助生成题目
- **题目上传**: 用户提交题目到审核队列
- **题库管理**: 管理和维护题目库
- **智能组卷**: 自动生成试卷
- **管理员后台**: 题目审核、用户管理、系统统计

### 角色权限体系
- **管理员 (role=1)**: 拥有所有权限，可以审核题目、管理用户
- **普通用户 (role=2)**: 只能查看、组卷、下载试卷
- **教师 (role=3)**: 与普通用户权限相同

## 环境要求

### 基础环境
- **Java**: JDK 8 或更高版本
- **数据库**: MySQL 5.7 或更高版本
- **构建工具**: Maven 3.6+
- **服务器**: 支持Spring Boot的任意服务器

### 推荐配置
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少10GB可用空间
- **网络**: 稳定的网络连接

## 部署步骤

### 1. 数据库初始化

#### 创建数据库
```sql
CREATE DATABASE maizi_edu_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE maizi_edu_system;
```

#### 执行数据库脚本
```bash
# 1. 执行主数据库脚本
mysql -u root -p maizi_edu_system < src/main/resources/db/maizipapergendb.sql

# 2. 创建管理员账号
mysql -u root -p maizi_edu_system < src/main/resources/db/admin-user-init.sql
```

### 2. 应用配置

#### 修改数据库连接配置
编辑 `src/main/resources/application.properties`:

```properties
# 数据库配置
spring.datasource.url=*****************************************************************************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 服务器配置
server.port=8080
server.servlet.context-path=

# JWT配置
jwt.secret=your_jwt_secret_key_here
jwt.expiration=86400000

# 文件上传配置
file.upload.base-path=./uploads
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 审核系统配置
app.audit.daily-limit=50
app.audit.auto-approve-days=7
app.audit.notification.enabled=true

# 定时任务配置
app.task.topic-audit.enabled=true
app.task.audit-report.enabled=true
```

### 3. 编译和打包

```bash
# 清理并编译
mvn clean compile

# 运行测试
mvn test

# 打包应用
mvn clean package -DskipTests
```

### 4. 启动应用

#### 开发环境启动
```bash
mvn spring-boot:run
```

#### 生产环境启动
```bash
java -jar target/maizi-edu-sys-1.0.0.jar
```

#### 后台启动（推荐生产环境）
```bash
nohup java -jar target/maizi-edu-sys-1.0.0.jar > app.log 2>&1 &
```

### 5. 验证部署

启动成功后，控制台会显示：

```
╔══════════════════════════════════════════════════════════════════════════════╗
║                           麦子教育系统启动成功                                ║
║  应用访问地址:                                                                ║
║    本地访问: http://localhost:8080                                           ║
║    网络访问: http://*************:8080                                      ║
║                                                                              ║
║  🔐 管理员登录页面:                                                           ║
║    本地访问: http://localhost:8080/admin/login                              ║
║    网络访问: http://*************:8080/admin/login                         ║
║                                                                              ║
║  🔐 管理员后台地址 (登录后访问):                                              ║
║    本地访问: http://localhost:8080/admin/topics/audit                       ║
║    网络访问: http://*************:8080/admin/topics/audit                  ║
╚══════════════════════════════════════════════════════════════════════════════╝
```

## 默认账号信息

### 管理员账号
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: 管理员 (role=1)
- **权限**: 所有功能权限

### 测试账号
- **普通用户**: `user001` / `admin123`
- **教师用户**: `teacher001` / `admin123`

**⚠️ 重要提示**: 生产环境请立即修改默认密码！

## 功能使用指南

### 普通用户功能

#### 1. 用户注册和登录
- 访问 `/auth/login` 进行登录
- 访问 `/auth/register` 进行注册

#### 2. 智能出题
- 访问 `/main/chat`
- 使用AI助手生成题目

#### 3. 题目上传
- 访问 `/topics/upload-topics`
- 上传题目到审核队列
- 查看审核状态

#### 4. 题库浏览
- 访问 `/topics/bank`
- 浏览和搜索题目

#### 5. 智能组卷
- 访问 `/paper/generate`
- 配置试卷参数
- 生成和下载试卷

### 管理员功能

#### 1. 管理员登录
- 访问 `/admin/login`
- 使用管理员账号登录

#### 2. 题目审核
- 访问 `/admin/topics/audit`
- 审核用户提交的题目
- 批量操作和统计分析

#### 3. 用户管理
- 访问 `/admin/users`
- 管理用户账号和权限

#### 4. 系统统计
- 访问 `/admin/stats`
- 查看系统使用统计
- 数据可视化分析

## 安全配置

### 1. 密码安全
```bash
# 修改管理员密码
UPDATE user SET password = '$2a$10$新的加密密码' WHERE username = 'admin';
```

### 2. JWT密钥
```properties
# 生成强密钥
jwt.secret=your_very_long_and_secure_secret_key_here_at_least_256_bits
```

### 3. 数据库安全
- 使用强密码
- 限制数据库访问IP
- 定期备份数据

### 4. 文件上传安全
```properties
# 限制文件类型和大小
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
```

## 监控和维护

### 1. 日志监控
```bash
# 查看应用日志
tail -f app.log

# 查看错误日志
grep "ERROR" app.log

# 查看管理员操作日志
grep "管理员" app.log
```

### 2. 数据库维护
```sql
-- 清理过期数据
DELETE FROM system_messages WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 优化表
OPTIMIZE TABLE topic_audit;
OPTIMIZE TABLE system_messages;
```

### 3. 性能监控
- 监控内存使用情况
- 监控数据库连接数
- 监控响应时间

## 故障排除

### 1. 启动失败
- 检查Java版本
- 检查数据库连接
- 检查端口占用

### 2. 登录问题
- 检查JWT配置
- 检查用户表数据
- 检查密码加密

### 3. 权限问题
- 检查用户角色
- 检查权限配置
- 检查拦截器配置

### 4. 文件上传问题
- 检查上传目录权限
- 检查文件大小限制
- 检查磁盘空间

## 扩展功能

### 1. 邮件通知
```properties
# 邮件配置
spring.mail.host=smtp.example.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your_password
```

### 2. Redis缓存
```properties
# Redis配置
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
```

### 3. 文件存储
```properties
# 阿里云OSS配置
aliyun.oss.endpoint=your_endpoint
aliyun.oss.accessKeyId=your_access_key
aliyun.oss.accessKeySecret=your_secret
```

## 技术支持

### 联系方式
- 技术文档: 查看 `docs/` 目录
- 问题反馈: 提交 GitHub Issue
- 技术交流: 加入技术群

### 常用命令
```bash
# 查看应用状态
ps aux | grep java

# 重启应用
pkill -f maizi-edu-sys
nohup java -jar target/maizi-edu-sys-1.0.0.jar > app.log 2>&1 &

# 数据库备份
mysqldump -u root -p maizi_edu_system > backup_$(date +%Y%m%d).sql
```

---

**祝您使用愉快！如有问题，请参考故障排除部分或联系技术支持。**
