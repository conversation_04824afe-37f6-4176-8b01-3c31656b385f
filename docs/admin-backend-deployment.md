# 管理员后台部署说明

## 概述

本文档说明了麦子教育系统管理员后台的安全部署和访问控制机制。

## 安全特性

### 1. 隐藏式后台入口
- **前端隐藏**: 普通用户在前端页面中看不到管理员后台入口
- **权限控制**: 只有 `role=1` 的管理员用户才能访问后台
- **控制台显示**: 管理员后台地址只在服务器启动时在控制台打印

### 2. 多层权限验证
- **身份验证**: 必须登录才能访问
- **角色验证**: 必须是管理员角色 (role=1)
- **状态验证**: 账号必须处于正常状态
- **实时检查**: 每次访问都进行权限验证

### 3. 访问日志记录
- **操作记录**: 记录所有管理员操作
- **安全审计**: 记录访问尝试和权限检查
- **异常监控**: 记录非法访问尝试

## 角色权限体系

### 角色定义
```
role = 1: 管理员 (拥有所有权限)
role = 2: 普通用户 (只能查看、组卷、下载)
role = 3: 教师 (与普通用户权限相同)
```

### 权限范围
- **管理员**: 增删改查所有数据，审核题目，管理用户
- **普通用户/教师**: 只能查看题目、生成试卷、下载试卷

## 部署步骤

### 1. 数据库初始化

执行数据库脚本：
```sql
-- 1. 执行主数据库脚本
source maizi_edu_sys/src/main/resources/db/maizipapergendb.sql;

-- 2. 创建管理员账号
source maizi_edu_sys/src/main/resources/db/admin-user-init.sql;
```

### 2. 应用配置

确保以下配置正确：
```properties
# 启用管理员后台安全检查
app.admin.security.enabled=true

# 启用访问日志
app.admin.access-log.enabled=true

# 管理员会话超时时间（分钟）
app.admin.session.timeout=60
```

### 3. 启动应用

启动应用后，控制台会显示：
```
╔══════════════════════════════════════════════════════════════════════════════╗
║                           麦子教育系统启动成功                                ║
║  🔐 管理员后台地址 (仅限 role=1 的管理员访问):                                ║
║    本地访问: http://localhost:8080/admin/topics/audit                        ║
║    网络访问: http://*************:8080/admin/topics/audit                   ║
╚══════════════════════════════════════════════════════════════════════════════╝
```

## 访问方式

### 1. 管理员登录
1. 使用管理员账号登录系统
2. 在控制台查看后台地址
3. 直接访问后台URL

### 2. 默认管理员账号
```
用户名: admin
密码: admin123
角色: 管理员 (role=1)
```

**⚠️ 安全提示**: 生产环境请立即修改默认密码！

### 3. 后台功能模块
- **题目审核**: `/admin/topics/audit`
- **用户管理**: `/admin/users`
- **系统统计**: `/admin/stats`
- **权限管理**: `/admin/permissions`

## 安全机制

### 1. 访问控制
```java
// AdminInterceptor 拦截所有 /admin/** 路径
// 检查用户是否为活跃的管理员
if (!adminSecurityService.isActiveAdmin(userId)) {
    // 重定向到访问拒绝页面
    response.sendRedirect("/admin/access-denied");
    return false;
}
```

### 2. 前端隐藏
```javascript
// 只有管理员才显示管理员菜单
if (user.role === 1) { // 管理员
    document.getElementById('adminMenu').style.display = 'block';
} else {
    document.getElementById('adminMenu').style.display = 'none';
}
```

### 3. 操作日志
```java
// 记录管理员操作
adminSecurityService.logAdminAccess(userId, "ACCESS", uri);
```

## 监控和维护

### 1. 日志监控
监控以下日志关键词：
- `管理员访问后台`
- `非管理员用户尝试访问管理后台`
- `已禁用的管理员尝试访问后台`

### 2. 安全检查
定期检查：
- 管理员账号状态
- 异常访问记录
- 权限分配情况

### 3. 密码策略
建议：
- 定期更换管理员密码
- 使用强密码策略
- 启用双因素认证（可扩展）

## 故障排除

### 1. 无法访问后台
检查项：
- 用户是否已登录
- 用户角色是否为管理员 (role=1)
- 用户状态是否正常 (status=1)
- 用户是否被删除 (deleted=0)

### 2. 权限检查失败
检查项：
- 数据库连接是否正常
- 用户表数据是否完整
- 权限服务是否正常工作

### 3. 页面访问异常
检查项：
- 拦截器配置是否正确
- 路径映射是否正确
- 静态资源是否可访问

## 扩展功能

### 1. 多级管理员
可以扩展为多级管理员：
```sql
-- 扩展角色定义
role = 1: 超级管理员
role = 11: 高级管理员  
role = 12: 普通管理员
```

### 2. 操作审计
可以添加详细的操作审计：
```sql
CREATE TABLE admin_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    admin_id BIGINT NOT NULL,
    operation VARCHAR(100) NOT NULL,
    resource VARCHAR(200),
    ip_address VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 安全增强
- IP白名单限制
- 登录失败锁定
- 双因素认证
- 操作确认机制

## 注意事项

1. **生产环境安全**
   - 修改默认管理员密码
   - 配置HTTPS访问
   - 设置防火墙规则

2. **备份策略**
   - 定期备份用户数据
   - 备份权限配置
   - 备份操作日志

3. **监控告警**
   - 监控异常访问
   - 监控权限变更
   - 监控系统性能

4. **合规要求**
   - 满足数据保护法规
   - 实施访问控制策略
   - 保留审计日志
