# 题目审核系统实现文档

## 系统概述

本文档描述了麦子教育系统中新增的题目审核功能的完整实现，包括用户行为日志、分级权限管理、管理员后台界面等功能。

## 角色权限体系

### 角色定义
- **管理员 (role=1)**: 拥有所有权限，可以审核题目、管理用户、查看统计数据
- **普通用户 (role=2)**: 只能查看、组卷、下载试卷，提交题目到审核队列
- **教师 (role=3)**: 与普通用户权限相同，只能查看、组卷、下载试卷

### 权限控制
- 所有题目提交都会进入审核队列，不再直接添加到题库
- 管理员可以审核通过或拒绝题目
- 超过7天未审核的题目会自动通过审核
- 用户会收到审核结果的系统消息通知

## 数据库设计

### 新增数据表

1. **topic_audit** - 题目审核表
   - 存储用户提交的待审核题目
   - 记录审核状态、审核员、审核时间等信息

2. **topic_rejected** - 被拒绝题目表
   - 存储被拒绝的题目及拒绝原因
   - 用户可以查看被拒绝的题目和原因

3. **system_messages** - 系统消息表
   - 存储审核结果通知、系统公告等消息
   - 支持已读/未读状态管理

4. **user_permissions** - 用户权限表
   - 存储用户的特殊权限（覆盖角色权限）
   - 支持权限过期时间设置

5. **role_permissions** - 角色权限表
   - 存储各角色的默认权限配置
   - 支持API路径权限控制

## 核心功能实现

### 1. 题目审核流程

#### 提交审核
- 用户上传题目时，题目进入审核队列而非直接添加到题库
- 支持每日提交数量限制（默认50道）
- 记录提交时间、用户信息等

#### 管理员审核
- 管理员可以查看待审核题目列表
- 支持批量审核操作
- 可以添加审核意见或拒绝原因

#### 自动审核
- 定时任务每天凌晨2点检查超时题目
- 超过7天未审核的题目自动通过
- 发送自动审核通过通知

### 2. 消息通知系统

#### 消息类型
- **AUDIT_APPROVED**: 审核通过通知
- **AUDIT_REJECTED**: 审核拒绝通知
- **SYSTEM_NOTICE**: 系统公告

#### 消息功能
- 实时未读消息数量显示
- 消息列表分页查看
- 批量标记已读功能
- 消息删除功能

### 3. 权限管理系统

#### 权限检查
- API接口权限验证
- 页面访问权限控制
- 资源操作权限限制

#### 权限配置
- 基于角色的默认权限
- 用户特殊权限覆盖
- 权限缓存优化

### 4. 管理员后台界面

#### 设计风格
- 遵循苹果设计规范
- 响应式布局设计
- 现代化UI组件

#### 功能模块
- **题目审核管理**: 审核列表、批量操作、统计图表
- **用户管理**: 用户列表、权限分配
- **系统统计**: 数据可视化、趋势分析
- **权限管理**: 角色权限配置

## API接口

### 审核相关接口
- `GET /api/admin/topics/audit` - 获取审核列表
- `POST /api/admin/topics/audit/{id}/approve` - 审核通过
- `POST /api/admin/topics/audit/{id}/reject` - 审核拒绝
- `GET /api/admin/topics/audit/pending-count` - 获取待审核数量

### 消息相关接口
- `GET /api/messages` - 获取用户消息列表
- `GET /api/messages/unread-count` - 获取未读消息数量
- `POST /api/messages/{id}/read` - 标记消息已读
- `DELETE /api/messages/{id}` - 删除消息

### 统计相关接口
- `GET /api/admin/stats/audit` - 获取审核统计
- `GET /api/admin/stats/audit/trend` - 获取审核趋势
- `GET /api/admin/stats/topics` - 获取题库统计

## 前端实现

### 统一导航栏
- 创建了统一的导航栏组件 `unified-navbar.html`
- 支持角色权限显示
- 实时消息通知功能
- 响应式设计

### 管理员页面
- **题目审核页面**: `/admin/topics/audit`
- **用户管理页面**: `/admin/users`
- **系统统计页面**: `/admin/stats`
- **权限管理页面**: `/admin/permissions`

### 题库管理页面
- 完善了题库管理功能
- 支持题目搜索、筛选
- 批量操作功能
- 统计数据展示

## 定时任务

### 自动审核任务
- **执行时间**: 每天凌晨2点
- **功能**: 自动审核超过7天的待审核题目
- **配置**: `app.task.topic-audit.enabled=true`

### 审核统计任务
- **执行时间**: 每天上午9点
- **功能**: 生成审核统计报告
- **配置**: `app.task.audit-report.enabled=true`

## 配置说明

```properties
# 题目审核配置
app.audit.daily-limit=50                    # 每日提交限制
app.audit.auto-approve-days=7               # 自动审核天数
app.audit.notification.enabled=true        # 是否启用通知

# 定时任务配置
app.task.topic-audit.enabled=true          # 是否启用审核定时任务
app.task.audit-report.enabled=true         # 是否启用报告定时任务

# 权限管理配置
app.permission.strict-mode=true            # 是否启用严格权限模式
app.permission.cache-enabled=true          # 是否启用权限缓存

# 消息系统配置
app.message.max-unread=100                 # 最大未读消息数
app.message.auto-cleanup-days=30           # 自动清理天数
```

## 部署说明

### 数据库迁移
1. 执行 `maizipapergendb.sql` 中的新增表结构
2. 插入默认角色权限数据
3. 更新现有用户的角色字段（如需要）

### 应用配置
1. 更新 `application.properties` 配置
2. 启用定时任务功能
3. 配置权限管理参数

### 前端资源
1. 部署新的页面模板
2. 更新静态资源文件
3. 配置CDN资源引用

## 注意事项

1. **数据迁移**: 现有题目数据不受影响，新提交的题目会进入审核流程
2. **权限兼容**: 现有用户权限会根据角色自动分配
3. **性能优化**: 大量审核数据时建议启用权限缓存
4. **监控告警**: 建议监控待审核题目数量，及时处理积压

## 后续扩展

1. **邮件通知**: 可以添加邮件通知功能
2. **审核工作流**: 可以实现多级审核流程
3. **AI辅助审核**: 可以集成AI进行初步审核
4. **数据分析**: 可以添加更多统计分析功能
