# 试卷管理系统 API 文档

## 1. API 基础信息

- **基础路径**: `/api/papers`
- **响应格式**: JSON
- **错误处理**: 所有接口均使用统一的 `ApiResponse` 封装响应

## 2. API 列表

### 2.1 试卷生成相关 API

#### 2.1.1 智能组卷
**接口说明**: 生成单份试卷

- **请求路径**: `POST /api/papers/generate`
- **请求参数**:
  ```json
  {
    "title": "试卷标题",
    "totalScore": 100,
    "typeScoreMap": {
      "singleChoice": 30,
      "multipleChoice": 20,
      "judgment": 20,
      "fillBlank": 15,
      "shortAnswer": 15
    },
    "difficultyDistribution": {
      "easy": 0.3,
      "medium": 0.5,
      "hard": 0.2
    },
    "knowledgePointConfigs": [
      {
        "knowledgeId": 123,
        "questionCount": 5
      }
    ]
  }
  ```
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "试卷生成成功",
    "data": {
      "paperId": 123,
      "title": "生成的试卷标题",
      "totalScore": 100,
      "questionCount": 20,
      "questions": [...]
    }
  }
  ```

#### 2.1.2 批量生成试卷
**接口说明**: 批量生成多份试卷

- **请求路径**: `POST /api/papers/generate-batch`
- **请求参数**:
  ```json
  {
    "title": "批量生成的试卷标题",
    "paperCount": 5,
    "totalScore": 100,
    "typeScoreMap": {...},
    "difficultyDistribution": {...},
    "knowledgePointConfigs": [...]
  }
  ```
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "批量生成完成",
    "data": {
      "papers": [
        {
          "paperId": 123,
          "title": "试卷1",
          "success": true,
          "errorMessage": null
        }
      ]
    }
  }
  ```

### 2.2 试卷管理 API

#### 2.2.1 保存试卷
**接口说明**: 保存试卷信息

- **请求路径**: `POST /api/papers`
- **请求参数**:
  ```json
  {
    "title": "试卷标题",
    "totalScore": 100,
    "paperType": "regular",
    "fileFormat": "pdf",
    "userId": 123,
    "questions": [...]
  }
  ```
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "试卷保存成功",
    "data": {
      "id": 123,
      "title": "试卷标题",
      "totalScore": 100
    }
  }
  ```

#### 2.2.2 获取试卷列表
**接口说明**: 获取试卷列表（支持分页、搜索、筛选和排序）

- **请求路径**: `GET /api/papers`
- **查询参数**:
  - `search`: 搜索关键词
  - `type`: 试卷类型
  - `sort`: 排序字段（格式：field,direction）
  - `page`: 当前页码（默认0）
  - `size`: 每页大小（默认10）
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "获取试卷列表成功",
    "data": {
      "totalElements": 100,
      "totalPages": 10,
      "content": [
        {
          "id": 123,
          "title": "试卷1",
          "totalScore": 100
        }
      ]
    }
  }
  ```

#### 2.2.3 获取试卷详情
**接口说明**: 获取指定试卷的详细信息

- **请求路径**: `GET /api/papers/{id}`
- **路径参数**:
  - `id`: 试卷ID
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "获取试卷详情成功",
    "data": {
      "id": 123,
      "title": "试卷标题",
      "totalScore": 100,
      "questions": [...]
    }
  }
  ```

#### 2.2.4 删除试卷
**接口说明**: 删除指定试卷

- **请求路径**: `DELETE /api/papers/{id}`
- **路径参数**:
  - `id`: 试卷ID
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "试卷删除成功",
    "data": null
  }
  ```

## 3. 错误响应格式

所有接口统一使用以下错误响应格式：

```json
{
  "success": false,
  "message": "错误描述",
  "data": null
}
```

## 4. 响应状态码

- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 未找到资源
- `500 Internal Server Error`: 服务器内部错误

## 5. 注意事项

1. 所有接口都需要经过身份验证
2. 请求参数必须符合 JSON 格式
3. 响应数据均经过 UTF-8 编码
4. 所有时间字段采用 ISO 8601 格式
5. 分页参数默认值：page=0, size=10
