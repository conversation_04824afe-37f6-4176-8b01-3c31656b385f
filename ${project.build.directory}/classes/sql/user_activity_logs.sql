-- 用户行为日志表
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    activity_type VARCHAR(50) NOT NULL COMMENT '活动类型',
    description TEXT NOT NULL COMMENT '活动描述',
    module VARCHAR(50) NOT NULL COMMENT '模块名称',
    target_id VARCHAR(100) COMMENT '操作对象ID',
    target_type VARCHAR(50) COMMENT '操作对象类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_path VARCHAR(500) COMMENT '请求路径',
    request_method VARCHAR(10) COMMENT '请求方法',
    result VARCHAR(20) NOT NULL DEFAULT 'SUCCESS' COMMENT '操作结果',
    error_message TEXT COMMENT '错误信息',
    duration BIGINT COMMENT '操作耗时(毫秒)',
    extra_data JSON COMMENT '额外数据',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_username (username),
    INDEX idx_activity_type (activity_type),
    INDEX idx_module (module),
    INDEX idx_create_time (create_time),
    INDEX idx_result (result)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为日志表';

-- 插入一些示例数据
INSERT INTO user_activity_logs (user_id, username, activity_type, description, module, result, create_time) VALUES
(1, 'admin', 'LOGIN', '用户登录系统', 'SYSTEM', 'SUCCESS', NOW() - INTERVAL 1 HOUR),
(1, 'admin', 'GENERATE_PAPER', '生成数学试卷', 'PAPER_GENERATION', 'SUCCESS', NOW() - INTERVAL 50 MINUTE),
(2, 'teacher1', 'LOGIN', '用户登录系统', 'SYSTEM', 'SUCCESS', NOW() - INTERVAL 45 MINUTE),
(2, 'teacher1', 'UPLOAD_TOPICS', '上传英语题目', 'TOPIC_MANAGEMENT', 'SUCCESS', NOW() - INTERVAL 40 MINUTE),
(1, 'admin', 'DUPLICATE_CHECK', '执行试卷查重分析', 'DUPLICATE_CHECK', 'SUCCESS', NOW() - INTERVAL 35 MINUTE),
(3, 'teacher2', 'LOGIN', '用户登录系统', 'SYSTEM', 'SUCCESS', NOW() - INTERVAL 30 MINUTE),
(2, 'teacher1', 'CHAT_GENERATE', '使用AI生成物理题目', 'CHAT_SYSTEM', 'SUCCESS', NOW() - INTERVAL 25 MINUTE),
(1, 'admin', 'VIEW_TOPICS', '查看题库内容', 'TOPIC_MANAGEMENT', 'SUCCESS', NOW() - INTERVAL 20 MINUTE),
(3, 'teacher2', 'GENERATE_PAPER', '生成化学试卷', 'PAPER_GENERATION', 'SUCCESS', NOW() - INTERVAL 15 MINUTE),
(2, 'teacher1', 'EXPORT_PAPER', '导出试卷PDF', 'PAPER_GENERATION', 'SUCCESS', NOW() - INTERVAL 10 MINUTE),
(1, 'admin', 'VIEW_PROFILE', '查看个人信息', 'USER_MANAGEMENT', 'SUCCESS', NOW() - INTERVAL 5 MINUTE),
(3, 'teacher2', 'SEARCH', '搜索题目', 'TOPIC_MANAGEMENT', 'SUCCESS', NOW() - INTERVAL 2 MINUTE);

-- 创建定期清理过期日志的事件（可选）
-- DELIMITER $$
-- CREATE EVENT IF NOT EXISTS cleanup_activity_logs
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
-- BEGIN
--     DELETE FROM user_activity_logs WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
-- END$$
-- DELIMITER ;
