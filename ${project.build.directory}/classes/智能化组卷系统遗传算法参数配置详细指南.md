# 智能化组卷系统遗传算法参数配置详细指南

------

## **1. 概述**

本文档详细说明智能化组卷系统中遗传算法（Genetic Algorithm, GA）的核心参数及其配置方法。系统采用BitSet编码、多层初始化策略、类型保持遗传操作、自适应变异机制等先进技术，为后台管理人员提供精准的参数控制能力。

**系统特点**：

- 支持复杂约束条件下的多目标优化
- 实时参数调整与效果监控
- 智能修复算子确保解的可行性
- 自适应机制提高算法收敛性能

------

## **2. 参数配置方法**

### **2.1 配置文件路径**

参数通过后端配置文件（`application.yml`）管理，完整配置结构如下：

```yaml
# ========================
# 遗传算法参数配置
# ========================
algorithm:
  genetic:
    # 基础进化参数
    population-size: 100
    max-generations: 50
    min-generations: 30
    crossover-rate: 0.8
    mutation-rate: 0.1
    tournament-size: 5
    early-terminate-threshold: 0.97
    global-timeout-seconds: 2
    
    # 适应度权重配置
    fitness-weights:
      score: 0.4                      # 分数匹配权重
      quality: 0.2                    # 题目质量权重
      difficulty-distribution: 0.15   # 难度分布权重
      cognitive-distribution: 0.15    # 认知层次权重
      kp-coverage: 0.05              # 知识点覆盖权重
      topic-type-diversity: 0.05     # 题型多样性权重
    
    # 修复算子配置
    repair:
      enabled: true                   # 是否启用修复算子
      max-steps: 3                   # 最大修复步数
      greedy-threshold: 0.1          # 贪心修复阈值
    
    # 自适应变异配置
    adaptive-mutation:
      enabled: true                   # 是否启用自适应变异
      max-rate: 0.3                  # 最大变异率
      min-rate: 0.05                 # 最小变异率
      stagnation-threshold: 5         # 停滞检测阈值
  
  # 多样性控制配置
  diversity:
    similarity-threshold: 0.85        # 相似度阈值
    knowledge-point-level:
      enabled: true                   # 知识点级别多样性控制
      min-reuse-interval-days: 1      # 最小重用间隔（天）
      max-topics-per-knowledge-point: 50  # 每个知识点最大题目数
      priority-weight-usage: 10.0     # 使用次数权重
      priority-weight-time: 1.0       # 时间权重
  
  # 质量评估配置
  quality:
    error-rate-weight: 0.6           # 错误率权重
    freshness-weight: 0.4            # 新鲜度权重
  
  # 曝光控制配置
  exposure-control:
    default-min-reuse-interval-days: 1  # 默认最小重用间隔
```

### **2.2 配置生效机制**

- **热更新支持**：部分参数支持运行时动态调整，无需重启服务
- **配置验证**：系统启动时自动验证参数合法性
- **回滚机制**：配置错误时自动回滚到上一个有效配置

### **2.3 参数优先级**

1. **运行时动态参数** > **配置文件参数** > **系统默认参数**
2. **用户自定义预设** > **系统预设方案**
3. **场景特定参数** > **全局通用参数**

------

## **3. 核心参数详解**

### **3.1 进化控制参数**

#### **3.1.1 种群配置参数**

| 参数名            | 默认值 | 取值范围 | 含义                       | 性能影响         | 调整建议                                                     |
| ----------------- | ------ | -------- | -------------------------- | ---------------- | ------------------------------------------------------------ |
| `population-size` | 100    | 20-500   | 每代染色体（试卷方案）数量 | 线性影响计算时间 | **小题库(<1000题)**：50-100 **中等题库(1000-5000题)**：100-200 **大题库(>5000题)**：200-500 |
| `max-generations` | 50     | 10-500   | 最大进化代数               | 线性影响总耗时   | **简单约束**：20-50代 **复杂约束**：50-200代 **极复杂约束**：200-500代 |
| `min-generations` | 30     | 5-100    | 最小进化代数（强制迭代）   | 影响最小运行时间 | 通常设为max-generations的60-80%                              |

**调优策略**：

```java
# 快速组卷场景（2秒内完成）
population-size: 50
max-generations: 30
min-generations: 20

# 高质量组卷场景（允许10-30秒）
population-size: 200
max-generations: 150
min-generations: 100

# 平衡场景（默认配置）
population-size: 100
max-generations: 50
min-generations: 30
```

#### **3.1.2 遗传操作参数**

| 参数名            | 默认值 | 取值范围 | 含义                 | 影响分析               | 调整指导                                                     |
| ----------------- | ------ | -------- | -------------------- | ---------------------- | ------------------------------------------------------------ |
| `crossover-rate`  | 0.8    | 0.6-0.95 | 交叉操作概率         | 影响搜索能力与解稳定性 | **探索阶段**：0.8-0.9 **收敛阶段**：0.6-0.8 **复杂约束**：0.85-0.95 |
| `mutation-rate`   | 0.1    | 0.01-0.3 | 基础变异概率         | 影响多样性与收敛速度   | **大搜索空间**：0.1-0.2 **小搜索空间**：0.05-0.1 **复杂约束**：0.15-0.3 |
| `tournament-size` | 5      | 2-10     | 锦标赛选择竞争个体数 | 影响选择压力           | **小种群(<50)**：2-3 **中等种群(50-200)**：3-5 **大种群(>200)**：5-10 |

**选择压力计算公式**：

```tex
选择压力 = (tournament-size - 1) / population-size
推荐范围：0.02 - 0.08
```

#### **3.1.3 终止条件参数**

| 参数名                      | 默认值 | 取值范围  | 含义               | 业务场景       | 配置建议                                                     |
| --------------------------- | ------ | --------- | ------------------ | -------------- | ------------------------------------------------------------ |
| `early-terminate-threshold` | 0.97   | 0.85-0.99 | 早期终止适应度阈值 | 质量与速度平衡 | **快速出卷**：0.90-0.95 **高质量要求**：0.95-0.99 **实时组卷**：0.90-0.93 |
| `global-timeout-seconds`    | 2      | 1-60      | 全局超时时间（秒） | 响应时间控制   | **实时组卷**：1-3秒 **批量组卷**：5-15秒 **高质量组卷**：15-30秒 |

------

### **3.2 适应度权重配置**

适应度函数采用加权多目标优化，权重分配直接影响组卷质量。**所有权重之和必须等于1.0**。

#### **3.2.1 核心权重参数**

| 权重参数                  | 默认值 | 推荐范围  | 目标描述                                 | 业务重要性 | 调整场景                                                |
| ------------------------- | ------ | --------- | ---------------------------------------- | ---------- | ------------------------------------------------------- |
| `score`                   | 0.4    | 0.3-0.6   | 总分匹配度（实际总分与目标总分接近程度） | ⭐⭐⭐⭐⭐      | **严格分数要求**：0.5-0.6 **灵活分数要求**：0.3-0.4     |
| `quality`                 | 0.2    | 0.1-0.4   | 题目质量（基于使用历史、错误率等）       | ⭐⭐⭐⭐       | **重视新颖性**：0.25-0.4 **允许重复使用**：0.1-0.15     |
| `difficulty-distribution` | 0.15   | 0.1-0.3   | 难度分布匹配度                           | ⭐⭐⭐        | **严格难度要求**：0.2-0.3 **灵活难度要求**：0.1-0.15    |
| `cognitive-distribution`  | 0.15   | 0.05-0.3  | 认知层次分布匹配度                       | ⭐⭐⭐        | **重视认知层次**：0.2-0.3 **不关心认知层次**：0.05-0.1  |
| `kp-coverage`             | 0.05   | 0.02-0.2  | 知识点覆盖完整性                         | ⭐⭐         | **必须全覆盖**：0.1-0.2 **允许部分覆盖**：0.02-0.05     |
| `topic-type-diversity`    | 0.05   | 0.02-0.15 | 题型分布均衡性                           | ⭐⭐         | **重视题型平衡**：0.08-0.15 **允许题型倾斜**：0.02-0.05 |

#### **3.2.2 权重配置方案**

**方案1：严格分数优先**

```yaml
fitness-weights:
  score: 0.6                    # 分数最重要
  difficulty-distribution: 0.2  # 难度次重要
  quality: 0.1                 # 质量一般
  cognitive-distribution: 0.05  # 认知层次较低
  kp-coverage: 0.03            # 知识点覆盖较低
  topic-type-diversity: 0.02   # 题型多样性最低
```

**适用场景**：正式考试、标准化测试

**方案2：质量优先**

```yaml
fitness-weights:
  quality: 0.35               # 质量最重要
  score: 0.35                 # 分数同样重要
  difficulty-distribution: 0.15
  cognitive-distribution: 0.1
  kp-coverage: 0.03
  topic-type-diversity: 0.02
```

**适用场景**：高质量练习、精品试卷

**方案3：教学质量优先**

```yaml
fitness-weights:
  cognitive-distribution: 0.3  # 认知层次最重要
  difficulty-distribution: 0.25 # 难度分布次重要
  score: 0.25                  # 分数适中
  quality: 0.1                 # 质量一般
  kp-coverage: 0.07           # 知识点覆盖较高
  topic-type-diversity: 0.03  # 题型多样性较低
```

**适用场景**：教学测试、能力评估

**方案4：平衡型（默认）**

```yaml
fitness-weights:
  score: 0.4                  # 默认配置
  quality: 0.2
  difficulty-distribution: 0.15
  cognitive-distribution: 0.15
  kp-coverage: 0.05
  topic-type-diversity: 0.05
```

**适用场景**：通用组卷、日常测试

------

### **3.3 修复算子配置**

修复算子用于纠正遗传操作产生的不可行解，确保所有染色体都满足约束条件。

#### **3.3.1 修复算子参数**

| 参数名                    | 默认值 | 取值范围   | 含义                   | 性能影响       | 配置建议                                                     |
| ------------------------- | ------ | ---------- | ---------------------- | -------------- | ------------------------------------------------------------ |
| `repair.enabled`          | true   | true/false | 是否启用修复算子       | 影响解的可行性 | **复杂约束**：必须启用 **简单约束**：可关闭提升性能          |
| `repair.max-steps`        | 3      | 1-10       | 每个染色体最大修复步数 | 影响修复耗时   | **简单修复**：1-3步 **复杂修复**：3-5步 **极复杂约束**：5-10步 |
| `repair.greedy-threshold` | 0.1    | 0.01-0.5   | 贪心修复触发阈值       | 影响修复策略   | **保守修复**：0.01-0.1 **积极修复**：0.15-0.5                |

#### **3.3.2 修复策略配置**

```yaml
# 保守修复策略（适用于高质量要求）
repair:
  enabled: true
  max-steps: 2
  greedy-threshold: 0.05
  strategy: "conservative"

# 积极修复策略（适用于复杂约束）
repair:
  enabled: true
  max-steps: 5
  greedy-threshold: 0.2
  strategy: "aggressive"

# 平衡修复策略（默认）
repair:
  enabled: true
  max-steps: 3
  greedy-threshold: 0.1
  strategy: "balanced"
```

### **3.4 自适应变异配置**

自适应变异机制根据算法进化状态动态调整变异率，提高收敛性能。

#### **3.4.1 自适应变异参数**

| 参数名                                   | 默认值 | 取值范围   | 含义                 | 算法影响     | 调整建议                                            |
| ---------------------------------------- | ------ | ---------- | -------------------- | ------------ | --------------------------------------------------- |
| `adaptive-mutation.enabled`              | true   | true/false | 是否启用自适应变异   | 影响收敛性能 | **复杂搜索空间**：建议启用 **简单搜索空间**：可关闭 |
| `adaptive-mutation.max-rate`             | 0.3    | 0.15-0.6   | 最大变异率           | 影响搜索范围 | **保守策略**：0.15-0.3 **激进策略**：0.3-0.6        |
| `adaptive-mutation.min-rate`             | 0.05   | 0.01-0.15  | 最小变异率           | 影响精细搜索 | **精细搜索**：0.01-0.05 **粗糙搜索**：0.05-0.15     |
| `adaptive-mutation.stagnation-threshold` | 5      | 2-20       | 停滞检测阈值（代数） | 影响响应速度 | **快速响应**：2-5代 **稳定响应**：8-20代            |

#### **3.4.2 变异率计算公式**

```java
// 基础变异率（随进化进程线性递减）
baseRate = minRate + (maxRate - minRate) * (1.0 - generation / maxGenerations)

// 停滞惩罚（连续无改进时提高变异率）
if (generationsWithoutImprovement >= stagnationThreshold) {
    stagnationMultiplier = 1.0 + (generationsWithoutImprovement - stagnationThreshold) * 0.1
    finalRate = Math.min(maxRate, baseRate * stagnationMultiplier)
} else {
    finalRate = baseRate
}
```

### **3.5 多样性控制配置**

多样性控制确保题目选择的丰富性，避免重复使用和同质化问题。

#### **3.5.1 多样性参数**

| 参数名                           | 默认值 | 取值范围 | 含义                 | 业务影响       | 配置建议                                                     |
| -------------------------------- | ------ | -------- | -------------------- | -------------- | ------------------------------------------------------------ |
| `diversity.similarity-threshold` | 0.85   | 0.5-0.98 | 题目相似度容忍阈值   | 影响题目多样性 | **严格多样性**：0.5-0.8 **适中多样性**：0.8-0.9 **宽松多样性**：0.9-0.98 |
| `min-reuse-interval-days`        | 1      | 0-90     | 题目重复使用最小间隔 | 影响题目新鲜度 | **频繁组卷**：0-3天 **定期组卷**：7-14天 **偶尔组卷**：30天以上 |
| `max-topics-per-knowledge-point` | 50     | 5-200    | 单个知识点最大题目数 | 影响知识点平衡 | **小题库**：5-30题 **中等题库**：30-100题 **大题库**：100-200题 |

#### **3.5.2 **

#### **知识点级别多样性控制**

```java
diversity:
  knowledge-point-level:
    enabled: true                    # 启用知识点级别控制
    min-reuse-interval-days: 1       # 知识点内部重用间隔
    max-topics-per-knowledge-point: 50  # 每个知识点最大题目
    priority-weight-usage: 10.0      # 使用次数权重（值越大越倾向选择少用题目）
    priority-weight-time: 1.0        # 时间权重（值越大越倾向选择久未使用题目）
    diversity-strategy: "balanced"   # 多样性策略：strict/balanced/loose
```

**多样性策略说明**：

- **strict**：严格多样性，优先选择从未使用或很少使用的题目
- **balanced**：平衡策略，在质量和多样性之间取平衡
- **loose**：宽松策略，允许适度重复使用高质量题目

------

### **3.6 质量评估配置**

题目质量评估基于历史数据和使用反馈，影响题目选择优先级。

#### **3.6.1 质量评估参数**

| 参数名                      | 默认值 | 取值范围 | 含义                     | 计算影响         | 配置建议                                                     |
| --------------------------- | ------ | -------- | ------------------------ | ---------------- | ------------------------------------------------------------ |
| `quality.error-rate-weight` | 0.6    | 0.3-0.8  | 错误率在质量评估中的权重 | 影响题目质量排序 | **重视准确性**：0.6-0.8 **平衡考虑**：0.4-0.6 **重视新颖性**：0.3-0.5 |
| `quality.freshness-weight`  | 0.4    | 0.2-0.7  | 新鲜度在质量评估中的权重 | 影响题目选择倾向 | **重视新题目**：0.5-0.7 **平衡考虑**：0.3-0.5 **重视经典题**：0.2-0.4 |

#### **3.6.2 质量计算公式**

```java
// 题目质量计算
qualityScore = (1 - errorRate) * errorRateWeight + 
               freshnessScore * freshnessWeight

// 新鲜度计算（基于最后使用时间）
daysSinceLastUse = (currentDate - lastUsedDate) / (24 * 60 * 60 * 1000)
freshnessScore = Math.min(1.0, daysSinceLastUse / maxFreshnessDay)

// 使用次数惩罚
usagePenalty = Math.max(0.1, 1.0 - (usageCount * 0.1))
finalQuality = qualityScore * usagePenalty
```

## **4. 高级配置与优化**

### **4.1 性能调优配置**

#### **4.1.1 并行计算配置**

```java
algorithm:
  performance:
    parallel-evaluation: true        # 启用并行适应度评估
    thread-pool-size: 8             # 线程池大小（建议为CPU核心数）
    batch-size: 20                  # 批处理大小
    memory-optimization: true       # 启用内存优化
```

**线程池大小建议**：

- **CPU核心数 ≤ 4**：使用默认值（核心数）
- **CPU核心数 > 4**：设置为核心数的80%
- **内存受限环境**：减少线程数避免内存溢出

#### **4.1.2 内存优化配置**

```yaml
algorithm:
  memory:
    gc-frequency: 50               # 每50代执行一次垃圾回收
    cache-size: 1000              # 缓存大小
    use-weak-references: true      # 使用弱引用减少内存占用
```

**JVM参数建议**：

```yaml
# 小规模组卷（<1000题）
-Xms512m -Xmx1g -XX:+UseG1GC

# 中等规模组卷（1000-5000题）
-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# 大规模组卷（>5000题）
-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=100
```

### **4.2 监控与诊断配置**

#### **4.2.1 算法监控参数**

```yam
algorithm:
  monitoring:
    enabled: true                  # 启用监控
    log-level: "INFO"             # 日志级别：DEBUG/INFO/WARN/ERROR
    metrics-collection: true       # 收集性能指标
    fitness-tracking: true        # 跟踪适应度变化
    convergence-analysis: true    # 收敛性分析
    
    # 详细监控配置
    detailed-logging:
      generation-interval: 10      # 每10代输出详细日志
      population-analysis: true    # 种群分析
      diversity-metrics: true     # 多样性指标
      repair-statistics: true     # 修复算子统计
```

#### **4.2.2 性能指标收集**

```yaml
algorithm:
  metrics:
    collection-interval: 1000     # 指标收集间隔（毫秒）
    retention-period: 7           # 指标保留天数
    
    # 收集的指标类型
    collected-metrics:
      - "execution-time"          # 执行时间
      - "fitness-evolution"       # 适应度演化
      - "convergence-rate"        # 收敛速度
      - "repair-efficiency"       # 修复效率
      - "memory-usage"           # 内存使用
      - "thread-utilization"     # 线程利用率
```

## **5. 场景化配置方案**

### **5.1 实时组卷场景**

**特点**：要求1-3秒内完成组卷，对质量要求适中

```yaml
algorithm:
  genetic:
    population-size: 50
    max-generations: 30
    min-generations: 15
    crossover-rate: 0.85
    mutation-rate: 0.12
    tournament-size: 3
    early-terminate-threshold: 0.90
    global-timeout-seconds: 2
    
    fitness-weights:
      score: 0.5
      quality: 0.15
      difficulty-distribution: 0.2
      cognitive-distribution: 0.1
      kp-coverage: 0.03
      topic-type-diversity: 0.02
    
    repair:
      enabled: true
      max-steps: 2
      greedy-threshold: 0.15
    
    adaptive-mutation:
      enabled: true
      max-rate: 0.25
      min-rate: 0.08
      stagnation-threshold: 3
```

### **5.2 高质量组卷场景**

**特点**：允许10-30秒完成，追求最高质量

```yaml
algorithm:
  genetic:
    population-size: 200
    max-generations: 150
    min-generations: 80
    crossover-rate: 0.8
    mutation-rate: 0.08
    tournament-size: 7
    early-terminate-threshold: 0.98
    global-timeout-seconds: 25
    
    fitness-weights:
      score: 0.35
      quality: 0.3
      difficulty-distribution: 0.2
      cognitive-distribution: 0.1
      kp-coverage: 0.03
      topic-type-diversity: 0.02
    
    repair:
      enabled: true
      max-steps: 5
      greedy-threshold: 0.05
    
    adaptive-mutation:
      enabled: true
      max-rate: 0.2
      min-rate: 0.03
      stagnation-threshold: 8
```

### **5.3 批量组卷场景**

**特点**：一次生成多份试卷，注重效率和多样性

```yaml
algorithm:
  genetic:
    population-size: 80
    max-generations: 60
    min-generations: 30
    crossover-rate: 0.82
    mutation-rate: 0.15
    tournament-size: 4
    early-terminate-threshold: 0.94
    global-timeout-seconds: 8
    
    fitness-weights:
      score: 0.4
      quality: 0.25
      difficulty-distribution: 0.15
      cognitive-distribution: 0.12
      kp-coverage: 0.05
      topic-type-diversity: 0.03
    
    diversity:
      similarity-threshold: 0.75    # 更严格的多样性要求
      min-reuse-interval-days: 0    # 允许在不同试卷中重复
      
    repair:
      enabled: true
      max-steps: 3
      greedy-threshold: 0.1
```

### **5.4 教学评估场景**

**特点**：重视认知层次和知识点覆盖

```yam
algorithm:
  genetic:
    population-size: 120
    max-generations: 80
    min-generations: 40
    crossover-rate: 0.78
    mutation-rate: 0.1
    tournament-size: 5
    early-terminate-threshold: 0.95
    global-timeout-seconds: 15
    
    fitness-weights:
      score: 0.25
      quality: 0.2
      difficulty-distribution: 0.25
      cognitive-distribution: 0.2
      kp-coverage: 0.08
      topic-type-diversity: 0.02
    
    repair:
      enabled: true
      max-steps: 4
      greedy-threshold: 0.08
```

## **6. 参数调优策略与最佳实践**

### **6.1 调优流程**

#### **6.1.1 基础调优步骤**

1. **确定主要目标**

```yaml
# 分数精确性优先
fitness-weights:
  score: 0.6
  # 其他权重相应调整

# 题目质量优先
fitness-weights:
  quality: 0.4
  score: 0.4
  # 其他权重相应调整
```

2.**根据题库规模调整**

```yaml
# 小题库（<1000题）
population-size: 50
max-generations: 30

# 大题库（>5000题）
population-size: 200
max-generations: 100
```

3.**根据约束复杂度调整**

```yaml
# 简单约束
mutation-rate: 0.05
repair:
  max-steps: 1

# 复杂约束
mutation-rate: 0.15
repair:
  max-steps: 5
```

#### **6.1.2 性能优化调优**

**提高收敛速度**：

```yaml
# 增加选择压力
tournament-size: 7
crossover-rate: 0.9

# 启用早期终止
early-terminate-threshold: 0.95

# 优化自适应变异
adaptive-mutation:
  stagnation-threshold: 3
  max-rate: 0.25
```

**提高解的质量**：

```yaml
# 增加搜索时间
max-generations: 200
global-timeout-seconds: 20

# 增加种群多样性
population-size: 200
mutation-rate: 0.15

# 严格修复
repair:
  max-steps: 5
  greedy-threshold: 0.05
```

### **6.2 参数验证与测试**

#### **6.2.1 A/B测试配置**

```yaml
algorithm:
  testing:
    ab-testing:
      enabled: true
      test-groups:
        - name: "control"
          config: "default"
          traffic-percentage: 50
        - name: "experimental"
          config: "optimized"
          traffic-percentage: 50
      
      metrics:
        - "generation-time"
        - "solution-quality"
        - "constraint-satisfaction"
```

#### **6.2.2 参数敏感性分析**

```yaml
algorithm:
  sensitivity-analysis:
    enabled: true
    parameters:
      - name: "population-size"
        range: [50, 100, 150, 200]
      - name: "mutation-rate"
        range: [0.05, 0.1, 0.15, 0.2]
    
    test-cases: 100
    evaluation-metrics:
      - "convergence-speed"
      - "solution-quality"
      - "stability"
```

## **7. 故障排除与诊断**

### **7.1 常见问题诊断**

#### **7.1.1 性能问题**

| 问题现象      | 可能原因           | 诊断方法               | 解决方案                                     |
| ------------- | ------------------ | ---------------------- | -------------------------------------------- |
| 组卷时间过长  | 种群过大或代数过多 | 检查日志中的代数和耗时 | 逐步降低`population-size`和`max-generations` |
| 内存溢出      | 种群过大或题库过大 | 监控内存使用情况       | 减少种群大小，启用内存优化                   |
| CPU使用率过高 | 并行度设置不当     | 检查线程池使用情况     | 调整`thread-pool-size`                       |

#### **7.1.2 质量问题**

| 问题现象       | 可能原因                   | 诊断方法         | 解决方案                    |
| -------------- | -------------------------- | ---------------- | --------------------------- |
| 试卷总分偏差大 | 分数权重过低               | 检查适应度分解   | 提高`fitness-weights.score` |
| 难度分布不匹配 | 难度权重过低或题库分布不均 | 分析题库难度分布 | 提高难度权重或调整题库      |
| 题目重复率高   | 多样性控制不足             | 检查多样性指标   | 降低`similarity-threshold`  |
| 算法早熟收敛   | 变异率过低或种群过小       | 观察适应度曲线   | 提高变异率或增加种群        |

#### **7.1.3 约束满足问题**

| 问题现象         | 可能原因           | 诊断方法       | 解决方案               |
| ---------------- | ------------------ | -------------- | ---------------------- |
| 题型数量不匹配   | 修复算子失效       | 检查修复统计   | 增加修复步数或调整策略 |
| 知识点覆盖不足   | 覆盖权重过低       | 分析知识点分布 | 提高覆盖权重           |
| 认知层次分布异常 | 数据缺失或权重设置 | 检查题目元数据 | 补充数据或调整权重     |

### **7.2 监控指标解读**

#### **7.2.1 关键性能指标**

```tex
# 示例监控输出
Generation 50 | Best Fitness: 0.92 | Avg Fitness: 0.76 | Diversity: 0.65
Repair Success Rate: 95% | Avg Repair Steps: 2.3
Convergence Rate: 0.85 | Stagnation Count: 2
Memory Usage: 1.2GB | Thread Utilization: 85%
```

**指标解读**：

- **Best Fitness > 0.9**：解质量良好
- **Diversity > 0.6**：种群多样性充足
- **Repair Success Rate > 90%**：修复算子工作正常
- **Convergence Rate > 0.8**：收敛性能良好

#### **7.2.2 日志分析**

**正常运行日志**：

```tex
INFO  - Generation 10: Best=0.85, Avg=0.72, Improved=true
INFO  - Repair applied to 15 chromosomes, success rate: 93%
INFO  - Adaptive mutation rate: 0.12 (base: 0.10, stagnation: 0)
```

**异常情况日志**：

```tex
WARN  - Generation 25: No improvement for 8 consecutive generations
WARN  - Repair failure rate exceeding 20%, consider adjusting parameters
ERROR - Memory usage approaching limit, reducing population size
```

## **8. 附录**

### **8.1 完整配置示例**

#### **8.1.1 生产环境配置**

```yaml
# 生产环境推荐配置
algorithm:
  genetic:
    # 基础参数
    population-size: 100
    max-generations: 50
    min-generations: 30
    crossover-rate: 0.8
    mutation-rate: 0.1
    tournament-size: 5
    early-terminate-threshold: 0.97
    global-timeout-seconds: 5
    
    # 适应度权重
    fitness-weights:
      score: 0.4
      quality: 0.2
      difficulty-distribution: 0.15
      cognitive-distribution: 0.15
      kp-coverage: 0.05
      topic-type-diversity: 0.05
    
    # 修复算子
    repair:
      enabled: true
      max-steps: 3
      greedy-threshold: 0.1
      strategy: "balanced"
    
    # 自适应变异
    adaptive-mutation:
      enabled: true
      max-rate: 0.3
      min-rate: 0.05
      stagnation-threshold: 5
      decay-rate: 0.95
  
  # 多样性控制
  diversity:
    similarity-threshold: 0.85
    knowledge-point-level:
      enabled: true
      min-reuse-interval-days: 1
      max-topics-per-knowledge-point: 50
      priority-weight-usage: 10.0
      priority-weight-time: 1.0
      diversity-strategy: "balanced"
  
  # 质量评估
  quality:
    error-rate-weight: 0.6
    freshness-weight: 0.4
    quality-threshold: 0.7
    
  # 性能优化
  performance:
    parallel-evaluation: true
    thread-pool-size: 8
    batch-size: 20
    memory-optimization: true
    gc-frequency: 50
    
  # 监控配置
  monitoring:
    enabled: true
    log-level: "INFO"
    metrics-collection: true
    fitness-tracking: true
    convergence-analysis: true
    
    detailed-logging:
      generation-interval: 10
      population-analysis: false
      diversity-metrics: true
      repair-statistics: true
```

#### **8.1.2 开发环境配置**

```yaml
# 开发环境调试配置
algorithm:
  genetic:
    # 较小的参数便于快速测试
    population-size: 30
    max-generations: 20
    min-generations: 10
    crossover-rate: 0.8
    mutation-rate: 0.15
    tournament-size: 3
    early-terminate-threshold: 0.90
    global-timeout-seconds: 10
    
    fitness-weights:
      score: 0.5
      quality: 0.2
      difficulty-distribution: 0.15
      cognitive-distribution: 0.1
      kp-coverage: 0.03
      topic-type-diversity: 0.02
    
    repair:
      enabled: true
      max-steps: 2
      greedy-threshold: 0.2
    
    adaptive-mutation:
      enabled: true
      max-rate: 0.4
      min-rate: 0.1
      stagnation-threshold: 3
  
  # 详细监控用于调试
  monitoring:
    enabled: true
    log-level: "DEBUG"
    metrics-collection: true
    fitness-tracking: true
    convergence-analysis: true
    
    detailed-logging:
      generation-interval: 1
      population-analysis: true
      diversity-metrics: true
      repair-statistics: true
      chromosome-details: true
```

### **8.2 参数配置检查清单**

#### **8.2.1 配置前检查**

- 题库规模评估
  - 总题目数量：_____
  - 各题型分布：_____
  - 知识点覆盖：_____
  - 难度分布：_____
- 业务需求确认
  - 组卷时间要求：_____ 秒
  - 质量要求级别：高/中/低
  - 主要优化目标：_____
  - 约束条件复杂度：_____
- 系统资源评估
  - CPU核心数：_____
  - 可用内存：_____ GB
  - 并发用户数：_____
  - 预期QPS：_____

#### **8.2.2 配置后验证**

- 参数合法性检查
  - 权重总和 = 1.0
  - min-generations ≤ max-generations
  - 0 < mutation-rate < 1
  - 0 < crossover-rate < 1
  - tournament-size < population-size
- 性能基准测试
  - 单次组卷耗时：_____ 秒
  - 内存峰值使用：_____ MB
  - CPU平均使用率：_____%
  - 并发处理能力：_____ QPS
- 质量验证测试
  - 分数匹配准确率：_____%
  - 约束满足率：_____%
  - 题目多样性指标：_____
  - 用户满意度：_____

### **8.3 故障排除快速参考**

#### **8.3.1 性能问题快速修复**

```yaml
# 问题：组卷时间过长
# 快速修复方案
algorithm:
  genetic:
    population-size: 50        # 减少种群大小
    max-generations: 30        # 减少最大代数
    global-timeout-seconds: 3  # 设置更严格的超时
    early-terminate-threshold: 0.90  # 降低终止阈值

# 问题：内存使用过高
# 快速修复方案
algorithm:
  performance:
    memory-optimization: true  # 启用内存优化
    gc-frequency: 20          # 增加GC频率
  genetic:
    population-size: 50       # 减少种群大小
```

#### **8.3.2 质量问题快速修复**

```yam
# 问题：分数匹配不准确
# 快速修复方案
algorithm:
  genetic:
    fitness-weights:
      score: 0.6              # 提高分数权重
      quality: 0.15
      difficulty-distribution: 0.1
      cognitive-distribution: 0.1
      kp-coverage: 0.03
      topic-type-diversity: 0.02

# 问题：题目重复率高
# 快速修复方案
algorithm:
  diversity:
    similarity-threshold: 0.75  # 降低相似度阈值
    min-reuse-interval-days: 3  # 增加重用间隔
  genetic:
    fitness-weights:
      quality: 0.3             # 提高质量权重
```

