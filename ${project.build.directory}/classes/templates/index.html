<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>zi EDU - 智能教育平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/static/js/avatar-fix.js"></script>
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/index.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .dashboard-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .activity-log-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s ease;
        }

        .activity-item:hover {
            background-color: #f8f9fa;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2em;
            color: white;
        }

        .activity-content {
            flex: 1;
        }

        .activity-time {
            font-size: 0.9em;
            color: #666;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
        <div class="container">
            <a class="navbar-brand fw-bold text-white" href="/">
                <i class="fas fa-graduation-cap me-2"></i>Maizi EDU
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/main/chat">
                            <i class="fas fa-robot me-1"></i>智能出题
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/topics/upload-topics">
                            <i class="fas fa-upload me-1"></i>题目上传
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/paper/generate">
                            <i class="fas fa-file-alt me-1"></i>智能组卷
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/papers/duplicate-check">
                            <i class="fas fa-search me-1"></i>查重分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/topics/bank">
                            <i class="fas fa-database me-1"></i>题库管理
                        </a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="/static/images/default-avatar.png" alt="avatar" class="rounded-circle me-2" width="32" height="32" id="userAvatar"
                                 onerror="handleAvatarError(this)">
                            <span id="username" class="text-white">加载中...</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/user/profile"><i class="fas fa-user me-2"></i>个人信息</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="dashboard-container">
        <!-- 欢迎卡片 -->
        <div class="welcome-card">
            <h1 class="display-4 fw-bold mb-3">
                <i class="fas fa-graduation-cap text-primary me-3"></i>
                欢迎使用 Maizi EDU
            </h1>
            <p class="lead mb-4">智能教育辅助系统，让教学更轻松、更高效</p>
            <div class="d-flex justify-content-center gap-3">
                <button class="btn btn-primary btn-lg" onclick="window.location.href='/main/chat'">
                    <i class="fas fa-robot me-2"></i>开始智能出题
                </button>
                <button class="btn btn-outline-primary btn-lg" onclick="window.location.href='/paper/generate'">
                    <i class="fas fa-file-alt me-2"></i>智能组卷
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-section" id="statsSection">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">-</div>
                <div class="text-muted">活跃用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalActivities">-</div>
                <div class="text-muted">今日活动</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPapers">-</div>
                <div class="text-muted">生成试卷</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTopics">-</div>
                <div class="text-muted">题库题目</div>
            </div>
        </div>

        <!-- 功能卡片 -->
        <div class="feature-cards">
            <div class="feature-card" onclick="navigateToFeature('/main/chat')">
                <div class="feature-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3 class="h4 fw-bold mb-3">智能出题</h3>
                <p class="text-muted">基于AI技术，快速生成高质量试题，支持多种题型和难度级别</p>
                <div class="mt-3">
                    <span class="badge bg-primary">AI驱动</span>
                    <span class="badge bg-success">高效率</span>
                </div>
            </div>

            <div class="feature-card" onclick="navigateToFeature('/topics/upload-topics')">
                <div class="feature-icon">
                    <i class="fas fa-upload"></i>
                </div>
                <h3 class="h4 fw-bold mb-3">题目上传</h3>
                <p class="text-muted">支持多种格式试卷上传和智能识别，快速构建题库</p>
                <div class="mt-3">
                    <span class="badge bg-info">多格式</span>
                    <span class="badge bg-warning">智能识别</span>
                </div>
            </div>

            <div class="feature-card" onclick="navigateToFeature('/paper/generate')">
                <div class="feature-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="h4 fw-bold mb-3">智能组卷</h3>
                <p class="text-muted">根据教学目标和难度要求自动生成试卷，支持多种组卷策略</p>
                <div class="mt-3">
                    <span class="badge bg-success">自动化</span>
                    <span class="badge bg-primary">个性化</span>
                </div>
            </div>

            <div class="feature-card" onclick="navigateToFeature('/papers/duplicate-check')">
                <div class="feature-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="h4 fw-bold mb-3">查重分析</h3>
                <p class="text-muted">快速检测试题重复度，确保试卷质量和题目唯一性</p>
                <div class="mt-3">
                    <span class="badge bg-danger">精确匹配</span>
                    <span class="badge bg-info">快速分析</span>
                </div>
            </div>

            <div class="feature-card" onclick="navigateToFeature('/topics/bank')">
                <div class="feature-icon">
                    <i class="fas fa-database"></i>
                </div>
                <h3 class="h4 fw-bold mb-3">题库管理</h3>
                <p class="text-muted">强大的题库管理功能，支持题目分类、搜索和批量操作</p>
                <div class="mt-3">
                    <span class="badge bg-secondary">分类管理</span>
                    <span class="badge bg-success">批量操作</span>
                </div>
            </div>

            <div class="feature-card" onclick="navigateToFeature('/user/profile')">
                <div class="feature-icon">
                    <i class="fas fa-user-cog"></i>
                </div>
                <h3 class="h4 fw-bold mb-3">个人中心</h3>
                <p class="text-muted">管理个人信息、查看使用统计和系统设置</p>
                <div class="mt-3">
                    <span class="badge bg-info">个人设置</span>
                    <span class="badge bg-warning">使用统计</span>
                </div>
            </div>
        </div>

        <!-- 活动统计图表 -->
        <div class="row">
            <div class="col-md-8">
                <div class="chart-container">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-line text-primary me-2"></i>系统活动趋势
                    </h4>
                    <canvas id="activityChart" width="400" height="200"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-container">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-pie text-primary me-2"></i>功能使用分布
                    </h4>
                    <canvas id="moduleChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- 用户活动日志 -->
        <div class="activity-log-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-history text-primary me-2"></i>最近活动日志
                </h4>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshActivityLog()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="exportActivityLog()">
                        <i class="fas fa-download me-1"></i>导出
                    </button>
                </div>
            </div>

            <div id="activityLogContainer">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载活动日志...</p>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary btn-sm" onclick="loadActivityPage(0, false)" id="firstPageBtn">
                        <i class="fas fa-angle-double-left me-1"></i>首页
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="loadPreviousPage()" id="prevPageBtn">
                        <i class="fas fa-angle-left me-1"></i>上一页
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadMoreActivities()" id="loadMoreBtn">
                        <i class="fas fa-chevron-down me-1"></i>加载更多
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="loadNextPage()" id="nextPageBtn">
                        下一页<i class="fas fa-angle-right ms-1"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="loadLastPage()" id="lastPageBtn">
                        末页<i class="fas fa-angle-double-right ms-1"></i>
                    </button>
                </div>
                <div class="pagination-info text-muted small">
                    <!-- 分页信息将通过JavaScript动态更新 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示框 -->
    <div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let activityChart = null;
        let moduleChart = null;
        let currentActivityPage = 0;
        const activityPageSize = 10;
        let totalActivityPages = 0;
        let isLoadingActivities = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        // 初始化页面
        function initializePage() {
            loadUserInfo();

            // 先初始化图表，再加载统计数据
            initializeCharts();

            // 延迟加载统计数据，确保图表已初始化
            setTimeout(() => {
                loadStatistics();
            }, 100);

            loadActivityLog();

            // 绑定退出登录事件
            const logoutBtn = document.getElementById('logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });
            }
        }

        // 加载用户信息
        function loadUserInfo() {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('username').textContent = '游客用户';
                return;
            }

            // 尝试多个可能的API路径
            const apiPaths = ['/api/user/info', '/user/info', '/api/users/current'];

            function tryNextApi(index) {
                if (index >= apiPaths.length) {
                    // 所有API都失败，使用默认信息
                    document.getElementById('username').textContent = '当前用户';
                    return;
                }

                fetch(apiPaths[index], {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200 && data.data) {
                        document.getElementById('username').textContent = data.data.username || '当前用户';
                        if (data.data.avatar) {
                            document.getElementById('userAvatar').src = data.data.avatar;
                        }
                    } else if (data.success && data.data) {
                        document.getElementById('username').textContent = data.data.username || '当前用户';
                        if (data.data.avatar) {
                            document.getElementById('userAvatar').src = data.data.avatar;
                        }
                    } else {
                        tryNextApi(index + 1);
                    }
                })
                .catch(error => {
                    console.warn(`API ${apiPaths[index]} 失败:`, error);
                    tryNextApi(index + 1);
                });
            }

            tryNextApi(0);
        }

        // 加载统计数据
        function loadStatistics() {
            // 模拟统计数据，因为后端API可能还未完全配置
            const mockData = {
                activeUsers: [
                    {username: 'admin', count: 25},
                    {username: 'teacher1', count: 18},
                    {username: 'teacher2', count: 12}
                ],
                activityTypes: [
                    {activity_type: 'GENERATE_PAPER', count: 15},
                    {activity_type: 'UPLOAD_TOPICS', count: 8},
                    {activity_type: 'DUPLICATE_CHECK', count: 5},
                    {activity_type: 'CHAT_GENERATE', count: 12}
                ],
                modules: [
                    {module: 'PAPER_GENERATION', count: 20},
                    {module: 'TOPIC_MANAGEMENT', count: 15},
                    {module: 'CHAT_SYSTEM', count: 12},
                    {module: 'DUPLICATE_CHECK', count: 5},
                    {module: 'SYSTEM', count: 8}
                ]
            };

            updateStatistics(mockData);
            updateCharts(mockData);

            // 尝试从真实API获取数据
            fetch('/api/activities/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    updateStatistics(data.data);
                    updateCharts(data.data);
                }
            })
            .catch(error => {
                console.warn('API暂未可用，使用模拟数据:', error);
            });
        }

        // 更新统计数据
        function updateStatistics(data) {
            // 更新活跃用户数
            const activeUsers = data.activeUsers ? data.activeUsers.length : 0;
            animateNumber('totalUsers', activeUsers);

            // 更新今日活动数
            const todayActivities = data.activityTypes ?
                data.activityTypes.reduce((sum, item) => sum + item.count, 0) : 0;
            animateNumber('totalActivities', todayActivities);

            // 更新生成试卷数（从活动类型中获取）
            const paperCount = data.activityTypes ?
                data.activityTypes.find(item => item.activity_type === 'GENERATE_PAPER')?.count || 0 : 0;
            animateNumber('totalPapers', paperCount);

            // 更新题库题目数（模拟数据）
            animateNumber('totalTopics', 1250);
        }

        // 数字动画效果
        function animateNumber(elementId, targetNumber) {
            const element = document.getElementById(elementId);
            const startNumber = 0;
            const duration = 1000;
            const startTime = Date.now();

            function updateNumber() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);

                element.textContent = currentNumber.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }

            updateNumber();
        }

        // 功能导航
        function navigateToFeature(url) {
            // 记录用户活动
            logUserActivity('NAVIGATE', `访问功能：${url}`, 'SYSTEM');

            // 页面跳转
            window.location.href = url;
        }

        // 记录用户活动
        function logUserActivity(activityType, description, module, targetId = null, targetType = null) {
            const params = new URLSearchParams({
                activityType: activityType,
                description: description,
                module: module
            });

            if (targetId) params.append('targetId', targetId);
            if (targetType) params.append('targetType', targetType);

            // 尝试记录活动，如果失败则静默处理
            fetch(`/api/activities/log?${params.toString()}`)
            .then(response => {
                if (response.ok) {
                    console.log('活动记录成功:', description);
                }
            })
            .catch(error => {
                // 静默处理，不影响用户体验
                console.debug('活动记录API暂未可用:', error);
            });
        }

        // 退出登录
        function logout() {
            Swal.fire({
                title: '确认退出',
                text: '您确定要退出登录吗？',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    localStorage.removeItem('token');
                    logUserActivity('LOGOUT', '用户退出登录', 'SYSTEM');

                    Swal.fire('已退出', '您已成功退出登录', 'success').then(() => {
                        window.location.href = '/auth/login';
                    });
                }
            });
        }

        // 初始化图表
        function initializeCharts() {
            // 活动趋势图表
            const activityCtx = document.getElementById('activityChart').getContext('2d');
            activityChart = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '系统活动',
                        data: [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 模块使用分布图表
            const moduleCtx = document.getElementById('moduleChart').getContext('2d');
            moduleChart = new Chart(moduleCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c',
                            '#4facfe',
                            '#00f2fe'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 更新图表数据
        function updateCharts(data) {
            // 检查图表是否已初始化
            if (!activityChart || !moduleChart) {
                console.warn('图表尚未初始化，跳过更新');
                return;
            }

            // 检查数据是否有效
            if (!data) {
                console.warn('图表数据为空，使用默认数据');
                data = {
                    modules: [
                        {module: 'PAPER_GENERATION', count: 20},
                        {module: 'TOPIC_MANAGEMENT', count: 15}
                    ]
                };
            }

            // 更新活动趋势图表（模拟7天数据）
            const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            const activityData = [12, 19, 15, 25, 22, 18, 30]; // 模拟数据

            try {
                activityChart.data.labels = days;
                activityChart.data.datasets[0].data = activityData;
                activityChart.update();
            } catch (error) {
                console.error('更新活动趋势图表失败:', error);
            }

            // 更新模块使用分布图表
            try {
                if (data.modules && data.modules.length > 0) {
                    const moduleLabels = data.modules.map(item => getModuleDisplayName(item.module));
                    const moduleData = data.modules.map(item => item.count);

                    moduleChart.data.labels = moduleLabels;
                    moduleChart.data.datasets[0].data = moduleData;
                    moduleChart.update();
                } else {
                    // 使用默认数据
                    const defaultModules = [
                        {module: 'PAPER_GENERATION', count: 20},
                        {module: 'TOPIC_MANAGEMENT', count: 15},
                        {module: 'CHAT_SYSTEM', count: 12}
                    ];
                    const moduleLabels = defaultModules.map(item => getModuleDisplayName(item.module));
                    const moduleData = defaultModules.map(item => item.count);

                    moduleChart.data.labels = moduleLabels;
                    moduleChart.data.datasets[0].data = moduleData;
                    moduleChart.update();
                }
            } catch (error) {
                console.error('更新模块分布图表失败:', error);
            }
        }

        // 获取模块显示名称
        function getModuleDisplayName(module) {
            const moduleNames = {
                'PAPER_GENERATION': '智能组卷',
                'TOPIC_MANAGEMENT': '题库管理',
                'DUPLICATE_CHECK': '查重分析',
                'USER_MANAGEMENT': '用户管理',
                'CHAT_SYSTEM': '智能出题',
                'SYSTEM': '系统功能'
            };
            return moduleNames[module] || module;
        }

        // 加载活动日志
        function loadActivityLog() {
            loadActivityPage(0, false);
        }

        // 加载指定页的活动日志
        function loadActivityPage(page, append = false) {
            if (isLoadingActivities) {
                return;
            }

            isLoadingActivities = true;

            if (!append) {
                showActivityLogLoading();
            }

            // 尝试从API获取分页数据
            fetch(`/api/activities/page?page=${page}&size=${activityPageSize}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    const pageData = data.data;
                    currentActivityPage = pageData.currentPage;
                    totalActivityPages = pageData.totalPages;

                    if (append) {
                        appendActivityLog(pageData.content);
                    } else {
                        renderActivityLog(pageData.content);
                    }

                    updatePaginationControls(pageData);
                } else {
                    if (!append) {
                        renderMockActivityLog();
                    }
                }
            })
            .catch(error => {
                console.warn('活动日志API暂未可用，使用模拟数据:', error);
                if (!append) {
                    renderMockActivityLog();
                }
            })
            .finally(() => {
                isLoadingActivities = false;
            });
        }

        // 渲染模拟活动日志数据
        function renderMockActivityLog() {
            const mockActivities = [
                {
                    id: 1,
                    username: 'admin',
                    activityType: 'LOGIN',
                    description: '用户登录系统',
                    module: 'SYSTEM',
                    result: 'SUCCESS',
                    createTime: new Date(Date.now() - 5 * 60 * 1000).toISOString()
                },
                {
                    id: 2,
                    username: 'teacher1',
                    activityType: 'GENERATE_PAPER',
                    description: '生成数学试卷',
                    module: 'PAPER_GENERATION',
                    result: 'SUCCESS',
                    createTime: new Date(Date.now() - 15 * 60 * 1000).toISOString()
                },
                {
                    id: 3,
                    username: 'teacher2',
                    activityType: 'UPLOAD_TOPICS',
                    description: '上传英语题目',
                    module: 'TOPIC_MANAGEMENT',
                    result: 'SUCCESS',
                    createTime: new Date(Date.now() - 30 * 60 * 1000).toISOString()
                },
                {
                    id: 4,
                    username: 'admin',
                    activityType: 'DUPLICATE_CHECK',
                    description: '执行试卷查重分析',
                    module: 'DUPLICATE_CHECK',
                    result: 'SUCCESS',
                    createTime: new Date(Date.now() - 45 * 60 * 1000).toISOString()
                },
                {
                    id: 5,
                    username: 'teacher1',
                    activityType: 'CHAT_GENERATE',
                    description: '使用AI生成物理题目',
                    module: 'CHAT_SYSTEM',
                    result: 'SUCCESS',
                    createTime: new Date(Date.now() - 60 * 60 * 1000).toISOString()
                }
            ];

            // 模拟分页数据
            currentActivityPage = 0;
            totalActivityPages = 5;

            renderActivityLog(mockActivities);
            updatePaginationControls({
                currentPage: 0,
                totalPages: 5,
                hasNext: true,
                hasPrevious: false,
                totalElements: 50
            });
        }

        // 渲染活动日志
        function renderActivityLog(activities) {
            const container = document.getElementById('activityLogContainer');

            if (!activities || activities.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox text-muted" style="font-size: 3em;"></i>
                        <p class="mt-3 text-muted">暂无活动记录</p>
                    </div>
                `;
                return;
            }

            let html = '';
            activities.forEach(activity => {
                html += `
                    <div class="activity-item">
                        <div class="activity-icon" style="background-color: ${getActivityColor(activity.activityType)}">
                            <i class="${getActivityIcon(activity.activityType)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="fw-bold">${activity.description}</div>
                            <div class="activity-time">
                                <i class="fas fa-user me-1"></i>${activity.username}
                                <span class="mx-2">•</span>
                                <i class="fas fa-clock me-1"></i>${formatDateTime(activity.createTime)}
                                <span class="mx-2">•</span>
                                <span class="badge bg-secondary">${getModuleDisplayName(activity.module)}</span>
                            </div>
                        </div>
                        <div class="activity-status">
                            <span class="badge ${activity.result === 'SUCCESS' ? 'bg-success' : 'bg-danger'}">
                                ${activity.result === 'SUCCESS' ? '成功' : '失败'}
                            </span>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 获取活动图标
        function getActivityIcon(activityType) {
            const icons = {
                'LOGIN': 'fas fa-sign-in-alt',
                'LOGOUT': 'fas fa-sign-out-alt',
                'GENERATE_PAPER': 'fas fa-file-alt',
                'UPLOAD_TOPICS': 'fas fa-upload',
                'DUPLICATE_CHECK': 'fas fa-search',
                'VIEW_TOPICS': 'fas fa-eye',
                'EDIT_TOPIC': 'fas fa-edit',
                'DELETE_TOPIC': 'fas fa-trash',
                'EXPORT_PAPER': 'fas fa-download',
                'CHAT_GENERATE': 'fas fa-robot',
                'VIEW_PROFILE': 'fas fa-user',
                'UPDATE_PROFILE': 'fas fa-user-edit',
                'SEARCH': 'fas fa-search',
                'FILTER': 'fas fa-filter',
                'DOWNLOAD': 'fas fa-download',
                'NAVIGATE': 'fas fa-mouse-pointer'
            };
            return icons[activityType] || 'fas fa-circle';
        }

        // 获取活动颜色
        function getActivityColor(activityType) {
            const colors = {
                'LOGIN': '#28a745',
                'LOGOUT': '#dc3545',
                'GENERATE_PAPER': '#007bff',
                'UPLOAD_TOPICS': '#17a2b8',
                'DUPLICATE_CHECK': '#ffc107',
                'VIEW_TOPICS': '#6c757d',
                'EDIT_TOPIC': '#fd7e14',
                'DELETE_TOPIC': '#dc3545',
                'EXPORT_PAPER': '#20c997',
                'CHAT_GENERATE': '#6f42c1',
                'VIEW_PROFILE': '#6c757d',
                'UPDATE_PROFILE': '#fd7e14',
                'SEARCH': '#17a2b8',
                'FILTER': '#6c757d',
                'DOWNLOAD': '#20c997',
                'NAVIGATE': '#007bff'
            };
            return colors[activityType] || '#6c757d';
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未知时间';
            const date = new Date(dateTimeStr);
            const now = new Date();
            const diff = now - date;

            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 24小时内
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
            }
        }

        // 显示活动日志错误
        function showActivityLogError(message) {
            const container = document.getElementById('activityLogContainer');
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3em;"></i>
                    <p class="mt-3 text-muted">${message}</p>
                    <button class="btn btn-outline-primary" onclick="loadActivityLog()">
                        <i class="fas fa-sync-alt me-1"></i>重新加载
                    </button>
                </div>
            `;
        }

        // 刷新活动日志
        function refreshActivityLog() {
            currentActivityPage = 0;
            loadActivityLog();
        }

        // 加载更多活动
        function loadMoreActivities() {
            if (currentActivityPage < totalActivityPages - 1) {
                loadActivityPage(currentActivityPage + 1, false);
            } else {
                Swal.fire('提示', '已经是最后一页了', 'info');
            }
        }

        // 显示活动日志加载状态
        function showActivityLogLoading() {
            const container = document.getElementById('activityLogContainer');
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载活动日志...</p>
                </div>
            `;
        }

        // 追加活动日志（用于加载更多）
        function appendActivityLog(activities) {
            const container = document.getElementById('activityLogContainer');
            let html = '';

            activities.forEach(activity => {
                html += `
                    <div class="activity-item">
                        <div class="activity-icon" style="background-color: ${getActivityColor(activity.activityType)}">
                            <i class="${getActivityIcon(activity.activityType)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="fw-bold">${activity.description}</div>
                            <div class="activity-time">
                                <i class="fas fa-user me-1"></i>${activity.username}
                                <span class="mx-2">•</span>
                                <i class="fas fa-clock me-1"></i>${formatDateTime(activity.createTime)}
                                <span class="mx-2">•</span>
                                <span class="badge bg-secondary">${getModuleDisplayName(activity.module)}</span>
                            </div>
                        </div>
                        <div class="activity-status">
                            <span class="badge ${activity.result === 'SUCCESS' ? 'bg-success' : 'bg-danger'}">
                                ${activity.result === 'SUCCESS' ? '成功' : '失败'}
                            </span>
                        </div>
                    </div>
                `;
            });

            container.innerHTML += html;
        }

        // 更新分页控制
        function updatePaginationControls(pageData) {
            // 更新按钮状态
            const firstPageBtn = document.getElementById('firstPageBtn');
            const prevPageBtn = document.getElementById('prevPageBtn');
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            const nextPageBtn = document.getElementById('nextPageBtn');
            const lastPageBtn = document.getElementById('lastPageBtn');

            if (firstPageBtn) firstPageBtn.disabled = !pageData.hasPrevious;
            if (prevPageBtn) prevPageBtn.disabled = !pageData.hasPrevious;
            if (nextPageBtn) nextPageBtn.disabled = !pageData.hasNext;
            if (lastPageBtn) lastPageBtn.disabled = !pageData.hasNext;

            if (loadMoreBtn) {
                if (pageData.hasNext) {
                    loadMoreBtn.innerHTML = '<i class="fas fa-chevron-down me-1"></i>下一页';
                    loadMoreBtn.disabled = false;
                } else {
                    loadMoreBtn.innerHTML = '<i class="fas fa-check me-1"></i>已加载全部';
                    loadMoreBtn.disabled = true;
                }
            }

            // 更新分页信息
            updatePaginationInfo(pageData);
        }

        // 加载上一页
        function loadPreviousPage() {
            if (currentActivityPage > 0) {
                loadActivityPage(currentActivityPage - 1, false);
            }
        }

        // 加载下一页
        function loadNextPage() {
            if (currentActivityPage < totalActivityPages - 1) {
                loadActivityPage(currentActivityPage + 1, false);
            }
        }

        // 加载最后一页
        function loadLastPage() {
            if (totalActivityPages > 0) {
                loadActivityPage(totalActivityPages - 1, false);
            }
        }

        // 更新分页信息显示
        function updatePaginationInfo(pageData) {
            const infoContainer = document.querySelector('.activity-log-section .d-flex.justify-content-between');
            if (infoContainer) {
                const existingInfo = infoContainer.querySelector('.pagination-info');
                if (existingInfo) {
                    existingInfo.remove();
                }

                const paginationInfo = document.createElement('div');
                paginationInfo.className = 'pagination-info text-muted small';
                paginationInfo.innerHTML = `
                    第 ${pageData.currentPage + 1} 页，共 ${pageData.totalPages} 页
                    (总计 ${pageData.totalElements} 条记录)
                `;

                infoContainer.appendChild(paginationInfo);
            }
        }

        // 导出活动日志
        function exportActivityLog() {
            Swal.fire('提示', '导出功能开发中...', 'info');
        }

        // 处理头像加载错误
        function handleAvatarError(img) {
            console.warn('头像加载失败，使用默认头像');
            // 尝试多个可能的默认头像路径
            const fallbackPaths = [
                '/static/images/default-avatar.png',
                '/images/default-avatar.png',
                'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTggMTJDMTAuMjA5MSAxMiAxMiAxMC4yMDkxIDEyIDhDMTIgNS43OTA5IDEwLjIwOTEgNCA4IDRDNi43OTA5IDQgNCA1Ljc5MDkgNCA4QzQgMTAuMjA5MSA1Ljc5MDkgMTIgOCAxMloiIGZpbGw9IndoaXRlIi8+CjwvcGF0aD4KPC9zdmc+Cjwvc3ZnPgo='
            ];

            // 如果当前路径已经是最后一个fallback，就不再尝试
            if (img.src.includes('data:image/svg+xml')) {
                return;
            }

            // 尝试下一个fallback路径
            for (let path of fallbackPaths) {
                if (img.src !== window.location.origin + path) {
                    img.src = path;
                    break;
                }
            }
        }
    </script>
</body>
</html>