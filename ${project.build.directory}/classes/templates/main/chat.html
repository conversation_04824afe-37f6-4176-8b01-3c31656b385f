<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON> EDU - AI智能出题</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- 1. JQUERY - Must be first -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- 2. DISABLER SCRIPT for original chat.js (if it was ever a separate file named chat.js) -->
    <script>
        // Neutralize functions from any original/old chat.js that might conflict
        window.initChat = function() { console.log('[Disabler] Original global initChat neutralized.'); };
        window.loadChatHistory = function() { console.log('[Disabler] Original global loadChatHistory neutralized.'); };
        window.switchToChat = function() { console.log('[Disabler] Original global switchToChat neutralized.'); };
        window.handleSendMessage = function() { console.log('[Disabler] Original global handleSendMessage neutralized.'); };
        
        // Attempt to prevent loading of a script named "chat.js" if it's referenced elsewhere
        (function() {
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                const element = originalCreateElement.call(document, tagName);
                if (tagName.toLowerCase() === 'script') {
                    const originalSetAttribute = element.setAttribute;
                    element.setAttribute = function(name, value) {
                        if (name === 'src' && value && (value.includes('/static/js/chat.js') || value.includes('/js/chat.js'))) {
                            console.log('[Disabler] Preventing loading of script via src: ', value);
                            originalSetAttribute.call(this, 'data-original-src', value);
                            originalSetAttribute.call(this, 'type', 'text/inactive-javascript');
                            return;
                        }
                        return originalSetAttribute.call(this, name, value);
                    };
                }
                return element;
            };
            window.addEventListener('DOMContentLoaded', () => {
                document.querySelectorAll('script[src*="chat.js"]').forEach(script => {
                    if (!script.src.includes('chat-unified.js') && !script.src.includes('chat-fix.js')) { // Don't disable our main scripts
                        const srcValue = script.getAttribute('src');
                        console.log('[Disabler] On DOMContentLoaded, ensuring old chat.js is disabled:', srcValue);
                        script.setAttribute('data-original-src', srcValue);
                        try { script.removeAttribute('src'); } catch(e){}
                        script.type = 'text/inactive-javascript';
                        script.innerHTML = "console.log('[Disabler] Original chat.js content replaced.');";
                    }
                });
            }, { once: true });
        })();
    </script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/chat.css">

    <style>
        /* Additional styles from your provided HTML */
        .chat-item.active {
            background-color: #e9ecef; /* Example active color */
        }
        .loading-indicator {
            color: #6c757d;
        }
        /* Add any other necessary styles here */
        /* 聊天操作按钮样式 */
        .chat-actions .btn-link {
            padding: 0.25rem 0.5rem;
            margin-left: 0.25rem;
        }
        
        .chat-actions .btn-link:hover {
            background-color: rgba(0,0,0,0.05);
            border-radius: 4px;
        }
        
        /* 确保主聊天区域标题与操作按钮对齐 */
        .chat-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        
        #currentChatTitle {
            margin-bottom: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 80%;
        }
        
        /* 修复模态框遮罩层问题 */
        .modal-backdrop {
            z-index: 1040 !important;
        }
        .modal-dialog {
            z-index: 1060 !important;
            pointer-events: auto !important;
        }
        .modal-content {
            pointer-events: auto !important;
        }
        
        /* 确保输入框在模态框内可聚焦 */
        #newTitleInput {
            position: relative;
            z-index: 1065 !important;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="nav-brand"><a href="/">Maizi EDU</a></div>
        <div class="nav-menu">
            <a href="/main/chat" class="nav-item active">出题</a>
            <a href="/topics/upload-topics" class="nav-item">上传</a>
            <a href="/paper/generate" class="nav-item">组卷</a>
            <a href="/paper/check" class="nav-item">查重</a>
            <a href="/topics/bank" class="nav-item">题库</a>
            <a href="/main/books" class="nav-item">教材资源</a>
        </div>
        <div class="nav-user">
            <div class="user-info">
                <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                <span class="username">加载中...</span>
            </div>
            <div class="dropdown-menu">
                <a href="/user/profile">个人信息</a>
                <a href="#" id="logout">退出登录</a> <!-- Logout handled by common.js or similar -->
            </div>
        </div>
    </nav>

    <!-- Chat Container -->
    <div class="chat-container">
        <div class="chat-sidebar">
            <button class="new-chat-btn" id="newChatBtn"><i class="plus-icon"></i>新建对话</button>
            <div class="chat-history" id="chatHistory"></div>
            </div>
        <div class="chat-main">
            <div class="chat-header d-flex justify-content-between align-items-center mb-3">
                <h5 class="chat-title" id="currentChatTitle">当前聊天</h5>
                <div class="chat-actions">
                    <button id="editTitleBtn" class="btn btn-sm btn-link text-secondary" title="编辑标题">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button id="deleteChatBtn" class="btn btn-sm btn-link text-danger" title="删除聊天">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            <div id="chatMessages" class="chat-messages">
                <!-- 聊天消息将显示在这里 -->
            </div>
            <div class="chat-input-area-container">
                <div class="chat-config-inputs">
                    <div class="config-input-group">
                        <label for="knowId" class="form-label visually-hidden">知识点 ID</label>
                        <input type="text" id="knowId" class="form-control form-control-sm" placeholder="知识点 ID (可选)">
            </div>
                    <div class="config-input-group">
                        <label for="bookUrl" class="form-label visually-hidden">书籍链接</label>
                        <input type="text" id="bookUrl" class="form-control form-control-sm" placeholder="书籍链接 (可选)">
                </div>
                    </div>
                <div class="chat-main-input-group">
                    <textarea id="messageInput" placeholder="输入您的问题... (Shift+Enter 换行)" rows="1"></textarea>
                    <div class="chat-input-buttons">
                        <button id="sendMessage" class="btn btn-send" title="发送消息 (Enter)" disabled>
                            <i class="bi bi-send-fill"></i>
                        </button>
                        <button id="stopResponse" class="btn btn-stop" title="停止生成" style="display: none;">
                            <i class="bi bi-stop-circle-fill"></i>
                        </button>
                </div>
                    <div class="char-counter">0/4000</div>
                </div>
                <div class="input-help-text">
                    按回车键 (Enter) 发送 · Shift+Enter 换行 · / 使用提示词
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modals (Book Search, Book Details, Edit Title, Delete Confirm) -->
    <!-- Ensure these modals are correctly defined in your HTML if used by chat-unified.js -->
    <!-- Example: Edit Title Modal (if you implement it) -->
    <div class="modal fade" id="editTitleModal" tabindex="-1" aria-labelledby="editTitleModalLabel">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTitleModalLabel">编辑聊天标题</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newTitleInput">新标题</label>
                        <input type="text" class="form-control" id="newTitleInput" placeholder="输入新的聊天标题">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmEditTitleBtn">保存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Example: Delete Confirm Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除此聊天吗？<span id="deleteChatTitlePreview" class="fw-bold"></span></p>
                    <p class="text-danger">此操作不可撤销，所有相关消息记录将被永久删除。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteModalBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Bootstrap JS (depends on jQuery) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Helper scripts (common utilities, toast notifications) -->
    <!-- Ensure these are loaded before chat-unified.js if it depends on them -->
    <script src="/static/js/common.js"></script> <!-- For getAuthToken, logout etc. -->
    <script src="/static/js/toast.js"></script>   <!-- For showToast -->
    
    <!-- REMOVE references to old chat scripts -->
    <!-- <script src="/static/js/chat-state.js"></script> -->
    <script src="/static/js/chat-state-fix.js"></script>
    <!-- <script src="/static/js/chat-fix.js"></script> -->
    <!-- <script src="/static/js/avatar-fix.js"></script> -->
    <!-- <script src="/static/js/environment-check.js"></script> -->
    <!-- <script src="/static/js/tooltip-styles.js"></script> -->


    <!-- Global Ajax Error Handler (from previous chat.html) -->
    <script>
        jQuery(document).ajaxError(function(event, jqXHR) {
            if (jqXHR.status === 401) { // Unauthorized
                if (typeof showToast === 'function') {
                    showToast('登录已过期，请重新登录 (AJAX Error)', 'warning', 5000);
                } else {
                    console.warn('登录已过期 (AJAX Error)');
                }
                setTimeout(() => { window.location.href = '/login'; }, 2000);
            }
        });
        // Any other $(document).ready() logic that was in chat.html and is NOT
        // related to chat button bindings (which are now in chat-unified.js)
        // can remain here or be moved into common.js if truly global.
        // Example: Logout button from common.js might be initialized here if not in common.js itself.
        $(document).ready(function() {
            // Initialize tooltips if Bootstrap is used and you have elements with data-bs-toggle="tooltip"
            if (typeof bootstrap !== 'undefined' && typeof bootstrap.Tooltip !== 'undefined') {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
            // Logic for 'selectedBookInfo' from localStorage (pulsing button)
            // This can stay here as it's UI specific and not core chat logic.
            if (localStorage.getItem('selectedBookInfo')) {
                $('#newChatBtn').addClass('btn-pulse').css({
                    'animation': 'pulse 1.5s infinite',
                    'box-shadow': '0 0 0 rgba(38, 143, 255, 0.4)'
                });
                if(typeof showToast === 'function') {
                    showToast('您有待应用的教材，请点击"新建对话"按钮创建包含此教材的对话', 'info', 10000);
                }
            }
            // The specific #newChatBtn click handler that dealt with localStorage.getItem('selectedBookInfo')
            // has been effectively merged into MainChatApp.handleNewChat() if that logic is desired there.
            // Or, it can remain as a separate, more specific handler if it only deals with the book selection flow.
            // For now, assuming MainChatApp.handleNewChat is the primary way to create chats.
        });
    </script>

    <!-- UNIFIED CHAT SCRIPT - Load this last -->
    <script src="/static/js/chat-unified.js"></script>

</body>
</html> 