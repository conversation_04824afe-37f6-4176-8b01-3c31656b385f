<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息 - <PERSON><PERSON> EDU</title>
    <link rel="stylesheet" href="/static/css/common.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <a href="/">Maizi EDU</a>
        </div>
        <div class="nav-menu">
            <a href="/main/chat" class="nav-item">出题</a>
            <a href="/topics/upload-topics" class="nav-item">上传</a>
            <a href="/paper/generate" class="nav-item">组卷</a>
            <a href="/paper/check" class="nav-item">查重</a>
            <a href="/topics/bank" class="nav-item">题库</a>
        </div>
        <div class="nav-user">
            <div class="user-info">
                <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                <span class="username">加载中...</span>
            </div>
            <div class="dropdown-menu">
                <a href="/user/profile" class="active">个人信息</a>
                <a href="#" id="logout">退出登录</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="avatar-container mb-3">
                            <img src="/static/images/default-avatar.png" alt="User Avatar" class="rounded-circle profile-avatar" id="userAvatar">
                            <button class="btn btn-sm btn-light upload-avatar-btn" id="uploadAvatarBtn">
                                <i class="bi bi-pencil"></i>
                            </button>
                        </div>
                        <h4 id="profileUsername">加载中...</h4>
                        <p class="text-muted" id="profileRole">用户</p>
                        <input type="file" id="avatarInput" style="display: none;" accept="image/*">
                    </div>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>我的题目</span>
                            <span class="badge bg-primary rounded-pill" id="topicCount">0</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>我的试卷</span>
                            <span class="badge bg-primary rounded-pill" id="paperCount">0</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        个人信息
                    </div>
                    <div class="card-body">
                        <form id="profileForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" readonly>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email">
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">手机号</label>
                                <input type="tel" class="form-control" id="phone">
                            </div>
                            <div class="mb-3">
                                <label for="bio" class="form-label">个人简介</label>
                                <textarea class="form-control" id="bio" rows="3"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">保存信息</button>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        修改密码
                    </div>
                    <div class="card-body">
                        <form id="passwordForm">
                            <div class="mb-3">
                                <label for="currentPassword" class="form-label">当前密码</label>
                                <input type="password" class="form-control" id="currentPassword" required>
                            </div>
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="newPassword" required>
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                            <button type="submit" class="btn btn-primary">修改密码</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示框 -->
    <div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/toast.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载用户信息
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/auth/login';
                return;
            }

            fetch('/api/user/info', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    const user = data.data;
                    document.querySelector('.username').textContent = user.username;
                    document.getElementById('profileUsername').textContent = user.username;
                    document.getElementById('username').value = user.username;

                    if (user.avatar) {
                        document.querySelector('.avatar').src = user.avatar;
                        document.getElementById('userAvatar').src = user.avatar;
                    }

                    if (user.email) {
                        document.getElementById('email').value = user.email;
                    }

                    if (user.phone) {
                        document.getElementById('phone').value = user.phone;
                    }

                    if (user.bio) {
                        document.getElementById('bio').value = user.bio;
                    }

                    if (user.role) {
                        document.getElementById('profileRole').textContent = user.role;
                    }
                } else {
                    window.location.href = '/auth/login';
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                showToast('获取用户信息失败，请重新登录', 'error');
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 1500);
            });

            // 注销功能
            document.getElementById('logout').addEventListener('click', async function(e) {
                e.preventDefault();
                console.log("个人信息页面退出登录按钮被点击");
                if (typeof logout === 'function') {
                    await logout();
                } else {
                    // 备用退出逻辑
                    localStorage.removeItem('token');
                    localStorage.removeItem('currentUser');
                    showToast('已成功退出登录', 'info');
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 1500);
                }
            });

            // 上传头像
            document.getElementById('uploadAvatarBtn').addEventListener('click', function() {
                document.getElementById('avatarInput').click();
            });

            document.getElementById('avatarInput').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    const formData = new FormData();
                    formData.append('avatar', file);

                    showToast('头像上传功能正在开发中...', 'info');

                    // 预览上传的头像
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        document.getElementById('userAvatar').src = event.target.result;
                        document.querySelector('.avatar').src = event.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });

            // 个人信息表单提交
            document.getElementById('profileForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const token = localStorage.getItem('token');
                if (!token) {
                    showToast('请先登录', 'error');
                    window.location.href = '/auth/login';
                    return;
                }

                const formData = {
                    email: document.getElementById('email').value.trim(),
                    phone: document.getElementById('phone').value.trim(),
                    bio: document.getElementById('bio').value.trim()
                };

                try {
                    const response = await fetch('/api/user/profile', {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        showToast('个人信息更新成功', 'success');
                    } else {
                        showToast(result.message || '更新失败', 'error');
                    }
                } catch (error) {
                    console.error('更新个人信息失败:', error);
                    showToast('更新失败，请稍后重试', 'error');
                }
            });

            // 修改密码表单提交
            document.getElementById('passwordForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (newPassword !== confirmPassword) {
                    showToast('两次输入的密码不一致', 'error');
                    return;
                }

                showToast('密码修改功能正在开发中...', 'info');
            });
        });
    </script>
</body>
</html>