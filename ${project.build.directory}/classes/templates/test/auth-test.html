<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>认证功能测试页面</h1>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>当前认证状态</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Token:</strong> <span id="tokenStatus">检查中...</span></p>
                        <p><strong>用户ID:</strong> <span id="userId">检查中...</span></p>
                        <p><strong>用户名:</strong> <span id="username">检查中...</span></p>
                        <p><strong>认证状态:</strong> <span id="authStatus">检查中...</span></p>
                        <button class="btn btn-primary" onclick="checkAuth()">重新检查</button>
                        <button class="btn btn-info" onclick="checkUsers()">检查用户</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试下载记录</h5>
                    </div>
                    <div class="card-body">
                        <p>测试试卷下载记录功能</p>
                        <div class="btn-group" role="group">
                            <button class="btn btn-success" onclick="testDownload()">测试PDF下载</button>
                            <button class="btn btn-info" onclick="testDownloadWord()">测试Word下载</button>
                            <button class="btn btn-warning" onclick="testDownloadAlternative()">测试备用路径</button>
                            <button class="btn btn-secondary" onclick="testDebugPdf()">调试PDF生成</button>
                            <button class="btn btn-warning" onclick="testMathPdf()">数学公式PDF</button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">现在使用先进的PDF生成器，支持数学公式渲染和更好的排版</small>
                        </div>
                        <div class="mt-2">
                            <a href="/test/math" class="btn btn-outline-primary btn-sm" target="_blank">数学公式测试</a>
                            <a href="/test/pdf" class="btn btn-outline-success btn-sm" target="_blank">PDF生成测试</a>
                        </div>
                        <div id="downloadResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>调试日志</h5>
                    </div>
                    <div class="card-body">
                        <pre id="debugLog" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px;"></pre>
                        <button class="btn btn-secondary" onclick="clearLog()">清除日志</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let debugLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            document.getElementById('debugLog').textContent = debugLog.join('\n');
        }

        function clearLog() {
            debugLog = [];
            document.getElementById('debugLog').textContent = '';
        }

        async function checkAuth() {
            log('开始检查认证状态...');

            const token = localStorage.getItem('token');
            document.getElementById('tokenStatus').textContent = token ? '存在' : '不存在';
            log(`Token状态: ${token ? '存在' : '不存在'}`);

            if (!token) {
                document.getElementById('userId').textContent = '未登录';
                document.getElementById('username').textContent = '未登录';
                document.getElementById('authStatus').textContent = '未认证';
                log('用户未登录');
                return;
            }

            try {
                const response = await fetch('/api/user/current', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                log(`API响应状态: ${response.status}`);

                if (response.ok) {
                    const result = await response.json();
                    log(`API响应: ${JSON.stringify(result)}`);

                    if (result.success && result.data) {
                        document.getElementById('userId').textContent = result.data.id || '未知';
                        document.getElementById('username').textContent = result.data.username || '未知';
                        document.getElementById('authStatus').textContent = '已认证';
                        log(`认证成功: 用户ID=${result.data.id}, 用户名=${result.data.username}`);
                    } else {
                        document.getElementById('userId').textContent = '获取失败';
                        document.getElementById('username').textContent = '获取失败';
                        document.getElementById('authStatus').textContent = '认证失败';
                        log(`认证失败: ${result.message || '未知错误'}`);
                    }
                } else {
                    document.getElementById('userId').textContent = '请求失败';
                    document.getElementById('username').textContent = '请求失败';
                    document.getElementById('authStatus').textContent = '请求失败';
                    log(`请求失败: HTTP ${response.status}`);
                }
            } catch (error) {
                log(`请求异常: ${error.message}`);
                document.getElementById('userId').textContent = '异常';
                document.getElementById('username').textContent = '异常';
                document.getElementById('authStatus').textContent = '异常';
            }
        }

        async function checkUsers() {
            log('开始检查用户数据...');

            try {
                const response = await fetch('/api/user/debug/users');
                log(`用户检查请求状态: ${response.status}`);

                if (response.ok) {
                    const result = await response.json();
                    log(`用户数据: ${JSON.stringify(result, null, 2)}`);

                    if (result.success && result.data) {
                        const users = result.data.users || [];
                        log(`系统中共有 ${result.data.totalUsers} 个用户`);
                        users.forEach(user => {
                            log(`用户: ID=${user.id}, 用户名=${user.username}, 邮箱=${user.email}, 角色=${user.role}, 状态=${user.status}, 删除=${user.deleted}`);
                        });
                    }
                } else {
                    log(`用户检查失败: HTTP ${response.status}`);
                }
            } catch (error) {
                log(`用户检查异常: ${error.message}`);
            }
        }

        async function testDownload() {
            await testDownloadWithPath('/api/papers/download/1?format=pdf', 'PDF下载');
        }

        async function testDownloadWord() {
            await testDownloadWithPath('/api/papers/download/1?format=word', 'Word下载');
        }

        async function testDownloadAlternative() {
            await testDownloadWithPath('/papers/download/1', 'PDF下载(备用路径)');
        }

        async function testDebugPdf() {
            log('开始测试调试PDF生成...');

            try {
                log('请求URL: /debug/pdf');
                const response = await fetch('/debug/pdf');

                log(`调试PDF生成请求状态: ${response.status}`);

                if (response.ok) {
                    // 检查响应类型
                    const contentType = response.headers.get('content-type');
                    log(`响应内容类型: ${contentType}`);

                    document.getElementById('downloadResult').innerHTML = `<div class="alert alert-success">调试PDF生成成功！<br>内容类型: ${contentType}</div>`;
                    log('调试PDF生成成功');
                } else {
                    // 尝试获取错误信息
                    let errorMessage = `HTTP ${response.status}`;
                    try {
                        const errorText = await response.text();
                        if (errorText) {
                            errorMessage += `: ${errorText}`;
                        }
                    } catch (e) {
                        log(`无法读取错误响应: ${e.message}`);
                    }

                    document.getElementById('downloadResult').innerHTML = `<div class="alert alert-danger">调试PDF生成失败: ${errorMessage}</div>`;
                    log(`调试PDF生成失败: ${errorMessage}`);
                }
            } catch (error) {
                document.getElementById('downloadResult').innerHTML = `<div class="alert alert-danger">调试PDF生成异常: ${error.message}</div>`;
                log(`调试PDF生成异常: ${error.message}`);
            }
        }

        async function testMathPdf() {
            log('开始测试数学公式PDF生成...');

            try {
                log('请求URL: /debug/math-pdf');
                const response = await fetch('/debug/math-pdf');

                log(`数学公式PDF生成请求状态: ${response.status}`);

                if (response.ok) {
                    // 检查响应类型
                    const contentType = response.headers.get('content-type');
                    log(`响应内容类型: ${contentType}`);

                    document.getElementById('downloadResult').innerHTML = `<div class="alert alert-success">数学公式PDF生成成功！<br>内容类型: ${contentType}<br><small>包含各种数学公式的测试PDF已生成</small></div>`;
                    log('数学公式PDF生成成功');
                } else {
                    // 尝试获取错误信息
                    let errorMessage = `HTTP ${response.status}`;
                    try {
                        const errorText = await response.text();
                        if (errorText) {
                            errorMessage += `: ${errorText}`;
                        }
                    } catch (e) {
                        log(`无法读取错误响应: ${e.message}`);
                    }

                    document.getElementById('downloadResult').innerHTML = `<div class="alert alert-danger">数学公式PDF生成失败: ${errorMessage}</div>`;
                    log(`数学公式PDF生成失败: ${errorMessage}`);
                }
            } catch (error) {
                document.getElementById('downloadResult').innerHTML = `<div class="alert alert-danger">数学公式PDF生成异常: ${error.message}</div>`;
                log(`数学公式PDF生成异常: ${error.message}`);
            }
        }

        async function testDownloadWithPath(url, description) {
            log(`开始测试${description}功能...`);

            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('downloadResult').innerHTML = '<div class="alert alert-warning">请先登录</div>';
                log(`测试${description}失败: 用户未登录`);
                return;
            }

            try {
                log(`请求URL: ${url}`);
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                log(`${description}请求状态: ${response.status}`);

                if (response.ok) {
                    // 检查响应类型
                    const contentType = response.headers.get('content-type');
                    log(`响应内容类型: ${contentType}`);

                    document.getElementById('downloadResult').innerHTML = `<div class="alert alert-success">${description}测试成功！检查后台日志查看下载记录。<br>内容类型: ${contentType}</div>`;
                    log(`${description}测试成功`);
                } else {
                    // 尝试获取错误信息
                    let errorMessage = `HTTP ${response.status}`;
                    try {
                        const errorText = await response.text();
                        if (errorText) {
                            errorMessage += `: ${errorText}`;
                        }
                    } catch (e) {
                        log(`无法读取错误响应: ${e.message}`);
                    }

                    document.getElementById('downloadResult').innerHTML = `<div class="alert alert-danger">${description}测试失败: ${errorMessage}</div>`;
                    log(`${description}测试失败: ${errorMessage}`);
                }
            } catch (error) {
                document.getElementById('downloadResult').innerHTML = `<div class="alert alert-danger">${description}测试异常: ${error.message}</div>`;
                log(`${description}测试异常: ${error.message}`);
            }
        }

        // 页面加载时自动检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始检查认证状态');
            checkAuth();
        });
    </script>
</body>
</html>
