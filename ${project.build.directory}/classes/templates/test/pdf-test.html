<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF生成测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log-area {
            height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">PDF生成系统测试</h1>

        <div class="row">
            <div class="col-md-8">
                <!-- 基础PDF测试 -->
                <div class="test-section">
                    <h3>基础PDF生成测试</h3>
                    <p>测试基本的PDF生成功能</p>
                    <div class="btn-group" role="group">
                        <button class="btn btn-primary" onclick="testUltraSimplePdf()">超简单PDF</button>
                        <button class="btn btn-success" onclick="testReliablePdf()">可靠PDF测试</button>
                        <button class="btn btn-info" onclick="testAdvancedPdf()">测试先进PDF</button>
                        <button class="btn btn-outline-secondary" onclick="debugHtml()">调试HTML</button>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-warning btn-sm" onclick="debugHtmlContent()">查看HTML源码</button>
                        <button class="btn btn-info btn-sm ml-2" onclick="debugTopicData()">查看题目数据</button>
                        <button class="btn btn-danger btn-sm ml-2" onclick="debugQuestionTypes()">调试题型映射</button>
                        <button class="btn btn-success btn-sm ml-2" onclick="analyzeKnowledgePoints()">分析知识点分布</button>
                        <small class="text-muted ml-2">用于调试PDF内容和题型映射问题</small>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <strong>推荐顺序</strong>：超简单PDF → 可靠PDF → 先进PDF<br>
                            超简单PDF：零依赖问题，使用默认字体，100%稳定<br>
                            可靠PDF：支持中文字体，功能更丰富
                        </small>
                    </div>
                    <div id="basicResult" class="mt-3"></div>
                </div>

                <!-- 数学公式PDF测试 -->
                <div class="test-section">
                    <h3>数学公式PDF测试</h3>
                    <p>测试包含数学公式的PDF生成</p>
                    <div class="btn-group" role="group">
                        <button class="btn btn-warning" onclick="testMathFormulaPdf()">数学公式PDF</button>
                        <button class="btn btn-secondary" onclick="testComplexMath()">复杂数学公式</button>
                    </div>
                    <div id="mathResult" class="mt-3"></div>
                </div>

                <!-- 试卷PDF测试 -->
                <div class="test-section">
                    <h3>试卷PDF下载测试</h3>
                    <p>测试实际试卷的PDF生成和下载</p>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary" onclick="testPaperDownload(1)">试卷1 PDF</button>
                        <button class="btn btn-outline-success" onclick="testPaperDownload(79)">试卷79 PDF</button>
                        <button class="btn btn-outline-info" onclick="testPaperDownload(80)">试卷80 PDF</button>
                        <button class="btn btn-outline-warning" onclick="testPaperDownload(100)">数学试卷100 PDF</button>
                    </div>
                    <div id="paperResult" class="mt-3"></div>
                </div>

                <!-- 系统状态 -->
                <div class="test-section">
                    <h3>系统状态检查</h3>
                    <div id="systemStatus">
                        <div><span class="status-indicator status-info"></span>OpenHTMLToPDF: <span id="openHtmlStatus">检查中...</span></div>
                        <div><span class="status-indicator status-info"></span>iText: <span id="itextStatus">检查中...</span></div>
                        <div><span class="status-indicator status-info"></span>Flying Saucer: <span id="flyingSaucerStatus">检查中...</span></div>
                        <div><span class="status-indicator status-info"></span>数学公式支持: <span id="mathStatus">检查中...</span></div>
                    </div>
                    <button class="btn btn-sm btn-secondary mt-2" onclick="checkSystemStatus()">重新检查</button>
                </div>
            </div>

            <div class="col-md-4">
                <div class="test-section">
                    <h3>测试日志</h3>
                    <div id="logArea" class="log-area"></div>
                    <button class="btn btn-sm btn-secondary mt-2" onclick="clearLog()">清空日志</button>
                </div>

                <div class="test-section">
                    <h3>快速链接</h3>
                    <div class="d-grid gap-2">
                        <a href="/test/auth" class="btn btn-outline-primary">认证测试页面</a>
                        <a href="/test/math" class="btn btn-outline-success">数学公式测试</a>
                        <a href="/papers/generate" class="btn btn-outline-info">试卷生成页面</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const typeClass = type === 'error' ? 'text-danger' :
                             type === 'success' ? 'text-success' :
                             type === 'warning' ? 'text-warning' : 'text-info';
            logArea.innerHTML += `<div class="${typeClass}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        // 基础PDF测试
        async function testUltraSimplePdf() {
            log('开始测试超简单PDF生成...');
            await testEndpoint('/debug/ultra-simple-pdf', 'basicResult', '超简单PDF');
        }

        async function testReliablePdf() {
            log('开始测试可靠PDF生成...');
            await testEndpoint('/debug/reliable-pdf', 'basicResult', '可靠PDF');
        }

        async function testHtmlPdf() {
            log('开始测试HTML PDF生成...');
            await testEndpoint('/api/papers/download/1?format=pdf', 'basicResult', 'HTML PDF');
        }

        async function testAdvancedPdf() {
            log('开始测试先进PDF生成...');
            await testEndpoint('/debug/math-pdf', 'basicResult', '先进PDF');
        }

        async function debugHtml() {
            log('开始调试HTML格式...');
            try {
                const response = await fetch('/debug/html');
                if (response.ok) {
                    const htmlContent = await response.text();
                    log('HTML调试成功，长度: ' + htmlContent.length, 'success');

                    // 在新窗口中显示HTML
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(htmlContent);
                    newWindow.document.close();

                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-success">
                            <strong>HTML调试成功！</strong><br>
                            HTML长度: ${htmlContent.length} 字符<br>
                            已在新窗口中打开HTML内容
                        </div>`;
                } else {
                    const errorText = await response.text();
                    log('HTML调试失败: ' + errorText, 'error');
                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-danger">HTML调试失败: ${errorText}</div>`;
                }
            } catch (error) {
                log('HTML调试异常: ' + error.message, 'error');
                document.getElementById('basicResult').innerHTML =
                    `<div class="alert alert-danger">HTML调试异常: ${error.message}</div>`;
            }
        }

        async function debugHtmlContent() {
            log('开始查看HTML源码...');
            try {
                const response = await fetch('/debug/html-content/1');
                if (response.ok) {
                    const htmlContent = await response.text();
                    log('HTML源码长度: ' + htmlContent.length);

                    // 在新窗口中显示HTML源码
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write('<pre style="white-space: pre-wrap; font-family: monospace; font-size: 12px; padding: 20px;">' +
                        htmlContent.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>');
                    newWindow.document.title = 'HTML源码调试';

                    log('HTML源码已在新窗口中打开');

                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-info">
                            <strong>HTML源码调试成功！</strong><br>
                            HTML长度: ${htmlContent.length} 字符<br>
                            已在新窗口中打开源码内容
                        </div>`;
                } else {
                    log('获取HTML源码失败: ' + response.status);
                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-danger">获取HTML源码失败: ${response.status}</div>`;
                }
            } catch (error) {
                log('获取HTML源码出错: ' + error.message);
                document.getElementById('basicResult').innerHTML =
                    `<div class="alert alert-danger">获取HTML源码出错: ${error.message}</div>`;
            }
        }

        async function debugTopicData() {
            log('开始查看题目数据...');
            try {
                const response = await fetch('/debug/topic-data/1');
                if (response.ok) {
                    const topicData = await response.text();
                    log('题目数据获取成功');

                    // 在新窗口中显示题目数据
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(topicData);
                    newWindow.document.title = '题目数据调试';

                    log('题目数据已在新窗口中打开');

                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-info">
                            <strong>题目数据调试成功！</strong><br>
                            已在新窗口中打开题目数据详情
                        </div>`;
                } else {
                    log('获取题目数据失败: ' + response.status);
                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-danger">获取题目数据失败: ${response.status}</div>`;
                }
            } catch (error) {
                log('获取题目数据出错: ' + error.message);
                document.getElementById('basicResult').innerHTML =
                    `<div class="alert alert-danger">获取题目数据出错: ${error.message}</div>`;
            }
        }

        async function debugQuestionTypes() {
            log('开始调试题型映射...');
            try {
                const response = await fetch('/debug/question-types');
                if (response.ok) {
                    const debugData = await response.text();
                    log('题型映射调试数据获取成功');

                    // 在新窗口中显示调试数据
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(debugData);
                    newWindow.document.title = '题型映射调试';

                    log('题型映射调试数据已在新窗口中打开');

                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-success">
                            <strong>题型映射调试成功！</strong><br>
                            已在新窗口中打开调试详情，请检查：<br>
                            1. 数据库中的题型存储格式<br>
                            2. 题型映射是否正确<br>
                            3. 判断题的实际数量
                        </div>`;
                } else {
                    log('获取题型映射调试数据失败: ' + response.status);
                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-danger">获取题型映射调试数据失败: ${response.status}</div>`;
                }
            } catch (error) {
                log('获取题型映射调试数据出错: ' + error.message);
                document.getElementById('basicResult').innerHTML =
                    `<div class="alert alert-danger">获取题型映射调试数据出错: ${error.message}</div>`;
            }
        }

        async function analyzeKnowledgePoints() {
            log('开始分析知识点分布...');
            try {
                const response = await fetch('/debug/knowledge-point-analysis');
                if (response.ok) {
                    const analysisData = await response.text();
                    log('知识点分布分析数据获取成功');

                    // 在新窗口中显示分析数据
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(analysisData);
                    newWindow.document.title = '知识点题目分布分析';

                    log('知识点分布分析已在新窗口中打开');

                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-info">
                            <strong>知识点分布分析成功！</strong><br>
                            已在新窗口中打开详细分析，请检查：<br>
                            1. 各知识点的题目数量分布<br>
                            2. 判断题在各知识点中的数量<br>
                            3. 是否需要增加更多知识点或调整题型要求
                        </div>`;
                } else {
                    log('获取知识点分布分析失败: ' + response.status);
                    document.getElementById('basicResult').innerHTML =
                        `<div class="alert alert-danger">获取知识点分布分析失败: ${response.status}</div>`;
                }
            } catch (error) {
                log('获取知识点分布分析出错: ' + error.message);
                document.getElementById('basicResult').innerHTML =
                    `<div class="alert alert-danger">获取知识点分布分析出错: ${error.message}</div>`;
            }
        }

        // 数学公式PDF测试
        async function testMathFormulaPdf() {
            log('开始测试数学公式PDF生成...');
            await testEndpoint('/debug/math-pdf', 'mathResult', '数学公式PDF');
        }

        async function testComplexMath() {
            log('开始测试复杂数学公式...');
            await testEndpoint('/api/papers/download/100?format=pdf', 'mathResult', '复杂数学公式');
        }

        // 试卷PDF测试
        async function testPaperDownload(paperId) {
            log(`开始测试试卷${paperId} PDF下载...`);
            await testEndpoint(`/api/papers/download/${paperId}?format=pdf`, 'paperResult', `试卷${paperId}`);
        }

        // 通用测试端点函数
        async function testEndpoint(url, resultElementId, description) {
            try {
                log(`请求URL: ${url}`);
                const response = await fetch(url);

                log(`${description}请求状态: ${response.status}`);

                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    const contentLength = response.headers.get('content-length');

                    log(`响应内容类型: ${contentType}`, 'success');
                    log(`响应大小: ${contentLength || '未知'} bytes`, 'success');

                    document.getElementById(resultElementId).innerHTML =
                        `<div class="alert alert-success">
                            <strong>${description}测试成功！</strong><br>
                            内容类型: ${contentType}<br>
                            大小: ${contentLength || '未知'} bytes
                        </div>`;
                    log(`${description}测试成功`, 'success');
                } else {
                    let errorMessage = `HTTP ${response.status}`;
                    try {
                        const errorText = await response.text();
                        if (errorText) {
                            errorMessage += `: ${errorText}`;
                        }
                    } catch (e) {
                        log(`无法读取错误响应: ${e.message}`, 'warning');
                    }

                    document.getElementById(resultElementId).innerHTML =
                        `<div class="alert alert-danger">
                            <strong>${description}测试失败</strong><br>
                            错误: ${errorMessage}
                        </div>`;
                    log(`${description}测试失败: ${errorMessage}`, 'error');
                }
            } catch (error) {
                document.getElementById(resultElementId).innerHTML =
                    `<div class="alert alert-danger">
                        <strong>${description}测试异常</strong><br>
                        错误: ${error.message}
                    </div>`;
                log(`${description}测试异常: ${error.message}`, 'error');
            }
        }

        // 系统状态检查
        async function checkSystemStatus() {
            log('开始检查系统状态...');

            // 检查各个组件
            const checks = [
                { id: 'openHtmlStatus', url: '/debug/math-pdf', name: 'OpenHTMLToPDF' },
                { id: 'itextStatus', url: '/debug/pdf', name: 'iText' },
                { id: 'flyingSaucerStatus', url: '/api/papers/download/1?format=pdf', name: 'Flying Saucer' },
                { id: 'mathStatus', url: '/api/papers/download/100?format=pdf', name: '数学公式支持' }
            ];

            for (const check of checks) {
                try {
                    const response = await fetch(check.url);
                    const statusElement = document.getElementById(check.id);
                    const indicator = statusElement.previousElementSibling;

                    if (response.ok) {
                        statusElement.textContent = '正常';
                        indicator.className = 'status-indicator status-success';
                        log(`${check.name}: 正常`, 'success');
                    } else {
                        statusElement.textContent = `错误 (${response.status})`;
                        indicator.className = 'status-indicator status-error';
                        log(`${check.name}: 错误 ${response.status}`, 'error');
                    }
                } catch (error) {
                    const statusElement = document.getElementById(check.id);
                    const indicator = statusElement.previousElementSibling;
                    statusElement.textContent = '异常';
                    indicator.className = 'status-indicator status-error';
                    log(`${check.name}: 异常 - ${error.message}`, 'error');
                }
            }
        }

        // 页面加载时检查系统状态
        document.addEventListener('DOMContentLoaded', function() {
            log('PDF测试页面加载完成');
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
