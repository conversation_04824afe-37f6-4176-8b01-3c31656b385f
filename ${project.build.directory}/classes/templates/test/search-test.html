<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识点搜索功能测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/static/css/knowledge-search.css">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-search mr-2"></i>知识点搜索功能测试</h2>
                <p class="text-muted">测试知识点搜索功能是否正常工作</p>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-flask mr-2"></i>API测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="testKeyword">搜索关键词：</label>
                            <input type="text" class="form-control" id="testKeyword" placeholder="输入要搜索的知识点名称">
                        </div>
                        <button class="btn btn-primary" id="testSearchBtn">
                            <i class="fas fa-search mr-1"></i>测试搜索
                        </button>
                        <button class="btn btn-secondary ml-2" id="clearTestBtn">
                            <i class="fas fa-times mr-1"></i>清空结果
                        </button>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle mr-2"></i>API响应</h6>
                    </div>
                    <div class="card-body">
                        <pre id="apiResponse" class="bg-light p-3" style="max-height: 300px; overflow-y: auto;">
等待搜索...
                        </pre>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-list mr-2"></i>搜索结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="searchResults">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-search fa-3x mb-3"></i>
                                <p>输入关键词开始搜索</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-cogs mr-2"></i>测试说明</h5>
                    </div>
                    <div class="card-body">
                        <h6>功能特性：</h6>
                        <ul>
                            <li><strong>模糊搜索：</strong>支持知识点名称的模糊匹配</li>
                            <li><strong>多关键词：</strong>支持空格分隔的多个关键词搜索</li>
                            <li><strong>实时搜索：</strong>输入时自动触发搜索（防抖处理）</li>
                            <li><strong>高亮显示：</strong>搜索结果中关键词会被高亮显示</li>
                            <li><strong>分组显示：</strong>搜索结果按知识点分类分组显示</li>
                        </ul>

                        <h6 class="mt-3">测试建议：</h6>
                        <ul>
                            <li>尝试搜索 "数学"、"语文"、"英语" 等学科名称</li>
                            <li>尝试搜索具体的知识点名称</li>
                            <li>尝试输入多个关键词，用空格分隔</li>
                            <li>尝试输入不存在的关键词，查看错误处理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // 测试搜索按钮点击事件
            $('#testSearchBtn').on('click', function() {
                const keyword = $('#testKeyword').val().trim();
                if (!keyword) {
                    alert('请输入搜索关键词');
                    return;
                }
                performTestSearch(keyword);
            });

            // 清空结果按钮点击事件
            $('#clearTestBtn').on('click', function() {
                $('#testKeyword').val('');
                $('#apiResponse').text('等待搜索...');
                $('#searchResults').html(`
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>输入关键词开始搜索</p>
                    </div>
                `);
            });

            // 回车键搜索
            $('#testKeyword').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#testSearchBtn').click();
                }
            });
        });

        function performTestSearch(keyword) {
            // 显示加载状态
            $('#apiResponse').text('正在搜索...');
            $('#searchResults').html(`
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">搜索中...</span>
                    </div>
                    <p class="mt-2">正在搜索知识点...</p>
                </div>
            `);

            // 调用搜索API
            $.ajax({
                url: '/api/knowledge/search',
                method: 'GET',
                data: { keyword: keyword },
                success: function(response) {
                    // 显示API响应
                    $('#apiResponse').text(JSON.stringify(response, null, 2));
                    
                    // 渲染搜索结果
                    if (response && response.success && response.data) {
                        renderTestResults(response.data, keyword);
                    } else {
                        showTestError('搜索失败', response.message || '未能获取搜索结果');
                    }
                },
                error: function(xhr, status, error) {
                    const errorMsg = `HTTP ${xhr.status}: ${error}`;
                    $('#apiResponse').text(`搜索失败: ${errorMsg}`);
                    showTestError('网络错误', '无法连接到服务器，请检查网络连接');
                }
            });
        }

        function renderTestResults(results, keyword) {
            const container = $('#searchResults');
            
            if (!results || results.length === 0) {
                container.html(`
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>未找到匹配的知识点</h5>
                        <p>关键词: "${keyword}"</p>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle mr-2"></i>
                    找到 <strong>${results.length}</strong> 个匹配的知识点
                </div>
            `;

            results.forEach(function(item, index) {
                const isFree = item.isFree === 1;
                html += `
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">${escapeHtml(item.knowledgeName)}</h6>
                                    <small class="text-muted">
                                        ID: ${item.id} | 知识点ID: ${item.knowledgeId} | 
                                        分类: ${item.groupName || '未分类'}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge badge-info">${item.topicCount || 0} 题</span>
                                    ${isFree ? '<span class="badge badge-success ml-1">免费</span>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.html(html);
        }

        function showTestError(title, message) {
            $('#searchResults').html(`
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle mr-2"></i>${title}</h6>
                    <p class="mb-0">${message}</p>
                </div>
            `);
        }

        function escapeHtml(text) {
            if (!text) return '';
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    </script>
</body>
</html>
