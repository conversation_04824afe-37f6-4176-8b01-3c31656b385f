<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh">
<head>
    <meta charset="UTF-8">
    <title th:text="${pageTitle ?: '试卷预览'}"></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://polyfill-fastly.io/v3/polyfill.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- KaTeX Support -->
    <div th:replace="fragments/katex-support :: katex"></div>

    <!-- 数学公式处理 -->
    <script src="/static/js/math-formula-handler.js"></script>

    <style>
        body {
            font-family: 'SimHei', 'Microsoft YaHei', 'SimSun', serif; /* SimHei for better display */
            line-height: 1.6;
            background-color: #f0f0f0; /* Light gray background for screen */
            margin: 0;
            padding: 20px;
        }

        .preview-container {
            max-width: 800px; /* Limit width on screen */
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .paper {
            /* Styles for A4 printing */
            width: 100%;
            margin: 0 auto;
            page-break-after: always;
        }

        .paper-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .paper-title {
            font-size: 24px;
            font-weight: bold;
        }

        .paper-meta {
            font-size: 14px;
            color: #555;
        }

        .section {
            margin-top: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .question {
            margin-bottom: 15px;
            padding-left: 5px;
        }

        .question-text {
            font-weight: normal; /* Ensure question text is not bold if section title is */
            margin-bottom: 5px;
        }

        .question-content p { /* Assuming content might have <p> tags */
            margin-bottom: 5px;
        }

        .options {
            padding-left: 20px; /* Indent options */
        }

        .option {
            margin-bottom: 6px;
            padding: 4px 8px;
            font-family: 'SimHei', 'Microsoft YaHei', sans-serif;
            font-size: 12px;
            line-height: 1.5;
        }

        .option-label {
            font-weight: bold;
            margin-right: 6px;
            font-family: 'SimHei', 'Microsoft YaHei', sans-serif;
        }

        .option-text {
            font-family: 'SimHei', 'Microsoft YaHei', sans-serif;
        }

        .topic-options {
            margin-top: 8px;
            margin-left: 20px;
        }

        .topic-options .text-muted {
            font-style: italic;
            padding: 4px;
            font-family: 'SimHei', 'Microsoft YaHei', sans-serif;
            font-size: 11px;
        }

        .print-button-container {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }



        /* A4 Print Styles */
        @media print {
            @page {
                size: A4;
                margin: 1.5cm; /* Adjust margin as needed */
            }

            body {
                background-color: #fff; /* White background for print */
                padding: 0;
                margin: 0;
                font-size: 12pt; /* Standard print font size */
            }

            .preview-container {
                max-width: none;
                margin: 0;
                padding: 0;
                box-shadow: none;
                border: none;
            }

            .print-button-container {
                display: none; /* Hide print button when printing */
            }



            .paper {
                width: 100%; /* Use full available width within page margins */
                min-height: initial; /* Not needed for print, content flows */
                padding: 0;
                margin: 0 auto;
                border: none;
                box-shadow: none;
                background: transparent;
            }
        }
    </style>
</head>
<body>

<div class="preview-container">
    <div th:if="${paper}" class="paper">
        <div class="paper-header">
            <h1 class="paper-title" th:text="${paper.title}">试卷标题</h1>
            <p class="paper-meta">
                总分: <span th:text="${paper.totalScore}">100</span> |
                难度: <span th:text="${paper.difficulty != null ? #numbers.formatDecimal(paper.difficulty, 1, 2) : 'N/A'}">中等</span> |
                创建时间: <span th:text="${paper.createTime != null ? #temporals.format(paper.createTime, 'yyyy-MM-dd HH:mm') : 'N/A'}">2023-01-01</span>
            </p>
        </div>

        <div class="print-button-container">
            <button class="btn btn-primary" onclick="window.print();">
                <i class="fas fa-print"></i> 打印试卷
            </button>

            <!-- 版本选择下载 -->
            <div class="btn-group ml-2" role="group">
                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-download"></i> 下载试卷
                </button>
                <div class="dropdown-menu enhanced-download-menu" style="min-width: 280px;">
                    <div class="px-3 py-2">
                        <h6 class="dropdown-header">选择试卷版本</h6>
                        <select class="form-control form-control-sm version-selector mb-2" th:data-paper-id="${paper.id}">
                            <option value="regular">学生版 - 只有题目</option>
                            <option value="teacher">教师版 - 只有答案</option>
                            <option value="standard" selected>标准版 - 题目+答案</option>
                        </select>
                        <small class="text-muted version-description">
                            包含完整的题目、答案和解析，适合学习使用
                        </small>
                        <div class="dropdown-divider my-2"></div>
                        <h6 class="dropdown-header">下载格式</h6>
                        <button class="dropdown-item download-with-version" th:data-paper-id="${paper.id}" data-format="pdf">
                            <i class="fas fa-file-pdf text-danger"></i> PDF格式
                        </button>
                        <button class="dropdown-item download-with-version" th:data-paper-id="${paper.id}" data-format="word">
                            <i class="fas fa-file-word text-primary"></i> Word格式
                        </button>
                    </div>
                </div>
            </div>
        </div>



        <div th:if="${paper.topicsByType != null and !paper.topicsByType.isEmpty()}">
            <div th:each="entry : ${paper.topicsByType}" class="section">
                <h2 class="section-title" th:text="${entry.key} + ' (共' + ${#lists.size(entry.value)} + '题)'">题型标题</h2>
                <div th:each="topic, iterStat : ${entry.value}" class="question">
                    <div class="question-text">
                        <b th:text="${iterStat.count} + '. '">1. </b>
                        <span th:utext="${topic.title}" class="topic-title-content">题目内容...</span>
                        <span th:if="${topic.score != null}" th:text="' (' + ${topic.score} + '分)'" style="font-style: italic; color: #555;"> (5分)</span>
                    </div>
                    <!-- Display options for choice questions -->
                    <div th:if="${topic.type == 'choice' || topic.type == 'multiple' || topic.type == 'singleChoice' || topic.type == 'multipleChoice'}" class="options">
                        <!-- 调试信息 -->
                        <div class="debug-info" style="display: none;">
                            <small>题目类型: <span th:text="${topic.type}"></span></small><br>
                            <small>选项数据: <span th:text="${topic.options}"></span></small><br>
                            <small>选项长度: <span th:text="${topic.options != null ? topic.options.length() : 'null'}"></span></small>
                        </div>

                        <div th:if="${topic.options != null && topic.options.length() > 2}">
                            <!-- 直接使用JavaScript解析选项，更可靠 -->
                            <div class="topic-options" th:attr="data-options=${topic.options}, data-topic-id=${topic.id}, data-topic-type=${topic.type}">
                                <!-- 选项将通过JavaScript动态渲染 -->
                                <div class="text-muted">正在加载选项...</div>
                            </div>
                        </div>
                        <div th:if="${topic.options == null || topic.options.length() <= 2}" class="text-muted">
                            (无选项数据 - 长度: <span th:text="${topic.options != null ? topic.options.length() : 'null'}"></span>)
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:unless="${paper.topicsByType != null and !paper.topicsByType.isEmpty()}" class="alert alert-warning">
            此试卷没有详细的题目内容可供预览，或者题目数据未正确加载。
        </div>

    </div>
    <div th:unless="${paper}" class="alert alert-danger">
        加载试卷详情失败，或指定的试卷不存在。
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="/static/js/paper-version-selector.js"></script>
<script src="/static/js/options-display-test.js"></script>
<script src="/static/js/pdf-options-debug.js"></script>

<script>
$(document).ready(function() {
    console.log('🔧 开始渲染题目选项...');

    // 渲染所有题目的选项
    $('.topic-options').each(function() {
        const $container = $(this);
        const optionsData = $container.attr('data-options');
        const topicId = $container.attr('data-topic-id');

        console.log(`处理题目 ${topicId} 的选项:`, optionsData);

        if (optionsData && optionsData.trim() !== '') {
            try {
                // 解析选项数据
                let options = JSON.parse(optionsData);
                console.log(`题目 ${topicId} 解析后的选项:`, options);

                let optionsHtml = '';

                // 处理不同格式的选项数据
                if (Array.isArray(options)) {
                    // 新格式：[{key: "A", name: "选项内容"}, ...]
                    options.forEach(function(option, index) {
                        if (typeof option === 'object' && option.key && option.name) {
                            optionsHtml += `
                                <div class="option">
                                    <span class="option-label">${option.key}.</span>
                                    <span class="option-text">${option.name}</span>
                                </div>
                            `;
                        } else if (typeof option === 'string') {
                            // 简单字符串数组
                            const label = String.fromCharCode(65 + index); // A, B, C, D
                            optionsHtml += `
                                <div class="option">
                                    <span class="option-label">${label}.</span>
                                    <span class="option-text">${option}</span>
                                </div>
                            `;
                        }
                    });
                } else if (typeof options === 'object') {
                    // 旧格式：{A: "选项内容", B: "选项内容", ...}
                    Object.keys(options).forEach(function(key) {
                        optionsHtml += `
                            <div class="option">
                                <span class="option-label">${key}.</span>
                                <span class="option-text">${options[key]}</span>
                            </div>
                        `;
                    });
                }

                if (optionsHtml) {
                    $container.html(optionsHtml);
                    console.log(`✅ 题目 ${topicId} 选项渲染成功`);
                } else {
                    $container.html('<div class="text-muted">(选项格式不正确)</div>');
                    console.warn(`⚠️ 题目 ${topicId} 选项格式不正确:`, options);
                }

            } catch (e) {
                console.error(`❌ 题目 ${topicId} 选项解析失败:`, e);
                console.error('原始选项数据:', optionsData);
                $container.html('<div class="text-muted">(选项解析失败)</div>');
            }
        } else {
            $container.html('<div class="text-muted">(无选项数据)</div>');
            console.warn(`⚠️ 题目 ${topicId} 无选项数据`);
        }
    });

    console.log('✅ 选项渲染完成');

    // 渲染数学公式
    setTimeout(function() {
        console.log('🔧 开始渲染数学公式...');
        if (typeof renderMathInElement !== 'undefined') {
            try {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\(', right: '\\)', display: false},
                        {left: '\\[', right: '\\]', display: true}
                    ],
                    throwOnError: false,
                    errorColor: '#cc0000',
                    strict: false
                });
                console.log('✅ 数学公式渲染完成');
            } catch (e) {
                console.warn('❌ KaTeX渲染失败:', e);
            }
        } else if (typeof renderKaTeX !== 'undefined') {
            // 使用全局renderKaTeX函数
            try {
                renderKaTeX(document.body);
                console.log('✅ 数学公式渲染完成 (使用全局函数)');
            } catch (e) {
                console.warn('❌ KaTeX渲染失败:', e);
            }
        } else {
            console.warn('⚠️ KaTeX未加载，跳过数学公式渲染');
        }
    }, 100); // 延迟100ms确保DOM更新完成
});

// HTML转义函数 - 智能处理数学公式
function escapeHtml(text) {
    if (typeof text !== 'string') return text;

    // 检查是否包含数学公式分隔符
    if (text.includes('$') || text.includes('\\(') || text.includes('\\[') || text.includes('\\begin{')) {
        // 包含数学公式，不进行转义
        return text;
    }

    // 不包含数学公式，进行正常的HTML转义
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// 安全的HTML内容处理函数
function safeHtmlContent(text) {
    if (typeof text !== 'string') return text;

    // 对于包含数学公式的内容，直接返回原文本
    // KaTeX会处理渲染，浏览器会处理安全性
    if (text.includes('$') || text.includes('\\(') || text.includes('\\[') || text.includes('\\begin{')) {
        return text;
    }

    // 对于普通文本，进行HTML转义以确保安全
    return escapeHtml(text);
}
</script>

</body>
</html>