<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <!-- 试卷生成模态框 -->
    <div th:fragment="modal" class="modal fade" id="paperGenerationModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-file-alt mr-2"></i>试卷生成配置
                    </h5>
                    <!--  配置管理按钮组 -->
                    <div class="d-flex align-items-center">
                        <div class="btn-group mr-3" role="group">
                            <button type="button" class="btn btn-outline-light btn-sm" id="loadConfigBtnModal" title="加载已保存的配置">
                                <i class="fas fa-folder-open mr-1"></i>
                                <span class="d-none d-lg-inline">加载配置</span>
                            </button>
                            <button type="button" class="btn btn-outline-light btn-sm" id="saveConfigBtnModal" title="保存当前配置" disabled>
                                <i class="fas fa-save mr-1"></i>
                                <span class="d-none d-lg-inline">保存配置</span>
                            </button>
                            <button type="button" class="btn btn-outline-light btn-sm" id="manageConfigBtnModal" title="配置管理">
                                <i class="fas fa-cog mr-1"></i>
                                <span class="d-none d-lg-inline">管理</span>
                            </button>
                        </div>
                        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <form id="generatePaperForm">
                        <!-- 标题和基本信息 -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="paperTitle">试卷标题</label>
                                    <input type="text" class="form-control" id="paperTitle" required placeholder="例如：高一数学期末测试">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="paperCount">
                                        <i class="fas fa-copy mr-1"></i>生成套数
                                        <span class="badge badge-info ml-1" data-toggle="tooltip" title="一次可以生成多套不同的试卷，每套试卷的题目都不相同">?</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="paperCount" min="1" max="10" value="1" placeholder="1">
                                        <div class="input-group-append">
                                            <span class="input-group-text">套</span>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">最多可生成10套试卷</small>
                                </div>
                            </div>
                        </div>

                        <!-- 试卷版本选择 -->
                        <div class="form-group">
                            <label for="paperType">试卷版本</label>
                            <select class="form-control" id="paperType">
                                <option value="regular" selected>学生版 - 只有题目（适合考试）</option>
                                <option value="teacher">教师版 - 只有答案和解析（适合批改）</option>
                                <option value="standard">标准版 - 题目+答案+解析（适合学习）</option>
                            </select>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i>
                                <span id="paperTypeDescription">学生版只包含题目，适合考试使用</span>
                                <a href="#" class="ml-2" onclick="$('#versionHelpModal').modal('show'); return false;">
                                    <i class="fas fa-question-circle"></i> 版本说明
                                </a>
                            </small>
                        </div>

                        <!-- 选项卡导航 -->
                        <ul class="nav nav-tabs mb-3" id="paperConfigTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="question-tab" data-toggle="tab" href="#questions" role="tab">
                                    <i class="fas fa-list-ol mr-1"></i>题型设置
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="knowledge-tab" data-toggle="tab" href="#knowledge" role="tab">
                                    <i class="fas fa-brain mr-1"></i>知识点配置
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="difficulty-tab" data-toggle="tab" href="#difficulty" role="tab">
                                    <i class="fas fa-chart-bar mr-1"></i>难度分布
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="preview-tab" data-toggle="tab" href="#preview" role="tab">
                                    <i class="fas fa-eye mr-1"></i>试卷预览
                                </a>
                            </li>
                        </ul>

                        <!-- 选项卡内容 -->
                        <div class="tab-content" id="paperConfigTabContent">
                            <!-- 题型设置选项卡 -->
                            <div class="tab-pane fade show active" id="questions" role="tabpanel">
                                <div class="row">
                                    <!-- 单选题 -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">单选题</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label for="singleChoiceCount">题目数量</label>
                                                    <input type="number" class="form-control" id="singleChoiceCount" min="0" value="5">
                                                </div>
                                                <div class="form-group">
                                                    <label for="singleChoiceScore">每题分值</label>
                                                    <input type="number" class="form-control" id="singleChoiceScore" min="0" step="0.5" value="3">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 多选题 -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">多选题</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label for="multipleChoiceCount">题目数量</label>
                                                    <input type="number" class="form-control" id="multipleChoiceCount" min="0" value="3">
                                                </div>
                                                <div class="form-group">
                                                    <label for="multipleChoiceScore">每题分值</label>
                                                    <input type="number" class="form-control" id="multipleChoiceScore" min="0" step="0.5" value="3">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 判断题 -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">判断题</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label for="judgmentCount">题目数量</label>
                                                    <input type="number" class="form-control" id="judgmentCount" min="0" value="5">
                                                </div>
                                                <div class="form-group">
                                                    <label for="judgmentScore">每题分值</label>
                                                    <input type="number" class="form-control" id="judgmentScore" min="0" step="0.5" value="2">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 填空题 -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">填空题</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label for="fillCount">题目数量</label>
                                                    <input type="number" class="form-control" id="fillCount" min="0" value="3">
                                                </div>
                                                <div class="form-group">
                                                    <label for="fillScore">每题分值</label>
                                                    <input type="number" class="form-control" id="fillScore" min="0" step="0.5" value="3">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 简答题 -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">简答题</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label for="shortAnswerCount">题目数量</label>
                                                    <input type="number" class="form-control" id="shortAnswerCount" min="0" value="2">
                                                </div>
                                                <div class="form-group">
                                                    <label for="shortAnswerScore">每题分值</label>
                                                    <input type="number" class="form-control" id="shortAnswerScore" min="0" step="0.5" value="5">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 总分统计 -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">试卷统计</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="text-center">
                                                    <h3 class="mb-3">总分: <span id="totalScore">100</span></h3>
                                                    <h5>题目总数: <span id="totalQuestions">18</span></h5>
                                                </div>
                                                <div class="mt-3">
                                                    <canvas id="questionDistributionChart"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 知识点配置选项卡 -->
                            <div class="tab-pane fade" id="knowledge" role="tabpanel">
                                <div class="card mb-3">
                                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-brain mr-2"></i>知识点题量配置
                                        </h6>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-light" id="smartDistributeBtn">
                                                <i class="fas fa-magic mr-1"></i>智能分配题量
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <i class="fas fa-info-circle mr-2"></i>
                                                    <strong>配置说明：</strong>
                                                    <ul class="mb-0 mt-2">
                                                        <li>为每个知识点设置基础题量，系统会根据上方题型设置智能分配</li>
                                                        <li>可以为每个知识点单独开启简答题，并设置精确数量</li>
                                                        <li><strong>简答题数量不计入知识点总题量，是额外增加的</strong></li>
                                                        <li>知识点总题量应与上方题型设置的总题量一致</li>
                                                    </ul>
                                                </div>
                                                <div class="ml-3 flex-shrink-0">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="$('#algorithmFlowModal').modal('show')">
                                                        <i class="fas fa-brain mr-1"></i>
                                                        <span class="d-none d-lg-inline">系统工作原理</span>
                                                        <span class="d-lg-none">原理</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="knowledgePointsConfigContainer">
                                            <!-- 知识点配置将通过JavaScript动态生成 -->
                                        </div>

                                        <div class="alert alert-warning mt-3" id="knowledgePointsWarning" style="display: none;">
                                            <i class="fas fa-exclamation-triangle mr-2"></i>
                                            <span>知识点总题量与题型设置的总题量不一致，请调整。</span>
                                        </div>

                                        <div class="d-flex justify-content-between mt-3">
                                            <div>
                                                <span class="font-weight-bold">知识点基础题量：</span>
                                                <span id="totalKnowledgeQuestions">0</span>
                                            </div>
                                            <div>
                                                <span class="font-weight-bold">简答题总数（额外）：</span>
                                                <span id="totalShortAnswerQuestions" class="text-warning">0</span>
                                            </div>
                                            <div>
                                                <span class="font-weight-bold">题型设置总题量：</span>
                                                <span id="totalConfiguredQuestions">0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 难度分布选项卡 -->
                            <div class="tab-pane fade" id="difficulty" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">难度分布设置</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label for="easyPercentage">简单题目比例 (%)</label>
                                                    <input type="number" class="form-control" id="easyPercentage" min="0" max="100" value="30">
                                                </div>
                                                <div class="form-group">
                                                    <label for="mediumPercentage">中等题目比例 (%)</label>
                                                    <input type="number" class="form-control" id="mediumPercentage" min="0" max="100" value="50">
                                                </div>
                                                <div class="form-group">
                                                    <label for="hardPercentage">困难题目比例 (%)</label>
                                                    <input type="number" class="form-control" id="hardPercentage" min="0" max="100" value="20">
                                                </div>
                                                <div class="alert alert-warning mt-3" id="difficultyWarning" style="display: none;">
                                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                                    <span>难度分布总和应为100%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">难度分布预览</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="difficultyChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 试卷预览选项卡 -->
                            <div class="tab-pane fade" id="preview" role="tabpanel">
                                <!-- 全宽实时预览 -->
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="fas fa-eye mr-2"></i>试卷题目预览
                                                <small class="ml-2">(含选项、答案、解析、难度)</small>
                                            </h6>
                                            <div class="preview-stats">
                                                <span class="badge badge-light mr-2">
                                                    <i class="fas fa-calculator mr-1"></i>
                                                    总分: <span id="previewTotalScore">0</span>
                                                </span>
                                                <span class="badge badge-light">
                                                    <i class="fas fa-list-ol mr-1"></i>
                                                    题目数: <span id="previewTotalQuestions">0</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body p-0" style="max-height: 700px; overflow-y: auto;">
                                        <!-- 实时预览容器 -->
                                        <div id="realTimePreviewContainer">
                                            <!-- 实时预览内容将通过JavaScript动态生成 -->
                                            <div class="text-center text-muted py-5">
                                                <i class="fas fa-file-alt fa-4x mb-3"></i>
                                                <h5>试卷预览</h5>
                                                <p class="mb-0">配置题型和知识点后，这里将显示实际的题目内容</p>
                                                <small class="text-muted">包含题目、选项、答案、解析和难度信息</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-info mr-2" id="previewPaperBtn">
                        <i class="fas fa-eye mr-1"></i>预览试卷
                    </button>
                    <button type="button" class="btn btn-primary" id="submitGeneratePaperBtn">
                        <i class="fas fa-magic mr-1"></i>
                        <span id="generateBtnText">生成试卷</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
