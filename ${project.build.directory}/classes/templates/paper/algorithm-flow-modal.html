<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <!-- 智能组卷流程介绍模态框 -->
    <div th:fragment="modal" class="modal fade" id="algorithmFlowModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-gradient-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-brain mr-2"></i>智能组卷系统 - 工作原理揭秘
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-0">
                    <!-- 流程步骤容器 -->
                    <div class="algorithm-flow-container">
                        <!-- 标题介绍 -->
                        <div class="flow-intro text-center py-4 bg-light">
                            <div class="container">
                                <h3 class="mb-3">
                                    <i class="fas fa-magic text-primary mr-2"></i>
                                    当您点击"生成试卷"后，系统在做什么？
                                </h3>
                                <p class="lead text-muted mb-0">
                                    我们的AI智能组卷系统会在几秒钟内为您精心挑选最合适的题目组合
                                </p>
                            </div>
                        </div>

                        <!-- 流程步骤 -->
                        <div class="flow-steps py-4">
                            <div class="container">
                                <!-- 步骤1：输入分析 -->
                                <div class="flow-step" data-step="1">
                                    <div class="row align-items-center mb-5">
                                        <div class="col-md-6">
                                            <div class="step-content">
                                                <div class="step-number">
                                                    <span class="number">1</span>
                                                    <i class="fas fa-search-plus step-icon"></i>
                                                </div>
                                                <h4 class="step-title">智能分析您的需求</h4>
                                                <p class="step-description">
                                                    系统首先深度解析您的配置，包括知识点选择、题型数量、分值设置和难度分布。
                                                    就像一位经验丰富的老师在仔细研读考试大纲，确保完全理解您的教学目标。
                                                </p>
                                                <div class="step-details">
                                                    <div class="detail-item">
                                                        <i class="fas fa-brain text-primary"></i>
                                                        <span>解析知识点配置和题量分配</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-list-ol text-success"></i>
                                                        <span>转换题型格式并验证约束</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-chart-bar text-warning"></i>
                                                        <span>计算目标分数和难度分布</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-cogs text-info"></i>
                                                        <span>准备算法参数和约束条件</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="step-visual">
                                                <div class="visual-card input-analysis">
                                                    <div class="card-header">
                                                        <i class="fas fa-cogs"></i>
                                                        <span>需求分析中...</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="analysis-item">
                                                            <span class="label">知识点</span>
                                                            <span class="value">5个已选择</span>
                                                        </div>
                                                        <div class="analysis-item">
                                                            <span class="label">题型</span>
                                                            <span class="value">单选10道 + 多选5道</span>
                                                        </div>
                                                        <div class="analysis-item">
                                                            <span class="label">难度</span>
                                                            <span class="value">简单30% 中等50% 困难20%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤2：题库搜索 -->
                                <div class="flow-step" data-step="2">
                                    <div class="row align-items-center mb-5">
                                        <div class="col-md-6 order-md-2">
                                            <div class="step-content">
                                                <div class="step-number">
                                                    <span class="number">2</span>
                                                    <i class="fas fa-database step-icon"></i>
                                                </div>
                                                <h4 class="step-title">智能筛选和多样性过滤</h4>
                                                <p class="step-description">
                                                    系统从题库中精准筛选符合条件的题目，并应用多样性过滤确保题目质量。
                                                    就像图书管理员不仅找到相关书籍，还要确保内容丰富、避免重复。
                                                </p>
                                                <div class="step-details">
                                                    <div class="detail-item">
                                                        <i class="fas fa-database text-primary"></i>
                                                        <span>按知识点从题库获取候选题目</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-shield-alt text-success"></i>
                                                        <span>应用重用间隔和多样性过滤</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-balance-scale text-warning"></i>
                                                        <span>题型感知过滤确保数量充足</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-expand-arrows-alt text-info"></i>
                                                        <span>必要时扩展题目池满足需求</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 order-md-1">
                                            <div class="step-visual">
                                                <div class="visual-card database-search">
                                                    <div class="card-header">
                                                        <i class="fas fa-filter"></i>
                                                        <span>智能筛选中...</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="search-progress">
                                                            <div class="progress-item">
                                                                <span>知识点筛选</span>
                                                                <div class="progress">
                                                                    <div class="progress-bar bg-success" style="width: 100%"></div>
                                                                </div>
                                                                <span class="count">1,247题</span>
                                                            </div>
                                                            <div class="progress-item">
                                                                <span>多样性过滤</span>
                                                                <div class="progress">
                                                                    <div class="progress-bar bg-info" style="width: 85%"></div>
                                                                </div>
                                                                <span class="count">892题</span>
                                                            </div>
                                                            <div class="progress-item">
                                                                <span>题型平衡</span>
                                                                <div class="progress">
                                                                    <div class="progress-bar bg-warning" style="width: 70%"></div>
                                                                </div>
                                                                <span class="count">634题</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤3：智能优化 -->
                                <div class="flow-step" data-step="3">
                                    <div class="row align-items-center mb-5">
                                        <div class="col-md-6">
                                            <div class="step-content">
                                                <div class="step-number">
                                                    <span class="number">3</span>
                                                    <i class="fas fa-brain step-icon"></i>
                                                </div>
                                                <h4 class="step-title">混合智能算法优化</h4>
                                                <p class="step-description">
                                                    系统采用混合策略：精确分配器处理简答题，遗传算法优化基础题型。
                                                    就像专业团队分工合作，每个算法都专注于自己最擅长的领域。
                                                </p>
                                                <div class="step-details">
                                                    <div class="detail-item">
                                                        <i class="fas fa-crosshairs text-primary"></i>
                                                        <span>精确分配器处理知识点简答题</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-dna text-success"></i>
                                                        <span>遗传算法优化基础题型组合</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-chart-line text-warning"></i>
                                                        <span>多目标适应度函数评估方案</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-tools text-info"></i>
                                                        <span>修复算子确保约束满足</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="step-visual">
                                                <div class="visual-card ai-optimization">
                                                    <div class="card-header">
                                                        <i class="fas fa-cogs"></i>
                                                        <span>混合算法运行中...</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="optimization-rounds">
                                                            <div class="round-item">
                                                                <span class="round">精确分配</span>
                                                                <div class="score-bar">
                                                                    <div class="score" style="width: 100%">简答题</div>
                                                                </div>
                                                            </div>
                                                            <div class="round-item">
                                                                <span class="round">遗传算法</span>
                                                                <div class="score-bar">
                                                                    <div class="score" style="width: 85%">基础题型</div>
                                                                </div>
                                                            </div>
                                                            <div class="round-item active">
                                                                <span class="round">动态规划</span>
                                                                <div class="score-bar">
                                                                    <div class="score" style="width: 95%">分数调整</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="optimization-status">
                                                            <i class="fas fa-check-circle text-success"></i>
                                                            <span>算法协同完成！</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤4：质量检验 -->
                                <div class="flow-step" data-step="4">
                                    <div class="row align-items-center mb-5">
                                        <div class="col-md-6 order-md-2">
                                            <div class="step-content">
                                                <div class="step-number">
                                                    <span class="number">4</span>
                                                    <i class="fas fa-shield-alt step-icon"></i>
                                                </div>
                                                <h4 class="step-title">动态规划精确调整</h4>
                                                <p class="step-description">
                                                    系统使用动态规划算法对试卷进行最终优化，确保分数精确匹配且题型分布完美。
                                                    就像精密仪器的最后校准，让每个细节都达到完美状态。
                                                </p>
                                                <div class="step-details">
                                                    <div class="detail-item">
                                                        <i class="fas fa-calculator text-primary"></i>
                                                        <span>动态规划精确匹配目标分数</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-shield-alt text-success"></i>
                                                        <span>保持题型分布约束不变</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-clock text-warning"></i>
                                                        <span>超时保护确保系统响应</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-check-double text-info"></i>
                                                        <span>验证最终结果质量</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 order-md-1">
                                            <div class="step-visual">
                                                <div class="visual-card quality-check">
                                                    <div class="card-header">
                                                        <i class="fas fa-clipboard-check"></i>
                                                        <span>质量检验中...</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="check-items">
                                                            <div class="check-item passed">
                                                                <i class="fas fa-check-circle"></i>
                                                                <span>题型数量匹配</span>
                                                                <span class="status">✓ 通过</span>
                                                            </div>
                                                            <div class="check-item passed">
                                                                <i class="fas fa-check-circle"></i>
                                                                <span>难度分布合理</span>
                                                                <span class="status">✓ 通过</span>
                                                            </div>
                                                            <div class="check-item passed">
                                                                <i class="fas fa-check-circle"></i>
                                                                <span>知识点覆盖</span>
                                                                <span class="status">✓ 通过</span>
                                                            </div>
                                                            <div class="check-item passed">
                                                                <i class="fas fa-check-circle"></i>
                                                                <span>分数分布</span>
                                                                <span class="status">✓ 通过</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤5：完成输出 -->
                                <div class="flow-step" data-step="5">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <div class="step-content">
                                                <div class="step-number">
                                                    <span class="number">5</span>
                                                    <i class="fas fa-gift step-icon"></i>
                                                </div>
                                                <h4 class="step-title">智能试卷完美生成</h4>
                                                <p class="step-description">
                                                    经过多层算法优化和质量验证，您的专属智能试卷完成了！
                                                    每道题都经过精心计算和验证，确保试卷科学、公平、高质量。
                                                </p>
                                                <div class="step-details">
                                                    <div class="detail-item">
                                                        <i class="fas fa-check-circle text-primary"></i>
                                                        <span>最终质量验证和题型检查</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-chart-pie text-success"></i>
                                                        <span>统计分析和性能报告</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-file-alt text-warning"></i>
                                                        <span>生成多版本试卷格式</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <i class="fas fa-rocket text-info"></i>
                                                        <span>秒级完成，即时可用</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="step-visual">
                                                <div class="visual-card final-output">
                                                    <div class="card-header">
                                                        <i class="fas fa-check"></i>
                                                        <span>试卷生成完成！</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="output-summary">
                                                            <div class="summary-item">
                                                                <i class="fas fa-list-ol text-primary"></i>
                                                                <span class="label">总题数</span>
                                                                <span class="value">25题</span>
                                                            </div>
                                                            <div class="summary-item">
                                                                <i class="fas fa-calculator text-success"></i>
                                                                <span class="label">总分值</span>
                                                                <span class="value">100分</span>
                                                            </div>
                                                            <div class="summary-item">
                                                                <i class="fas fa-clock text-warning"></i>
                                                                <span class="label">用时</span>
                                                                <span class="value">2.3秒</span>
                                                            </div>
                                                            <div class="summary-item">
                                                                <i class="fas fa-star text-info"></i>
                                                                <span class="label">质量评分</span>
                                                                <span class="value">95分</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 技术优势介绍 -->
                        <div class="tech-advantages py-4 bg-light">
                            <div class="container">
                                <h4 class="text-center mb-4">
                                    <i class="fas fa-rocket text-primary mr-2"></i>
                                    为什么我们的系统这么智能？
                                </h4>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <div class="advantage-card">
                                            <div class="advantage-icon">
                                                <i class="fas fa-rocket"></i>
                                            </div>
                                            <h6>混合算法</h6>
                                            <p>遗传算法+精确分配+动态规划，多算法协同工作</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="advantage-card">
                                            <div class="advantage-icon">
                                                <i class="fas fa-shield-alt"></i>
                                            </div>
                                            <h6>多样性控制</h6>
                                            <p>智能重用间隔和知识点级别多样性过滤</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="advantage-card">
                                            <div class="advantage-icon">
                                                <i class="fas fa-crosshairs"></i>
                                            </div>
                                            <h6>精确匹配</h6>
                                            <p>题型数量、分数分布、难度比例精确控制</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="advantage-card">
                                            <div class="advantage-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <h6>性能优化</h6>
                                            <p>并行计算、超时保护、智能缓存机制</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>关闭
                    </button>
                    <button type="button" class="btn btn-primary" onclick="startDemoAnimation()">
                        <i class="fas fa-play mr-1"></i>观看演示动画
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
