<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Unified Navigation Fragment</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .navbar-apple {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .navbar-brand {
            font-weight: 600;
            font-size: 1.2rem;
        }
        .nav-link {
            font-weight: 500;
            border-radius: 8px;
            margin: 0 4px;
            transition: all 0.2s ease;
        }
        .nav-link:hover {
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }
        .nav-link.active {
            background-color: rgba(0, 123, 255, 0.15);
            color: #0d6efd !important;
        }
        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            padding: 8px;
        }
        .dropdown-item {
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.2s ease;
        }
        .dropdown-item:hover {
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateX(4px);
        }
        .badge-notification {
            font-size: 0.7rem;
            min-width: 18px;
            height: 18px;
            border-radius: 9px;
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid rgba(0, 123, 255, 0.2);
        }
    </style>
</head>
<body>

<!-- 统一导航栏片段 -->
<nav th:fragment="unified-navbar" class="navbar navbar-expand-lg navbar-apple sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand text-primary" href="/">
            <i class="bi bi-mortarboard-fill me-2"></i>
            麦子教育系统
        </a>
        
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/main/chat" data-page="chat">
                        <i class="bi bi-chat-dots text-primary me-1"></i> 智能出题
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/topics/upload-topics" data-page="upload">
                        <i class="bi bi-cloud-upload text-success me-1"></i> 上传题目
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/topics/bank" data-page="bank">
                        <i class="bi bi-collection text-info me-1"></i> 题库管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/paper/generate" data-page="generate">
                        <i class="bi bi-file-earmark-text text-warning me-1"></i> 智能组卷
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/main/books" data-page="books">
                        <i class="bi bi-book text-secondary me-1"></i> 教材资源
                    </a>
                </li>
                <!-- 管理员菜单 -->
                <li class="nav-item dropdown" id="adminMenu" style="display: none;">
                    <a class="nav-link dropdown-toggle text-danger" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-shield-check me-1"></i> 管理后台
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/admin/topics/audit">
                            <i class="bi bi-check-circle text-warning me-2"></i> 题目审核
                            <span class="badge bg-warning text-dark ms-2" id="pendingAuditBadge" style="display: none;">0</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/users">
                            <i class="bi bi-people text-primary me-2"></i> 用户管理
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/stats">
                            <i class="bi bi-graph-up text-success me-2"></i> 系统统计
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/admin/permissions">
                            <i class="bi bi-key text-danger me-2"></i> 权限管理
                        </a></li>
                    </ul>
                </li>
            </ul>
            
            <!-- 消息通知 -->
            <ul class="navbar-nav me-3">
                <li class="nav-item dropdown">
                    <a class="nav-link position-relative" href="#" id="messageDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-bell fs-5"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge badge-notification bg-danger" id="unreadCount" style="display: none;">
                            0
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 320px; max-height: 400px; overflow-y: auto;">
                        <li><h6 class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>系统消息</span>
                            <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">全部已读</button>
                        </h6></li>
                        <div id="messageList">
                            <li><span class="dropdown-item-text text-muted text-center py-3">暂无消息</span></li>
                        </div>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center fw-bold" href="/messages">查看全部消息</a></li>
                    </ul>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <img src="/images/default-avatar.png" alt="头像" class="user-avatar me-2" id="userAvatar">
                        <span id="username">用户</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><div class="dropdown-header">
                            <div class="d-flex align-items-center">
                                <img src="/images/default-avatar.png" alt="头像" class="user-avatar me-2" id="userAvatarDropdown">
                                <div>
                                    <div class="fw-bold" id="usernameDropdown">用户</div>
                                    <small class="text-muted" id="userRole">角色</small>
                                </div>
                            </div>
                        </div></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/user/profile">
                            <i class="bi bi-person me-2"></i> 个人资料
                        </a></li>
                        <li><a class="dropdown-item" href="/topics/audit/my">
                            <i class="bi bi-list-check me-2"></i> 我的审核
                        </a></li>
                        <li><a class="dropdown-item" href="/paper-configs">
                            <i class="bi bi-gear me-2"></i> 配置管理
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i> 退出登录
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<script>
// 导航栏初始化
document.addEventListener('DOMContentLoaded', function() {
    initUnifiedNavbar();
});

function initUnifiedNavbar() {
    // 获取当前用户信息
    getCurrentUser();
    // 获取未读消息数量
    getUnreadMessageCount();
    // 获取最近消息
    getRecentMessages();
    // 设置当前页面高亮
    setActiveNavItem();
    // 获取待审核数量（管理员）
    getPendingAuditCount();
}

function getCurrentUser() {
    const token = getAuthToken();
    if (!token) return;
    
    fetch('/api/user/current', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data) {
            const user = data.data;
            document.getElementById('username').textContent = user.username;
            document.getElementById('usernameDropdown').textContent = user.username;
            
            // 设置角色显示
            const roleText = getRoleText(user.role);
            document.getElementById('userRole').textContent = roleText;

            // 只有管理员才显示管理员菜单
            if (user.role === 1) { // 管理员
                document.getElementById('adminMenu').style.display = 'block';
                console.log('管理员用户已登录，显示管理后台菜单');
            } else {
                // 确保非管理员用户看不到管理员菜单
                document.getElementById('adminMenu').style.display = 'none';
            }
            
            // 设置头像
            if (user.avatar) {
                document.getElementById('userAvatar').src = user.avatar;
                document.getElementById('userAvatarDropdown').src = user.avatar;
            }
        }
    })
    .catch(error => console.error('获取用户信息失败:', error));
}

function getRoleText(role) {
    switch(role) {
        case 1: return '管理员';
        case 2: return '普通用户';
        case 3: return '教师';
        default: return '用户';
    }
}

function getUnreadMessageCount() {
    const token = getAuthToken();
    if (!token) return;
    
    fetch('/api/messages/unread-count', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data > 0) {
            const badge = document.getElementById('unreadCount');
            badge.textContent = data.data;
            badge.style.display = 'block';
        }
    })
    .catch(error => console.error('获取未读消息数量失败:', error));
}

function getRecentMessages() {
    const token = getAuthToken();
    if (!token) return;
    
    fetch('/api/messages/recent-notices?limit=5', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data && data.data.length > 0) {
            renderRecentMessages(data.data);
        }
    })
    .catch(error => console.error('获取最近消息失败:', error));
}

function renderRecentMessages(messages) {
    const messageList = document.getElementById('messageList');
    messageList.innerHTML = '';
    
    messages.forEach(message => {
        const li = document.createElement('li');
        li.innerHTML = `
            <a class="dropdown-item ${!message.isRead ? 'fw-bold' : ''}" href="/messages/${message.id}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="small">${message.title}</div>
                        <div class="text-muted" style="font-size: 0.8rem;">${formatDateTime(message.createdAt)}</div>
                    </div>
                    ${!message.isRead ? '<span class="badge bg-primary">新</span>' : ''}
                </div>
            </a>
        `;
        messageList.appendChild(li);
    });
}

function getPendingAuditCount() {
    const token = getAuthToken();
    if (!token) return;
    
    fetch('/api/admin/topics/audit/pending-count', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data > 0) {
            const badge = document.getElementById('pendingAuditBadge');
            badge.textContent = data.data;
            badge.style.display = 'inline';
        }
    })
    .catch(error => console.debug('获取待审核数量失败:', error)); // 非管理员会失败，使用debug级别
}

function setActiveNavItem() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link[data-page]');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
        }
    });
}

function markAllAsRead() {
    const token = getAuthToken();
    if (!token) return;
    
    fetch('/api/messages/read-all', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 刷新消息列表
            getUnreadMessageCount();
            getRecentMessages();
        }
    })
    .catch(error => console.error('标记全部已读失败:', error));
}

function logout() {
    const token = getAuthToken();
    if (token) {
        fetch('/api/user/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(() => {
            localStorage.removeItem('authToken');
            window.location.href = '/auth/login';
        })
        .catch(error => {
            console.error('退出登录失败:', error);
            localStorage.removeItem('authToken');
            window.location.href = '/auth/login';
        });
    } else {
        window.location.href = '/auth/login';
    }
}

function formatDateTime(dateTime) {
    const date = new Date(dateTime);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
        return '刚刚';
    } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        return date.toLocaleDateString('zh-CN');
    }
}

// 确保getAuthToken函数可用
function getAuthToken() {
    return localStorage.getItem('authToken') || '';
}
</script>

</body>
</html>
