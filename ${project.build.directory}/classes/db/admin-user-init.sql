-- 管理员用户初始化脚本
-- 创建默认管理员账号用于测试

-- 插入管理员用户（密码需要根据实际加密方式调整）
-- 注意：这里的密码是示例，实际部署时请修改为安全的密码
INSERT INTO `user` (
    `username`, 
    `password`, 
    `email`, 
    `phone`, 
    `role`, 
    `status`, 
    `created_at`, 
    `updated_at`, 
    `deleted`, 
    `avatar`, 
    `bio`
) VALUES (
    'admin', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.uxRUk/2m', -- 密码: admin123 (BCrypt加密)
    '<EMAIL>', 
    '13800138000', 
    1,  -- 管理员角色
    1,  -- 正常状态
    NOW(), 
    NOW(), 
    0,  -- 未删除
    NULL, 
    '系统管理员账号'
) ON DUPLICATE KEY UPDATE 
    `role` = 1,
    `status` = 1,
    `updated_at` = NOW();

-- 插入测试普通用户
INSERT INTO `user` (
    `username`, 
    `password`, 
    `email`, 
    `phone`, 
    `role`, 
    `status`, 
    `created_at`, 
    `updated_at`, 
    `deleted`, 
    `avatar`, 
    `bio`
) VALUES (
    'user001', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.uxRUk/2m', -- 密码: admin123
    '<EMAIL>', 
    '13800138001', 
    2,  -- 普通用户角色
    1,  -- 正常状态
    NOW(), 
    NOW(), 
    0,  -- 未删除
    NULL, 
    '测试普通用户'
) ON DUPLICATE KEY UPDATE 
    `role` = 2,
    `status` = 1,
    `updated_at` = NOW();

-- 插入测试教师用户
INSERT INTO `user` (
    `username`, 
    `password`, 
    `email`, 
    `phone`, 
    `role`, 
    `status`, 
    `created_at`, 
    `updated_at`, 
    `deleted`, 
    `avatar`, 
    `bio`
) VALUES (
    'teacher001', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.uxRUk/2m', -- 密码: admin123
    '<EMAIL>', 
    '13800138002', 
    3,  -- 教师角色
    1,  -- 正常状态
    NOW(), 
    NOW(), 
    0,  -- 未删除
    NULL, 
    '测试教师用户'
) ON DUPLICATE KEY UPDATE 
    `role` = 3,
    `status` = 1,
    `updated_at` = NOW();

-- 显示创建的用户信息
SELECT 
    id,
    username,
    email,
    CASE role 
        WHEN 1 THEN '管理员'
        WHEN 2 THEN '普通用户'
        WHEN 3 THEN '教师'
        ELSE '未知'
    END as role_name,
    CASE status
        WHEN 1 THEN '正常'
        WHEN 0 THEN '禁用'
        ELSE '未知'
    END as status_name,
    created_at
FROM `user` 
WHERE username IN ('admin', 'user001', 'teacher001')
ORDER BY role, username;
