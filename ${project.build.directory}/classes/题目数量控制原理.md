## 题目数量控制原理

### 1. 候选池预处理阶段的数量估算

````java path=src/main/java/com/edu/maizi_edu_sys/service/engine/PaperGenerationEngine.java mode=EXCERPT
// 智能扩展题目池以满足题型要求
// 当选定知识点的题目不足时，从全局题库中补充相应题型的题目
private List<Topic> expandQuestionPoolIfNeeded(List<Topic> currentPool, Map<String, Integer> typeTargetCounts) {
    // 统计当前题目池中各题型的数量
    Map<String, Integer> currentTypeCounts = new HashMap<>();
    for (Topic topic : currentPool) {
        String type = mapTopicType(topic.getType());
        currentTypeCounts.put(type, currentTypeCounts.getOrDefault(type, 0) + 1);
    }

    // 检查哪些题型需要补充
    Map<String, Integer> shortfalls = new HashMap<>();
    boolean needsExpansion = false;

    for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
        String type = entry.getKey();
        int required = entry.getValue();
        int available = currentTypeCounts.getOrDefault(type, 0);

        if (required > 0 && available < required) {
            int shortfall = required - available;
            shortfalls.put(type, shortfall);
            needsExpansion = true;
            log.warn("题型 {} 不足: 需要 {} 题，当前仅有 {} 题，缺少 {} 题",
                    type, required, available, shortfall);
        }
    }
}
````

### 2. 遗传算法中的题目数量控制机制

#### 2.1 强制类型匹配初始化

````java path=src/main/java/com/edu/maizi_edu_sys/service/engine/GeneticSolver.java mode=EXCERPT
// 强制类型数量匹配的初始化方法
// 生成的每个染色体都确保精确包含目标数量的每种题型
private List<Chromosome> enforceExactTypeCountsInitialization(List<Topic> availableQuestions, Map<String, Integer> typeTargetCounts) {
    // 检查是否有足够的每种类型题目
    boolean hasSufficientTopics = true;
    log.info("检查题型约束要求:");
    for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
        String type = entry.getKey();
        int requiredCount = entry.getValue();
        List<Integer> availableIndexes = indexesByType.getOrDefault(type, Collections.emptyList());

        log.info("- 题型 {}: 需要 {} 题，可用 {} 题", type, requiredCount, availableIndexes.size());

        if (requiredCount > 0 && availableIndexes.size() < requiredCount) {
            log.warn("❌ 题型 {} 题目不足，需要 {} 题，但只有 {} 题可用",
                   type, requiredCount, availableIndexes.size());
            hasSufficientTopics = false;
        }
    }

    if (!hasSufficientTopics) {
        log.warn("无法创建满足题型数量要求的初始种群");
        return Collections.emptyList();
    }
}
````

#### 2.2 随机初始化的概率控制

````java path=src/main/java/com/edu/maizi_edu_sys/service/engine/GeneticSolver.java mode=EXCERPT
// 初始化种群。随机生成一组染色体
private List<Chromosome> initializePopulation(int geneLength) {
    for (int i = 0; i < POPULATION_SIZE; i++) {
        BitSet gene = new BitSet(geneLength);
        for (int j = 0; j < geneLength; j++) {
            // 以约50%的概率决定是否选择该题目
            // 这个概率可以根据实际情况调整
            if (random.nextDouble() < 0.5) {
                gene.set(j);
            }
        }
        population.add(new Chromosome(gene));
    }
}
````

## 题目数量控制的具体策略

### 1. 预处理阶段的数量保障

**候选池扩展机制**：
```java
// 系统会自动检测题目不足并扩展候选池
if (required > 0 && available < required) {
    // 从全局题库补充该题型的题目
    List<Topic> globalTopicsOfType = topicMapper.selectByType(type);
    // 过滤重复，随机选择需要的数量
    Collections.shuffle(availableTopics);
    int actualAdded = Math.min(needed, availableTopics.size());
}
```

### 2. 初始化阶段的精确控制

**三层初始化策略确保数量**：

1. **启发式种子（10%种群）**：使用贪心算法生成高质量种子，确保精确的题型数量
2. **强制类型匹配（80%种群）**：每个染色体都严格按照要求选择题目数量
3. **随机补充（10%种群）**：用于增加多样性，但会通过修复算子调整

### 3. 进化过程中的数量维护

#### 3.1 类型保持交叉

```java
// 类型保持交叉操作 - 确保子代染色体维持与父代相同的题型分布
private Chromosome typePreservingCrossover(Chromosome parent1, Chromosome parent2,
                                           List<Topic> questions,
                                           Map<String, Integer> typeTargetCounts) {
    // 对每种题型分别进行交叉
    for (String type : typeTargetCounts.keySet()) {
        int targetCount = typeTargetCounts.getOrDefault(type, 0);
        if (targetCount <= 0) continue;

        // 获取两个父代中该题型的题目索引
        Set<Integer> indicesOfTypeParent1 = typeIndicesParent1.getOrDefault(type, new HashSet<>());
        Set<Integer> indicesOfTypeParent2 = typeIndicesParent2.getOrDefault(type, new HashSet<>());

        // 随机选择交叉点，范围在0和目标数量之间
        int crossPoint = random.nextInt(targetCount + 1);

        // 从第一个父代选择前crossPoint个元素
        for (int i = 0; i < Math.min(crossPoint, parent1IndicesList.size()); i++) {
            childGene.set(parent1IndicesList.get(i));
        }

        // 从第二个父代选择剩余元素，直到达到目标数量
        int remaining = targetCount - Math.min(crossPoint, parent1IndicesList.size());
        for (int i = 0; i < Math.min(remaining, parent2IndicesList.size()); i++) {
            childGene.set(parent2IndicesList.get(i));
        }
    }
}
```

#### 3.2 类型保持变异

```java
// 类型保持变异操作 - 确保变异后的染色体维持所需的题型数量分布
private void typePreservingMutate(Chromosome chromosome, List<Topic> questions, Map<String, Integer> typeTargetCounts) {
    // 对每种题型单独变异，确保变异后保持相同数量
    for (String type : typeTargetCounts.keySet()) {
        int targetCount = typeTargetCounts.getOrDefault(type, 0);
        if (targetCount <= 0) continue;

        Set<Integer> indicesOfType = typeIndices.getOrDefault(type, new HashSet<>());
        int currentCount = indicesOfType.size();

        // 如果当前数量等于目标数量，执行1对1替换变异
        if (currentCount == targetCount && random.nextDouble() < MUTATION_RATE) {
            // 查找此类型的所有候选题目索引
            Set<Integer> allCandidateIndicesOfType = getAllIndicesOfType(questions, type);
            
            // 移除当前已选索引，得到可以替换的候选集
            Set<Integer> candidatesForReplacement = new HashSet<>(allCandidateIndicesOfType);
            candidatesForReplacement.removeAll(indicesOfType);

            if (!candidatesForReplacement.isEmpty() && !indicesOfType.isEmpty()) {
                // 随机选择一个当前索引移除
                int indexToRemove = currentIndicesList.get(random.nextInt(currentIndicesList.size()));
                
                // 随机选择一个新索引添加
                int indexToAdd = candidatesList.get(random.nextInt(candidatesList.size()));

                // 执行替换 - 保持题目数量不变
                gene.clear(indexToRemove);
                gene.set(indexToAdd);
            }
        }
    }
}
```

#### 3.3 修复算子

```java
// 应用修复算子
if (repairOperator != null) {
    int repairSteps = repairOperator.repairChromosome(
        child, availableQuestions, typeTargetCounts,
        targetScore, null, enhancementDataMap
    );
    if (repairSteps > 0) {
        log.debug("Applied {} repair steps to chromosome", repairSteps);
    }
}
```

## 题目数量控制的完整流程图

```tex
1. 候选池构建阶段
   ├── 统计各题型需求量
   ├── 检查候选池中各题型数量
   ├── 如果不足，从全局题库补充
   └── 确保候选池满足最低要求

2. 初始化种群阶段
   ├── 启发式种子生成（精确数量）
   ├── 强制类型匹配初始化（精确数量）
   └── 随机初始化补充（需要修复）

3. 进化过程中
   ├── 类型保持交叉（维持数量）
   ├── 类型保持变异（1对1替换）
   └── 修复算子（纠正错误）

4. 后处理阶段
   ├── 动态规划调整（微调分数）
   ├── 动态题型调整（最终保障）
   └── 质量检查和验证
```

## 关键技术保障

### 1. 概率控制策略

**随机初始化的概率调整**：

```java
// 根据目标题目数量动态调整选择概率
double selectionProbability = (double) totalTargetCount / availableQuestions.size();
if (random.nextDouble() < selectionProbability) {
    gene.set(j);
}
```

### 2. 容错机制

**题目不足时的处理**：

- 从全局题库补充
- 题型转换（将多余题型转换为不足题型）
- 降级处理（适当减少某些题型要求）

### 3. 实时监控

**每代进化后的数量检查**：

```java
// 检查当前种群中每个个体的题型分布
for (Chromosome chromosome : population) {
    Map<String, Integer> typeDistribution = getTypeDistribution(chromosome, questions);
    // 如果不满足要求，标记为低适应度
    if (!satisfiesTypeConstraints(typeDistribution, typeTargetCounts)) {
        chromosome.setFitness(chromosome.getFitness() * 0.1); // 大幅降低适应度
    }
}
```

## 总结

我们的BitSet编码系统通过以下机制确保题目数量的精确控制：

1. **预处理保障**：候选池扩展机制确保有足够的题目可选
2. **初始化保障**：三层初始化策略确保大部分个体满足数量要求
3. **进化保障**：类型保持的交叉和变异操作维持题目数量
4. **修复保障**：修复算子实时纠正不合规的个体
5. **后处理保障**：动态调整机制作为最后的安全网