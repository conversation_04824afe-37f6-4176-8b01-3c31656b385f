/**
 * 智能分配题目功能
 * 将上方设置的总体量（各题型数量）智能分配给下方的各个知识点
 */

console.log('Smart distribution script loaded at:', new Date().toISOString());

// 显示分配选项模态框
function showDistributionOptionsModal() {
    console.log('Showing distribution options modal');

    // 检查是否已经有手动设置
    const hasManualSettings = checkForManualEdits();
    console.log('Manual settings detected:', hasManualSettings);

    // 检查模态框是否已存在
    if ($('#distribution-options-modal').length === 0) {
        console.log('Creating distribution options modal');

        // 创建弹窗HTML
        const modalHtml = `
        <div class="modal fade" id="distribution-options-modal" tabindex="-1" role="dialog" aria-labelledby="distributionOptionsLabel" aria-hidden="true">
          <div class="modal-dialog" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="distributionOptionsLabel">智能分配选项</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                ${hasManualSettings ? '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle mr-2"></i>检测到您已手动设置了部分知识点的题目数量，继续分配将覆盖这些设置。</div>' : ''}
                <div class="distribution-method">
                  <h6>分配方式</h6>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="distribution-method" id="equal-distribution" value="equal" checked>
                    <label class="form-check-label" for="equal-distribution">
                      平均分配
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="distribution-method" id="weighted-distribution" value="weighted">
                    <label class="form-check-label" for="weighted-distribution">
                      按知识点重要性分配
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="distribution-method" id="proportional-distribution" value="proportional">
                    <label class="form-check-label" for="proportional-distribution">
                      按知识点可用题目数量分配
                    </label>
                  </div>
                </div>

                <div class="importance-settings mt-3" id="importance-settings" style="display: none;">
                  <h6>知识点重要性设置</h6>
                  <div class="knowledge-importance-list">
                    <!-- 动态生成知识点重要性设置 -->
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="cancel-distribution">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-distribution">确认分配</button>
              </div>
            </div>
          </div>
        </div>
        `;

        // 添加弹窗到页面
        $('body').append(modalHtml);
    } else {
        console.log('Distribution options modal already exists');
    }

    // 初始化知识点重要性设置
    initImportanceSettings(getKnowledgePoints());

    // 绑定分配方式切换事件
    $('input[name="distribution-method"]').off('change').on('change', function() {
        const importanceSettings = $('#importance-settings');
        importanceSettings.toggle(this.value === 'weighted');
    });

    // 绑定确认分配按钮点击事件
    $('#confirm-distribution').off('click').on('click', function() {
        console.log('Confirm distribution button clicked');
        executeDistribution();
        $('#distribution-options-modal').modal('hide');
    });

    // 显示模态框
    $('#distribution-options-modal').modal('show');
}

// 初始化知识点重要性设置
function initImportanceSettings(knowledgePoints) {
    const importanceSettingsContainer = $('.knowledge-importance-list');
    importanceSettingsContainer.empty();

    // 为每个知识点创建重要性设置
    knowledgePoints.forEach(kp => {
        const importanceItem = $(`
          <div class="importance-item mb-2">
            <div class="d-flex align-items-center">
              <span class="knowledge-name flex-grow-1">${escapeHtml(kp.name)}</span>
              <div class="importance-slider d-flex align-items-center">
                <input type="range" min="1" max="5" value="3"
                       class="form-control-range mr-2" data-knowledge-id="${kp.id}">
                <span class="importance-value badge badge-primary">3</span>
              </div>
            </div>
          </div>
        `);

        importanceSettingsContainer.append(importanceItem);

        // 设置滑块事件
        const slider = importanceItem.find('input[type="range"]');
        const valueDisplay = importanceItem.find('.importance-value');
        slider.on('input', function() {
            valueDisplay.text(this.value);
        });
    });
}

// HTML转义函数
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 执行分配
function executeDistribution() {
    // 获取全局题型设置
    const globalTypeCounts = getGlobalTypeCounts();

    // 获取知识点列表
    const knowledgePoints = getKnowledgePoints();

    // 获取分配方式
    const distributionMethod = document.querySelector('input[name="distribution-method"]:checked').value;

    // 根据分配方式执行不同的分配算法
    let distributionResult;

    switch (distributionMethod) {
        case 'equal':
            distributionResult = distributeEqually(knowledgePoints, globalTypeCounts);
            break;
        case 'weighted':
            // 获取知识点重要性设置
            const knowledgePointsWithImportance = knowledgePoints.map(kp => {
                const importanceSlider = document.querySelector(`.importance-slider input[data-knowledge-id="${kp.id}"]`);
                return {
                    ...kp,
                    importance: parseInt(importanceSlider.value)
                };
            });
            distributionResult = distributeByImportance(knowledgePointsWithImportance, globalTypeCounts);
            break;
        case 'proportional':
            // 显示加载提示
            showToast('正在获取知识点可用题目数量...', 'info');

            // 获取知识点可用题目数量
            fetchKnowledgePointsAvailableQuestions(knowledgePoints)
                .then(knowledgePointsWithAvailable => {
                    distributionResult = distributeByAvailableQuestions(knowledgePointsWithAvailable, globalTypeCounts);
                    applyDistributionResult(distributionResult);
                })
                .catch(error => {
                    console.error('获取知识点可用题目数量失败', error);
                    showToast('获取知识点可用题目数量失败，将使用平均分配', 'error');
                    distributionResult = distributeEqually(knowledgePoints, globalTypeCounts);
                    applyDistributionResult(distributionResult);
                });
            return; // 异步操作，提前返回
        default:
            distributionResult = distributeEqually(knowledgePoints, globalTypeCounts);
    }

    // 应用分配结果
    applyDistributionResult(distributionResult);
}

/**
 * 平均分配算法
 * @param {Array} knowledgePoints - 知识点列表
 * @param {Object} globalTypeCounts - 全局题型数量 {singleChoice: 10, multipleChoice: 10, ...}
 * @returns {Array} - 分配结果
 */
function distributeEqually(knowledgePoints, globalTypeCounts) {
    // 计算总题量
    const totalQuestions = Object.values(globalTypeCounts).reduce((sum, count) => sum + count, 0);

    // 处理知识点数量多于总题量的情况
    if (knowledgePoints.length > totalQuestions) {
        showToast(`警告：知识点数量(${knowledgePoints.length})超过总题量(${totalQuestions})，部分知识点将不会分配到题目`, 'warning');
    }

    // 计算每个知识点的基础题量
    const baseCount = Math.floor(totalQuestions / knowledgePoints.length);
    let remainder = totalQuestions % knowledgePoints.length;

    // 分配结果
    const result = [];

    // 为每个知识点分配题目
    knowledgePoints.forEach((kp, index) => {
        // 该知识点的总题量 = 基础题量 + 可能的余数
        const kpTotalCount = baseCount + (remainder > 0 ? 1 : 0);
        if (remainder > 0) remainder--;

        // 分配各题型题目
        const typeDistribution = distributeTypesByRatio(kpTotalCount, globalTypeCounts);

        // 记录分配结果
        result.push({
            knowledgeId: kp.id,
            questionCount: kpTotalCount,
            typeDistribution: typeDistribution,
            includeShortAnswer: typeDistribution.shortAnswer > 0
        });
    });

    return result;
}

/**
 * 按重要性分配算法
 * @param {Array} knowledgePoints - 知识点列表，包含重要性设置
 * @param {Object} globalTypeCounts - 全局题型数量
 * @returns {Array} - 分配结果
 */
function distributeByImportance(knowledgePoints, globalTypeCounts) {
    // 计算总题量
    const totalQuestions = Object.values(globalTypeCounts).reduce((sum, count) => sum + count, 0);

    // 计算总重要性
    const totalImportance = knowledgePoints.reduce((sum, kp) => sum + kp.importance, 0);

    // 分配结果
    const result = [];

    // 为每个知识点分配题目
    let allocatedTotal = 0;
    knowledgePoints.forEach((kp, index) => {
        // 按重要性比例计算题量
        let kpTotalCount = Math.round((kp.importance / totalImportance) * totalQuestions);

        // 确保每个知识点至少有1题
        kpTotalCount = Math.max(1, kpTotalCount);

        // 防止最后一个知识点分配过多
        if (index === knowledgePoints.length - 1) {
            kpTotalCount = Math.min(kpTotalCount, totalQuestions - allocatedTotal);
        } else {
            allocatedTotal += kpTotalCount;
        }

        // 分配各题型题目
        const typeDistribution = distributeTypesByRatio(kpTotalCount, globalTypeCounts);

        // 记录分配结果
        result.push({
            knowledgeId: kp.id,
            questionCount: kpTotalCount,
            typeDistribution: typeDistribution,
            includeShortAnswer: typeDistribution.shortAnswer > 0
        });
    });

    return result;
}

/**
 * 按可用题目数量分配算法
 * @param {Array} knowledgePoints - 知识点列表，包含可用题目数量
 * @param {Object} globalTypeCounts - 全局题型数量
 * @returns {Array} - 分配结果
 */
function distributeByAvailableQuestions(knowledgePoints, globalTypeCounts) {
    // 计算总题量
    const totalQuestions = Object.values(globalTypeCounts).reduce((sum, count) => sum + count, 0);

    // 计算总可用题目数
    const totalAvailable = knowledgePoints.reduce((sum, kp) => sum + kp.availableQuestionCount, 0);

    // 如果总可用题目数为0，回退到平均分配
    if (totalAvailable === 0) {
        showToast('所有知识点都没有可用题目，将使用平均分配', 'warning');
        return distributeEqually(knowledgePoints, globalTypeCounts);
    }

    // 分配结果
    const result = [];

    // 为每个知识点分配题目
    let allocatedTotal = 0;
    knowledgePoints.forEach((kp, index) => {
        // 按可用题目比例计算题量
        let kpTotalCount = Math.round((kp.availableQuestionCount / totalAvailable) * totalQuestions);

        // 确保不超过可用题目数量
        kpTotalCount = Math.min(kpTotalCount, kp.availableQuestionCount);

        // 确保每个知识点至少有1题（如果有可用题目）
        kpTotalCount = kp.availableQuestionCount > 0 ? Math.max(1, kpTotalCount) : 0;

        // 防止最后一个知识点分配过多
        if (index === knowledgePoints.length - 1) {
            kpTotalCount = Math.min(kpTotalCount, totalQuestions - allocatedTotal);
        } else {
            allocatedTotal += kpTotalCount;
        }

        // 分配各题型题目
        const typeDistribution = distributeTypesByRatio(kpTotalCount, globalTypeCounts);

        // 记录分配结果
        result.push({
            knowledgeId: kp.id,
            questionCount: kpTotalCount,
            typeDistribution: typeDistribution,
            includeShortAnswer: typeDistribution.shortAnswer > 0
        });
    });

    return result;
}

/**
 * 按比例分配各题型题目
 * @param {Number} totalCount - 知识点总题量
 * @param {Object} globalTypeCounts - 全局题型数量
 * @returns {Object} - 各题型分配结果
 */
function distributeTypesByRatio(totalCount, globalTypeCounts) {
    // 计算全局总题量
    const globalTotal = Object.values(globalTypeCounts).reduce((sum, count) => sum + count, 0);

    // 分配结果
    const result = {};

    // 已分配题目数量
    let allocated = 0;

    // 为每种题型分配题目
    const typeEntries = Object.entries(globalTypeCounts);
    typeEntries.forEach(([type, count], index) => {
        if (index === typeEntries.length - 1) {
            // 最后一种题型，分配剩余的所有题目
            result[type] = totalCount - allocated;
        } else {
            // 按比例分配
            const typeCount = Math.round((count / globalTotal) * totalCount);
            result[type] = typeCount;
            allocated += typeCount;
        }
    });

    // 处理简答题特殊情况
    if (result.shortAnswer !== undefined) {
        // 如果有简答题，确保开启了简答题开关的知识点至少分配1题
        if (result.shortAnswer > 0) {
            result.shortAnswer = Math.max(1, result.shortAnswer);

            // 调整其他题型，确保总数不变
            const adjustmentNeeded = result.shortAnswer - Math.round((globalTypeCounts.shortAnswer / globalTotal) * totalCount);
            if (adjustmentNeeded > 0) {
                // 从其他题型中减少题目
                adjustOtherTypes(result, adjustmentNeeded);
            }
        }
    }

    return result;
}

/**
 * 调整其他题型数量
 * @param {Object} typeDistribution - 题型分布
 * @param {Number} adjustment - 需要调整的数量
 */
function adjustOtherTypes(typeDistribution, adjustment) {
    // 按题量从大到小排序，优先从数量多的题型中减少
    const sortedTypes = Object.entries(typeDistribution)
        .filter(([type]) => type !== 'shortAnswer')
        .sort(([, countA], [, countB]) => countB - countA);

    let remaining = adjustment;
    for (let i = 0; i < sortedTypes.length && remaining > 0; i++) {
        const [type, count] = sortedTypes[i];
        const reduce = Math.min(count - 1, remaining); // 确保至少保留1题
        if (reduce > 0) {
            typeDistribution[type] -= reduce;
            remaining -= reduce;
        }
    }
}

/**
 * 应用分配结果
 * @param {Array} distributionResult - 分配结果
 */
function applyDistributionResult(distributionResult) {
    // 更新知识点题目数量和简答题开关
    distributionResult.forEach(result => {
        const knowledgePointItem = $(`.knowledge-point-item[data-id="${result.knowledgeId}"]`);
        if (knowledgePointItem.length > 0) {
            // 更新总题量
            const questionCountInput = knowledgePointItem.find('.knowledge-question-count');
            if (questionCountInput.length > 0) {
                questionCountInput.val(result.questionCount);
                // 触发变更事件，确保UI和数据同步
                questionCountInput.trigger('change');
            }

            // 更新简答题开关
            const shortAnswerSwitch = knowledgePointItem.find('.include-short-answer');
            if (shortAnswerSwitch.length > 0) {
                shortAnswerSwitch.prop('checked', result.includeShortAnswer);
                // 触发变更事件，确保UI和数据同步
                shortAnswerSwitch.trigger('change');
            }
        }
    });

    // 显示成功提示
    Swal.fire({
        icon: 'success',
        title: '分配成功',
        text: '已智能分配题目到各知识点',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
    });

    // 验证分配结果
    validatePaperConfiguration();
}

/**
 * 验证分配结果
 */
function validateDistribution() {
    // 获取全局题型设置
    const globalTypeCounts = getGlobalTypeCounts();
    const globalTotal = Object.values(globalTypeCounts).reduce((sum, count) => sum + count, 0);

    // 获取所有知识点的题目总数
    const knowledgePointsTotal = Array.from(document.querySelectorAll('.question-count-input'))
        .reduce((sum, input) => sum + parseInt(input.value || 0), 0);

    // 验证总数是否匹配
    if (globalTotal !== knowledgePointsTotal) {
        showToast(`警告：全局题型总数(${globalTotal})与知识点题目总数(${knowledgePointsTotal})不匹配！`, 'warning');
    } else {
        // 更新分配状态指示器
        updateDistributionStatus(true);
    }
}

/**
 * 获取全局题型设置
 * @returns {Object} - 全局题型数量 {singleChoice: 10, multipleChoice: 10, ...}
 */
function getGlobalTypeCounts() {
    const result = {};

    // 获取各题型数量
    result.singleChoice = parseInt($('#customSingleChoiceCount').val()) || 0;
    result.multipleChoice = parseInt($('#customMultipleChoiceCount').val()) || 0;
    result.judgment = parseInt($('#customJudgmentCount').val()) || 0;
    result.fillBlank = parseInt($('#customFillCount').val()) || 0;
    result.shortAnswer = parseInt($('#customShortAnswerCount').val()) || 0;
    result.subjective = parseInt($('#customSubjectiveCount').val()) || 0;

    return result;
}

/**
 * 获取知识点列表
 * @returns {Array} - 知识点列表
 */
function getKnowledgePoints() {
    const result = [];

    // 获取所有知识点项
    $('#knowledgePointsContainer .knowledge-point-item').each(function() {
        const id = $(this).data('id');
        const name = $(this).find('.card-title').text().trim();

        if (id && name) {
            result.push({
                id: id,
                name: name
            });
        }
    });

    return result;
}

/**
 * 检查是否有手动修改
 * @returns {Boolean} - 是否有手动修改
 */
function checkForManualEdits() {
    console.log('Checking for manual edits');

    try {
        // 获取所有知识点项
        const knowledgePointItems = $('#knowledgePointsContainer .knowledge-point-item');
        console.log('Found knowledge point items:', knowledgePointItems.length);

        if (knowledgePointItems.length === 0) {
            return false;
        }

        // 检查是否有任何知识点的题目数量不为0
        let hasManualEdits = false;

        knowledgePointItems.each(function() {
            const questionCountInput = $(this).find('.knowledge-question-count');
            if (questionCountInput.length > 0) {
                const value = parseInt(questionCountInput.val() || 0);
                if (value > 0) {
                    console.log('Found manual edit with value:', value);
                    hasManualEdits = true;
                    return false; // 跳出each循环
                }
            }
        });

        return hasManualEdits;
    } catch (error) {
        console.error('Error checking for manual edits:', error);
        return false;
    }
}

// 这些函数已经不需要了，因为我们使用SweetAlert2显示提示

/**
 * 获取知识点可用题目数量
 * @param {Array} knowledgePoints - 知识点列表
 * @returns {Promise} - Promise对象
 */
function fetchKnowledgePointsAvailableQuestions(knowledgePoints) {
    return new Promise((resolve, reject) => {
        // 构建知识点ID列表
        const knowledgeIds = knowledgePoints.map(kp => kp.id);

        // 发送请求
        $.ajax({
            url: '/api/knowledge-points/available-questions',
            type: 'GET',
            data: { knowledgeIds: knowledgeIds.join(',') },
            dataType: 'json',
            success: function(data) {
                // 将可用题目数量添加到知识点对象
                const knowledgePointsWithAvailable = knowledgePoints.map(kp => {
                    const availableData = data.find(item => item.knowledgeId == kp.id);
                    return {
                        ...kp,
                        availableQuestionCount: availableData ? availableData.totalAvailable : 0,
                        typeCountMap: availableData ? availableData.typeCountMap : {}
                    };
                });

                resolve(knowledgePointsWithAvailable);
            },
            error: function(xhr, status, error) {
                console.error('获取知识点可用题目数量失败', error);
                reject(error);
            }
        });
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('Smart distribution initialization started');

    // 只使用方法3: 使用事件委托
    setupEventDelegation();

    // 添加测试函数，用于验证智能分配功能是否正常工作
    window.testSmartDistribution = function() {
        console.log('Testing smart distribution functionality');

        // 检查按钮是否存在
        const autoDistributeBtn = document.getElementById('autoDistributeBtn');
        console.log('Auto distribute button exists:', !!autoDistributeBtn);

        // 检查模态框是否存在
        const customPaperModal = document.getElementById('customPaperModal');
        console.log('Custom paper modal exists:', !!customPaperModal);

        // 模拟知识点列表
        const knowledgePoints = [
            { id: 1, name: '知识点1' },
            { id: 2, name: '知识点2' },
            { id: 3, name: '知识点3' }
        ];

        // 模拟全局题型设置
        const globalTypeCounts = {
            singleChoice: 10,
            multipleChoice: 5,
            judgment: 5,
            fillBlank: 3,
            shortAnswer: 2
        };

        // 测试平均分配
        console.log('平均分配结果:', distributeEqually(knowledgePoints, globalTypeCounts));

        // 测试按重要性分配
        const knowledgePointsWithImportance = knowledgePoints.map((kp, index) => ({
            ...kp,
            importance: index + 2 // 1, 2, 3 -> 3, 4, 5
        }));
        console.log('按重要性分配结果:', distributeByImportance(knowledgePointsWithImportance, globalTypeCounts));

        // 测试按可用题目数量分配
        const knowledgePointsWithAvailable = knowledgePoints.map((kp, index) => ({
            ...kp,
            availableQuestionCount: (index + 1) * 10 // 10, 20, 30
        }));
        console.log('按可用题目数量分配结果:', distributeByAvailableQuestions(knowledgePointsWithAvailable, globalTypeCounts));

        // 尝试手动触发按钮点击
        if (autoDistributeBtn) {
            console.log('Attempting to trigger button click programmatically');
            autoDistributeBtn.click();
        }

        return '测试完成，请查看控制台输出';
    };
});

// 方法1: 使用MutationObserver监听DOM变化
function setupMutationObserver() {
    console.log('Setting up MutationObserver');

    // 创建一个观察器实例
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];

                    // 检查是否是模态框或包含模态框的节点
                    if (node.id === 'customPaperModal' ||
                        (node.querySelector && node.querySelector('#customPaperModal'))) {
                        console.log('Custom paper modal detected via MutationObserver');

                        // 等待DOM更新完成
                        setTimeout(function() {
                            attachToAutoDistributeButton();
                        }, 500);
                    }

                    // 检查是否是按钮或包含按钮的节点
                    if (node.id === 'autoDistributeBtn' ||
                        (node.querySelector && node.querySelector('#autoDistributeBtn'))) {
                        // 按钮已检测到，不需要重复日志

                        // 等待DOM更新完成
                        setTimeout(function() {
                            attachToAutoDistributeButton();
                        }, 500);
                    }
                }
            }
        });
    });

    // 配置观察选项
    const config = { childList: true, subtree: true };

    // 开始观察
    observer.observe(document.body, config);
}

// 方法2: 监听模态框显示事件
function setupModalShownListener() {
    console.log('Setting up modal shown listener');

    // 监听模态框显示事件
    $(document).on('shown.bs.modal', '#customPaperModal', function() {
        console.log('Custom paper modal shown event triggered');
        attachToAutoDistributeButton();
    });
}

// 方法3: 使用事件委托
function setupEventDelegation() {
    console.log('Setting up event delegation');

    // 先移除可能存在的事件处理程序，避免重复绑定
    $(document).off('click', '#autoDistributeBtn');

    // 使用事件委托监听按钮点击
    $(document).on('click', '#autoDistributeBtn', function(event) {
        console.log('Auto distribute button clicked via delegation');

        // 阻止默认行为和事件冒泡
        event.preventDefault();
        event.stopPropagation();

        // 检查是否有知识点
        const knowledgePoints = $('#knowledgePointsContainer .knowledge-point-item');
        if (knowledgePoints.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: '无法分配',
                text: '请先添加知识点再进行分配',
                confirmButtonText: '确定'
            });
            return false;
        }

        // 显示分配选项模态框
        showDistributionOptionsModal();

        return false;
    });

    // 直接创建一个备用按钮，以防原始按钮不可用
    setTimeout(function() {
        // 检查是否已经存在备用按钮，避免重复创建
        if ($('#backupAutoDistributeBtn').length === 0 &&
            $('#autoDistributeBtn').length === 0 &&
            $('#knowledgePointsContainer').length > 0) {

            console.log('Creating backup smart distribution button');

            // 找到知识点配置卡片的头部
            const cardHeader = $('.card-header:contains("知识点配置")');
            if (cardHeader.length > 0) {
                // 检查是否已经有按钮容器
                let buttonContainer = cardHeader.find('div');
                if (buttonContainer.length === 0) {
                    // 创建按钮容器
                    buttonContainer = $('<div></div>');
                    cardHeader.append(buttonContainer);
                }

                // 创建智能分配按钮
                const backupButton = $(`
                    <button type="button" class="btn btn-success btn-sm mr-2" id="backupAutoDistributeBtn" title="智能分配题目到各知识点">
                        <i class="fas fa-magic mr-1"></i>智能分配题目
                    </button>
                `);

                // 添加按钮到容器
                buttonContainer.prepend(backupButton);

                // 绑定点击事件（使用one而不是on，确保只触发一次）
                backupButton.one('click', function(event) {
                    console.log('Backup auto distribute button clicked');
                    event.preventDefault();
                    event.stopPropagation();

                    // 检查是否有知识点
                    const knowledgePoints = $('#knowledgePointsContainer .knowledge-point-item');
                    if (knowledgePoints.length === 0) {
                        Swal.fire({
                            icon: 'warning',
                            title: '无法分配',
                            text: '请先添加知识点再进行分配',
                            confirmButtonText: '确定'
                        });
                        return false;
                    }

                    // 显示分配选项模态框
                    showDistributionOptionsModal();

                    return false;
                });

                console.log('Backup smart distribution button created');
            }
        } else {
            console.log('Backup button already exists or not needed');
        }
    }, 1000); // 延迟1秒，确保DOM已加载
}

// 附加到自动分配按钮 - 已废弃，不再使用
function attachToAutoDistributeButton() {
    // 此函数已废弃，不再使用
    // 我们现在使用事件委托来处理按钮点击事件
}