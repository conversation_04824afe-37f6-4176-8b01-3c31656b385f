/**
 * 试卷实时预览功能
 * 提供真实题目的实时预览，根据用户配置动态更新
 */

class PaperRealTimePreview {
    constructor() {
        this.previewContainer = null;
        this.loadingIndicator = null;
        this.errorContainer = null;
        this.statsContainer = null;
        this.warningsContainer = null;
        this.currentPreviewData = null;
        this.debounceTimer = null;
        this.isPreviewEnabled = false;
    }

    /**
     * 初始化实时预览功能
     * @param {string} containerId - 预览容器的ID
     * @param {Object} options - 配置选项
     */
    init(containerId, options = {}) {
        this.previewContainer = document.getElementById(containerId);
        if (!this.previewContainer) {
            console.error('预览容器未找到:', containerId);
            return;
        }

        this.options = {
            debounceDelay: 1000, // 防抖延迟时间（毫秒）
            previewLimit: 3,     // 每种题型预览题目数量
            autoUpdate: true,    // 是否自动更新预览
            showStats: true,     // 是否显示统计信息
            showWarnings: true,  // 是否显示警告信息
            ...options
        };

        this.createPreviewUI();
        this.bindEvents();

        console.log('实时预览功能已初始化');
    }

    /**
     * 创建预览界面
     */
    createPreviewUI() {
        this.previewContainer.innerHTML = `
            <div class="real-time-preview">
                <div class="preview-header">
                    <h4>
                        <i class="fas fa-eye"></i>
                        实时预览
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="togglePreview">
                            <i class="fas fa-sync-alt"></i>
                            刷新预览
                        </button>
                        <div class="form-check form-switch d-inline-block ms-3">
                            <input class="form-check-input" type="checkbox" id="autoPreviewSwitch" checked>
                            <label class="form-check-label" for="autoPreviewSwitch">自动更新</label>
                        </div>
                    </h4>
                </div>

                <div class="preview-loading d-none" id="previewLoading">
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在生成预览...</p>
                    </div>
                </div>

                <div class="preview-error d-none" id="previewError">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span class="error-message">预览生成失败</span>
                    </div>
                </div>

                <div class="preview-warnings" id="previewWarnings"></div>

                <div class="preview-stats" id="previewStats"></div>

                <div class="preview-content" id="previewContent">
                    <div class="text-center text-muted p-4">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>请配置知识点和题型后查看预览</p>
                    </div>
                </div>
            </div>
        `;

        this.loadingIndicator = document.getElementById('previewLoading');
        this.errorContainer = document.getElementById('previewError');
        this.statsContainer = document.getElementById('previewStats');
        this.warningsContainer = document.getElementById('previewWarnings');
        this.contentContainer = document.getElementById('previewContent');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 刷新预览按钮
        const refreshBtn = document.getElementById('togglePreview');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.updatePreview();
            });
        }

        // 自动更新开关
        const autoSwitch = document.getElementById('autoPreviewSwitch');
        if (autoSwitch) {
            autoSwitch.addEventListener('change', (e) => {
                this.options.autoUpdate = e.target.checked;
                if (this.options.autoUpdate) {
                    this.updatePreview();
                }
            });
        }
    }

    /**
     * 触发预览更新（带防抖）
     */
    triggerPreviewUpdate() {
        if (!this.options.autoUpdate) return;

        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        this.debounceTimer = setTimeout(() => {
            this.updatePreview();
        }, this.options.debounceDelay);
    }

    /**
     * 更新预览内容
     */
    async updatePreview() {
        try {
            const previewRequest = this.buildPreviewRequest();
            if (!previewRequest) {
                this.showEmptyState();
                return;
            }

            this.showLoading();
            this.hideError();

            const response = await this.fetchPreview(previewRequest);
            if (response.success) {
                this.currentPreviewData = response.data;
                this.renderPreview(response.data);
            } else {
                this.showError(response.message || '预览生成失败');
            }
        } catch (error) {
            console.error('预览更新失败:', error);
            this.showError('网络错误，请稍后重试');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 构建预览请求参数
     */
    buildPreviewRequest() {
        console.log('🚀 [预览] 开始构建预览请求参数...');

        // 获取当前页面的配置数据
        const knowledgePointConfigs = this.getKnowledgePointConfigs();
        const typeCountMap = this.getTypeCountMap();
        const typeScoreMap = this.getTypeScoreMap();

        console.log('🔍 [预览] 获取到的配置数据:');
        console.log('  - 知识点配置:', knowledgePointConfigs);
        console.log('  - 题型数量配置:', typeCountMap);
        console.log('  - 题型分值配置:', typeScoreMap);

        if (!knowledgePointConfigs || knowledgePointConfigs.length === 0) {
            console.warn('⚠️ [预览] 知识点配置为空，无法构建预览请求');
            return null;
        }

        if (!typeCountMap || Object.keys(typeCountMap).length === 0) {
            console.warn('⚠️ [预览] 题型数量配置为空，无法构建预览请求');
            return null;
        }

        const request = {
            title: this.getPaperTitle() || '试卷预览',
            knowledgePointConfigs: knowledgePointConfigs,
            typeCountMap: typeCountMap,
            typeScoreMap: typeScoreMap || {},
            previewLimit: this.options.previewLimit
        };

        console.log('✅ [预览] 预览请求参数构建完成:', request);
        return request;
    }

    /**
     * 获取知识点配置（需要根据实际页面结构实现）
     */
    getKnowledgePointConfigs() {
        // 这里需要根据实际的页面结构来获取知识点配置
        // 示例实现，需要根据实际情况调整
        const configs = [];

        // 假设知识点配置存储在某个全局变量或DOM元素中
        if (window.selectedKnowledgePoints) {
            window.selectedKnowledgePoints.forEach(kp => {
                configs.push({
                    knowledgeId: kp.id,
                    questionCount: kp.questionCount || 0,
                    includeShortAnswer: kp.includeShortAnswer || false
                });
            });
        }

        return configs;
    }

    /**
     * 获取题型数量配置
     */
    getTypeCountMap() {
        const typeCountMap = {};

        // 从页面表单中获取题型数量配置
        const typeInputs = document.querySelectorAll('[data-question-type]');
        typeInputs.forEach(input => {
            const type = input.getAttribute('data-question-type');
            const count = parseInt(input.value) || 0;
            if (count > 0) {
                typeCountMap[type] = count;
            }
        });

        return typeCountMap;
    }

    /**
     * 获取题型分值配置
     */
    getTypeScoreMap() {
        const typeScoreMap = {};

        // 从页面表单中获取题型分值配置
        const scoreInputs = document.querySelectorAll('[data-question-score]');
        scoreInputs.forEach(input => {
            const type = input.getAttribute('data-question-score');
            const score = parseInt(input.value) || 0;
            if (score > 0) {
                typeScoreMap[type] = score;
            }
        });

        return typeScoreMap;
    }

    /**
     * 获取试卷标题
     */
    getPaperTitle() {
        const titleInput = document.querySelector('#paperTitle, [name="title"]');
        return titleInput ? titleInput.value : '';
    }

    /**
     * 发送预览请求
     */
    async fetchPreview(requestData) {
        console.log('📡 [预览] 发送预览请求到后端:', requestData);

        const response = await fetch('/api/papers/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        console.log('📡 [预览] 后端响应状态:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json();
        console.log('📡 [预览] 后端响应数据:', responseData);

        return responseData;
    }

    /**
     * 渲染预览内容
     */
    renderPreview(previewData) {
        this.renderStats(previewData.stats);
        this.renderWarnings(previewData.warnings);
        this.renderTopics(previewData.previewTopicsByType);

        // 渲染数学公式
        this.renderMathFormulas();
    }

    /**
     * 渲染数学公式
     */
    renderMathFormulas() {
        if (typeof renderMathInElement !== 'undefined') {
            try {
                renderMathInElement(this.contentContainer, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\(', right: '\\)', display: false},
                        {left: '\\[', right: '\\]', display: true}
                    ],
                    throwOnError: false,
                    errorColor: '#cc0000',
                    strict: false
                });
                console.log('KaTeX数学公式渲染完成');
            } catch (e) {
                console.warn('KaTeX渲染失败:', e);
            }
        } else if (typeof katex !== 'undefined') {
            // 如果没有auto-render，使用基础katex
            this.contentContainer.querySelectorAll('.math-formula').forEach(element => {
                try {
                    const formula = element.textContent;
                    katex.render(formula, element, {
                        throwOnError: false,
                        errorColor: '#cc0000',
                        strict: false
                    });
                } catch (e) {
                    console.warn('KaTeX渲染失败:', e);
                }
            });
        } else {
            console.log('KaTeX未加载，跳过数学公式渲染');
        }
    }

    /**
     * HTML转义函数
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 渲染统计信息
     */
    renderStats(stats) {
        if (!stats) return;

        // 更新页面头部的统计信息
        const totalScoreElement = document.getElementById('previewTotalScore');
        const totalQuestionsElement = document.getElementById('previewTotalQuestions');

        if (totalScoreElement) {
            totalScoreElement.textContent = stats.expectedTotalScore || stats.actualTotalScore || 0;
        }

        if (totalQuestionsElement) {
            totalQuestionsElement.textContent = stats.totalRequestedQuestions || 0;
        }

        // 如果有统计容器且启用了统计显示，也更新它
        if (this.options.showStats && this.statsContainer) {
            const statsHtml = `
                <div class="preview-stats-card mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-chart-bar"></i>
                                详细统计信息
                            </h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stat-item text-center p-2 bg-light rounded">
                                        <div class="stat-value h5 mb-1 text-primary">${stats.totalRequestedQuestions || 0}</div>
                                        <div class="stat-label small text-muted">请求题目数</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item text-center p-2 bg-light rounded">
                                        <div class="stat-value h5 mb-1 text-info">${stats.totalAvailableQuestions || 0}</div>
                                        <div class="stat-label small text-muted">可用题目数</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item text-center p-2 bg-light rounded">
                                        <div class="stat-value h5 mb-1 text-success">${stats.expectedTotalScore || 0}</div>
                                        <div class="stat-label small text-muted">预期总分</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item text-center p-2 bg-light rounded">
                                        <div class="stat-value h5 mb-1 text-warning">${stats.actualTotalScore || 0}</div>
                                        <div class="stat-label small text-muted">实际总分</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            this.statsContainer.innerHTML = statsHtml;
        } else if (this.statsContainer) {
            this.statsContainer.innerHTML = '';
        }
    }

    /**
     * 渲染警告信息
     */
    renderWarnings(warnings) {
        if (!this.options.showWarnings || !warnings || warnings.length === 0) {
            this.warningsContainer.innerHTML = '';
            return;
        }

        const warningsHtml = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>注意事项：</strong>
                <ul class="mb-0 mt-2">
                    ${warnings.map(warning => `<li>${warning}</li>`).join('')}
                </ul>
            </div>
        `;

        this.warningsContainer.innerHTML = warningsHtml;
    }

    /**
     * 渲染题目预览
     */
    renderTopics(topicsByType) {
        console.log('🎨 [预览] 开始渲染题目预览...');
        console.log('🎨 [预览] 接收到的题目数据:', topicsByType);

        if (!topicsByType || Object.keys(topicsByType).length === 0) {
            console.warn('⚠️ [预览] 题目数据为空，显示空状态');
            this.showEmptyState();
            return;
        }

        let contentHtml = '<div class="preview-topics p-3">';

        // 题型顺序（支持新旧两种格式）
        const typeOrder = [
            'SINGLE_CHOICE', 'singleChoice',
            'MULTIPLE_CHOICE', 'multipleChoice',
            'JUDGE', 'judgment',
            'FILL', 'fillBlank',
            'SHORT', 'shortAnswer',
            'SUBJECTIVE', 'subjective',
            'GROUP', 'groupQuestion'
        ];

        // 已处理的题型，避免重复显示
        const processedTypes = new Set();

        typeOrder.forEach(type => {
            if (topicsByType[type] && topicsByType[type].length > 0 && !processedTypes.has(type)) {
                const typeName = this.getTypeDisplayName(type);
                console.log(`🎨 [预览] 渲染题型: ${type} (${typeName}), 题目数量: ${topicsByType[type].length}`);
                contentHtml += this.renderTopicType(type, typeName, topicsByType[type]);
                processedTypes.add(type);

                // 标记对应的旧格式也已处理
                const alternativeType = this.getAlternativeType(type);
                if (alternativeType) {
                    processedTypes.add(alternativeType);
                }
            }
        });

        contentHtml += '</div>';
        this.contentContainer.innerHTML = contentHtml;

        console.log('✅ [预览] 题目预览渲染完成');
    }

    /**
     * 渲染单个题型的题目
     */
    renderTopicType(type, typeName, topics) {
        let html = `
            <div class="topic-type-section mb-5">
                <div class="topic-type-header mb-4">
                    <h4 class="topic-type-title d-flex align-items-center">
                        <span class="badge badge-primary badge-lg mr-3">
                            <i class="fas fa-list-ol mr-1"></i>
                            ${typeName}
                        </span>
                        <small class="text-muted">共 ${topics.length} 题</small>
                    </h4>
                </div>
                <div class="topics-list">
        `;

        topics.forEach((topic, index) => {
            html += this.renderSingleTopic(topic, index + 1, type);
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    /**
     * 渲染单个题目
     */
    renderSingleTopic(topic, questionNumber, type) {
        let optionsHtml = '';
        let answerHtml = '';
        let parseHtml = '';

        // 处理选择题选项
        if ((type === 'SINGLE_CHOICE' || type === 'MULTIPLE_CHOICE' || type === 'singleChoice' || type === 'multipleChoice') && topic.options) {
            try {
                const options = typeof topic.options === 'string' ? JSON.parse(topic.options) : topic.options;
                console.log('解析选项数据:', options);

                if (Array.isArray(options)) {
                    // 新格式：[{key: "A", name: "选项内容"}, ...]
                    optionsHtml = '<div class="topic-options mt-3">';
                    options.forEach((option, idx) => {
                        let label, text;

                        if (typeof option === 'object' && option !== null) {
                            // 支持多种对象格式
                            label = option.key || String.fromCharCode(65 + idx);
                            text = option.name || option.text || option.value || '';
                        } else {
                            // 简单字符串格式
                            label = String.fromCharCode(65 + idx);
                            text = option;
                        }

                        optionsHtml += `
                            <div class="option-item mb-2 p-2 border rounded">
                                <strong class="option-label text-primary">${label}.</strong>
                                <span class="option-text">${this.escapeHtml(text)}</span>
                            </div>
                        `;
                    });
                    optionsHtml += '</div>';
                } else if (typeof options === 'object' && options !== null) {
                    // 旧格式：{A: "选项内容", B: "选项内容", ...}
                    optionsHtml = '<div class="topic-options mt-3">';
                    Object.keys(options).forEach(key => {
                        optionsHtml += `
                            <div class="option-item mb-2 p-2 border rounded">
                                <strong class="option-label text-primary">${key}.</strong>
                                <span class="option-text">${this.escapeHtml(options[key])}</span>
                            </div>
                        `;
                    });
                    optionsHtml += '</div>';
                } else {
                    console.warn('选项格式不正确:', options);
                    optionsHtml = '<div class="text-muted">(选项格式不正确)</div>';
                }
            } catch (e) {
                console.error('解析题目选项失败:', e);
                console.error('原始选项数据:', topic.options);
                optionsHtml = '<div class="text-muted">(选项解析失败)</div>';
            }
        }

        // 处理答案
        if (topic.answer) {
            answerHtml = `
                <div class="topic-answer mt-3">
                    <div class="alert alert-success">
                        <strong><i class="fas fa-check-circle mr-1"></i>正确答案：</strong>
                        ${this.escapeHtml(topic.answer)}
                    </div>
                </div>
            `;
        }

        // 处理解析
        if (topic.parse) {
            parseHtml = `
                <div class="topic-parse mt-3">
                    <div class="alert alert-info">
                        <strong><i class="fas fa-lightbulb mr-1"></i>题目解析：</strong>
                        <div class="mt-2">${this.escapeHtml(topic.parse)}</div>
                    </div>
                </div>
            `;
        }

        // 获取题型显示名称
        const typeDisplayName = this.getTypeDisplayName(type);

        const topicHtml = `
            <div class="topic-item card mb-4 shadow-sm" data-topic-id="${topic.id || 'unknown'}">
                <div class="card-body">
                    <div class="topic-header d-flex justify-content-between align-items-start mb-3">
                        <div class="topic-number-type">
                            <span class="topic-number badge badge-primary badge-lg mr-2">${questionNumber}</span>
                            <span class="topic-type badge badge-secondary">${typeDisplayName}</span>
                        </div>
                        <div class="topic-meta-badges">
                            <span class="topic-difficulty badge ${this.getDifficultyClass(topic.difficulty)} mr-1">
                                <i class="fas fa-star mr-1"></i>${this.getDifficultyText(topic.difficulty)}
                            </span>
                            ${topic.score ? `<span class="badge badge-warning"><i class="fas fa-coins mr-1"></i>${topic.score}分</span>` : ''}
                        </div>
                    </div>

                    <div class="topic-title mb-3">
                        <h6 class="mb-0">${this.escapeHtml(topic.title || '题目内容')}</h6>
                    </div>

                    ${optionsHtml}
                    ${answerHtml}
                    ${parseHtml}

                    <div class="topic-footer mt-3 pt-3 border-top">
                        <small class="text-muted">
                            <i class="fas fa-book mr-1"></i>来源: ${this.escapeHtml(topic.source || '未知')}
                            ${topic.knowId ? `<span class="ml-3"><i class="fas fa-tag mr-1"></i>知识点ID: ${topic.knowId}</span>` : ''}
                        </small>
                    </div>
                </div>
            </div>
        `;

        return topicHtml;
    }

    /**
     * 获取题型显示名称
     */
    getTypeDisplayName(type) {
        const typeMap = {
            'SINGLE_CHOICE': '单选题',
            'MULTIPLE_CHOICE': '多选题',
            'JUDGE': '判断题',
            'FILL': '填空题',
            'SHORT': '简答题',
            'SUBJECTIVE': '主观题',
            'GROUP': '组合题',
            // 兼容旧格式
            'singleChoice': '单选题',
            'multipleChoice': '多选题',
            'judgment': '判断题',
            'fillBlank': '填空题',
            'shortAnswer': '简答题',
            'subjective': '主观题',
            'groupQuestion': '组合题'
        };
        return typeMap[type] || type || '未知题型';
    }

    /**
     * 获取替代题型标识（新旧格式互转）
     */
    getAlternativeType(type) {
        const typeMapping = {
            'SINGLE_CHOICE': 'singleChoice',
            'singleChoice': 'SINGLE_CHOICE',
            'MULTIPLE_CHOICE': 'multipleChoice',
            'multipleChoice': 'MULTIPLE_CHOICE',
            'JUDGE': 'judgment',
            'judgment': 'JUDGE',
            'FILL': 'fillBlank',
            'fillBlank': 'FILL',
            'SHORT': 'shortAnswer',
            'shortAnswer': 'SHORT',
            'SUBJECTIVE': 'subjective',
            'subjective': 'SUBJECTIVE',
            'GROUP': 'groupQuestion',
            'groupQuestion': 'GROUP'
        };
        return typeMapping[type] || null;
    }

    /**
     * 获取难度样式类
     */
    getDifficultyClass(difficulty) {
        if (!difficulty) return 'badge-secondary';

        // 如果是数字类型的难度值（0-1之间）
        if (typeof difficulty === 'number') {
            if (difficulty <= 0.4) return 'badge-success';
            if (difficulty <= 0.7) return 'badge-warning';
            return 'badge-danger';
        }

        // 如果是字符串类型的难度值
        switch (difficulty.toString().toLowerCase()) {
            case 'easy':
            case '简单':
            case '1':
                return 'badge-success';
            case 'medium':
            case '中等':
            case '2':
                return 'badge-warning';
            case 'hard':
            case '困难':
            case '3':
                return 'badge-danger';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * 获取难度文本
     */
    getDifficultyText(difficulty) {
        if (!difficulty) return '未知';

        // 如果是数字类型的难度值（0-1之间）
        if (typeof difficulty === 'number') {
            if (difficulty <= 0.4) return '简单';
            if (difficulty <= 0.7) return '中等';
            return '困难';
        }

        // 如果是字符串类型的难度值
        switch (difficulty.toString().toLowerCase()) {
            case 'easy':
            case '简单':
            case '1':
                return '简单';
            case 'medium':
            case '中等':
            case '2':
                return '中等';
            case 'hard':
            case '困难':
            case '3':
                return '困难';
            default:
                return '未知';
        }
    }

    /**
     * HTML转义函数
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        this.loadingIndicator.classList.remove('d-none');
        this.contentContainer.classList.add('d-none');
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        this.loadingIndicator.classList.add('d-none');
        this.contentContainer.classList.remove('d-none');
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        this.errorContainer.querySelector('.error-message').textContent = message;
        this.errorContainer.classList.remove('d-none');
    }

    /**
     * 隐藏错误信息
     */
    hideError() {
        this.errorContainer.classList.add('d-none');
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        this.contentContainer.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="fas fa-file-alt fa-3x mb-3"></i>
                <p>请配置知识点和题型后查看预览</p>
            </div>
        `;
        this.statsContainer.innerHTML = '';
        this.warningsContainer.innerHTML = '';
    }

    /**
     * 获取当前预览数据
     */
    getCurrentPreviewData() {
        return this.currentPreviewData;
    }

    /**
     * 设置配置选项
     */
    setOptions(options) {
        this.options = { ...this.options, ...options };
    }
}

// 导出类供全局使用
window.PaperRealTimePreview = PaperRealTimePreview;
