/**
 *  下拉菜单显示问题修复脚本
 * 解决历史试卷下拉菜单显示不全和无法滚动的问题
 */

$(document).ready(function() {
    // 初始化下拉菜单修复

    // 修复所有下拉菜单的显示问题
    fixAllDropdownMenus();

    // 监听下拉菜单显示事件
    $(document).on('show.bs.dropdown', '.dropdown', function(e) {
        const $dropdown = $(this);
        const $menu = $dropdown.find('.dropdown-menu');

        console.log('🔧 下拉菜单显示事件触发:', $dropdown);

        //  添加body类，降低其他元素层级
        $('body').addClass('dropdown-open');

        // 延迟执行修复，确保菜单已经显示
        setTimeout(() => {
            fixDropdownPosition($dropdown, $menu);
        }, 10);
    });

    // 监听下拉菜单隐藏事件
    $(document).on('hide.bs.dropdown', '.dropdown', function(e) {
        const $dropdown = $(this);
        console.log('🔧 下拉菜单隐藏事件触发:', $dropdown);

        //  移除body类，恢复正常层级
        $('body').removeClass('dropdown-open');
    });

    // 监听窗口大小变化
    $(window).on('resize', function() {
        $('.dropdown-menu.show').each(function() {
            const $menu = $(this);
            const $dropdown = $menu.closest('.dropdown');
            fixDropdownPosition($dropdown, $menu);
        });
    });

    // 监听滚动事件
    $(window).on('scroll', function() {
        $('.dropdown-menu.show').each(function() {
            const $menu = $(this);
            const $dropdown = $menu.closest('.dropdown');
            fixDropdownPosition($dropdown, $menu);
        });
    });

    //  监听点击外部关闭下拉菜单
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.dropdown').length) {
            // 点击了下拉菜单外部，关闭所有下拉菜单
            $('.dropdown-menu.show').removeClass('show');
            $('.dropdown-toggle.show').removeClass('show');
            $('body').removeClass('dropdown-open');
        }
    });

    //  监听ESC键关闭下拉菜单
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('.dropdown-menu.show').removeClass('show');
            $('.dropdown-toggle.show').removeClass('show');
            $('body').removeClass('dropdown-open');
        }
    });


});

/**
 * 修复所有下拉菜单
 */
function fixAllDropdownMenus() {
    // 修复所有下拉菜单

    // 查找所有增强下载菜单
    $('.enhanced-download-menu').each(function() {
        const $menu = $(this);
        const $dropdown = $menu.closest('.dropdown, .btn-group');

        // 确保菜单有正确的类和属性
        $menu.addClass('dropdown-menu');

        // 修复菜单结构
        fixMenuStructure($menu);


    });

    // 为所有下拉菜单添加事件监听
    $('.dropdown-toggle').off('click.dropdown-fix').on('click.dropdown-fix', function(e) {
        const $toggle = $(this);
        const $dropdown = $toggle.closest('.dropdown, .btn-group');
        const $menu = $dropdown.find('.dropdown-menu');



        // 延迟修复位置
        setTimeout(() => {
            if ($menu.hasClass('show')) {
                fixDropdownPosition($dropdown, $menu);
            }
        }, 50);
    });
}

/**
 * 修复下拉菜单位置
 */
function fixDropdownPosition($dropdown, $menu) {
    if (!$menu || $menu.length === 0) {
        return;
    }

    try {
        // 获取触发按钮的位置
        const $toggle = $dropdown.find('.dropdown-toggle');

        //  添加空值检查
        if (!$toggle || $toggle.length === 0) {
            return;
        }

        const toggleOffset = $toggle.offset();

        //  添加offset空值检查
        if (!toggleOffset) {
            // 使用默认位置
            $menu.css({
                'position': 'absolute',
                'top': '100%',
                'left': '0',
                'z-index': 1080,
                'max-height': '80vh',
                'overflow-y': 'auto'
            });
            return;
        }

        const toggleHeight = $toggle.outerHeight() || 0;
        const toggleWidth = $toggle.outerWidth() || 0;

        // 获取窗口尺寸
        const windowWidth = $(window).width();
        const windowHeight = $(window).height();
        const scrollTop = $(window).scrollTop();

        // 获取菜单尺寸
        const menuWidth = $menu.outerWidth() || 200; // 默认宽度
        const menuHeight = $menu.outerHeight() || 100; // 默认高度

        // 计算最佳位置
        let left = toggleOffset.left;
        let top = toggleOffset.top + toggleHeight + 2;

        // 检查右边界
        if (left + menuWidth > windowWidth) {
            left = windowWidth - menuWidth - 10;
        }

        // 检查左边界
        if (left < 10) {
            left = 10;
        }

        // 检查下边界
        if (top + menuHeight > windowHeight + scrollTop) {
            // 显示在按钮上方
            top = toggleOffset.top - menuHeight - 2;
        }

        // 检查上边界
        if (top < scrollTop + 10) {
            top = scrollTop + 10;
        }

        // 应用位置
        $menu.css({
            'position': 'fixed',
            'top': top - scrollTop,
            'left': left,
            'z-index': 1080, /*  使用最高层级 */
            'max-height': Math.min(menuHeight, windowHeight - 100),
            'overflow-y': 'auto'
        });

    } catch (error) {
        // 修复失败，静默处理
    }
}

/**
 * 修复菜单结构
 */
function fixMenuStructure($menu) {
    // 确保菜单有正确的滚动设置
    $menu.css({
        'max-height': '80vh',
        'overflow-y': 'auto',
        'overflow-x': 'hidden'
    });

    // 修复版本选择器事件
    $menu.find('.version-selector').off('change.dropdown-fix').on('change.dropdown-fix', function() {
        const $selector = $(this);
        const selectedValue = $selector.val();
        const $description = $menu.find('.version-description');

        // 更新版本描述
        updateVersionDescription(selectedValue, $description);
    });

    // 修复下载按钮事件
    $menu.find('.download-with-version').off('click.dropdown-fix').on('click.dropdown-fix', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $btn = $(this);
        const paperId = $btn.data('paper-id');
        const format = $btn.data('format');
        const version = $menu.find('.version-selector').val() || 'standard';



        // 关闭下拉菜单
        $menu.removeClass('show');
        $menu.closest('.dropdown, .btn-group').find('.dropdown-toggle').removeClass('show');
        $('body').removeClass('dropdown-open');

        //  延迟执行下载，确保菜单完全关闭
        setTimeout(() => {
            // 执行下载
            if (typeof downloadPaperWithVersion === 'function') {
                downloadPaperWithVersion(paperId, format, version);
            } else if (typeof window.PaperVersionSelector !== 'undefined' &&
                      typeof window.PaperVersionSelector.downloadPaperWithVersion === 'function') {
                window.PaperVersionSelector.downloadPaperWithVersion(paperId, format, version);
            } else {
                // 备用下载方法
                const url = `/api/papers/download/${paperId}?format=${format}&paperType=${version}`;
                window.open(url, '_blank');
            }
        }, 100);
    });

    // 修复其他按钮事件
    $menu.find('.clone-paper-btn').off('click.dropdown-fix').on('click.dropdown-fix', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $btn = $(this);
        const paperId = $btn.data('id');

        // 关闭下拉菜单
        $menu.removeClass('show');

        // 执行克隆
        if (typeof clonePaper === 'function') {
            clonePaper(paperId);
        }
    });

    $menu.find('.delete-paper-btn').off('click.dropdown-fix').on('click.dropdown-fix', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $btn = $(this);
        const paperId = $btn.data('id');
        const paperTitle = $btn.data('title');

        // 关闭下拉菜单
        $menu.removeClass('show');

        // 执行删除
        if (typeof deletePaper === 'function') {
            deletePaper(paperId, paperTitle);
        }
    });
}

/**
 * 更新版本描述
 */
function updateVersionDescription(version, $description) {
    const descriptions = {
        'regular': '只包含题目和选项，不显示答案和解析，适合考试使用',
        'teacher': '只包含答案和解析，不显示题目内容，适合教师批改使用',
        'standard': '包含完整的题目、答案和解析，适合学习使用'
    };

    const description = descriptions[version] || descriptions['standard'];
    $description.text(description);
}

/**
 * 强制修复所有下拉菜单问题
 */
window.forceFixAllDropdowns = function() {
    // 关闭所有下拉菜单
    $('.dropdown-menu.show').removeClass('show');
    $('.dropdown-toggle.show').removeClass('show');

    // 重新初始化
    setTimeout(() => {
        fixAllDropdownMenus();
    }, 100);
};

/**
 * 调试函数：高亮显示下拉菜单
 */
window.debugDropdownMenus = function() {
    $('.enhanced-download-menu').each(function(index) {
        const $menu = $(this);
        $menu.addClass('debug');
        $menu.css('border', '2px solid red');
    });
};

/**
 * 移除调试样式
 */
window.removeDropdownDebug = function() {
    $('.enhanced-download-menu').removeClass('debug').css('border', '');
};

// 导出函数供其他脚本使用
window.DropdownMenuFix = {
    fixAllDropdownMenus: fixAllDropdownMenus,
    fixDropdownPosition: fixDropdownPosition,
    fixMenuStructure: fixMenuStructure,
    updateVersionDescription: updateVersionDescription
};
