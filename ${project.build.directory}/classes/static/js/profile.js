// 设置头像显示的函数
function updateAvatarDisplay(avatarPath) {
    const avatarUrl = getAvatarUrl(avatarPath);
    document.querySelectorAll('.avatar, .large-avatar').forEach(img => {
        img.src = avatarUrl;
        img.onerror = function() {
            this.src = '/images/default-avatar.png';
            console.log('Avatar load failed, using default');
        };
    });
}

document.addEventListener('DOMContentLoaded', async function() {
    // 初始化用户信息
    const userInfo = await checkAuth();
    if (userInfo) {
        // 设置用户信息
        document.querySelector('.username').textContent = userInfo.username;
        document.querySelector('.email').textContent = userInfo.email || '未设置';
        document.querySelector('.role').textContent =
            userInfo.role === 1 ? '普通用户' :
            userInfo.role === 2 ? '管理员' : '超级管理员';

        // 设置表单默认值
        document.querySelector('input[name="username"]').value = userInfo.username;
        document.querySelector('input[name="email"]').value = userInfo.email || '';
        document.querySelector('input[name="phone"]').value = userInfo.phone || '';

        // 设置头像
        updateAvatarDisplay(userInfo.avatar);
    }

    // 头像上传
    const avatarInput = document.getElementById('avatarInput');
    const uploadBtn = document.querySelector('.upload-btn');

    uploadBtn.addEventListener('click', () => avatarInput.click());

    avatarInput.addEventListener('change', async function() {
        const file = this.files[0];
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/api/user/avatar', {
                method: 'POST',
                headers: {
                    'Authorization': localStorage.getItem('token')
                },
                body: formData
            });

            const data = await response.json();
            if (data.code === 200) {
                updateAvatarDisplay(data.data);
                showToast('头像更新成功', 'success');
            } else {
                showToast(data.message, 'error');
            }
        } catch (error) {
            showToast('头像上传失败', 'error');
            console.error('Avatar upload failed:', error);
        }
    });
});

// 在表单提交处理中添加
async function handleFormSubmit(form, submitHandler) {
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        const submitBtn = form.querySelector('button[type="submit"]');

        if (submitBtn.classList.contains('loading')) {
            return;
        }

        try {
            submitBtn.classList.add('loading');
            await submitHandler(new FormData(form));
            showToast('保存成功', 'success');
        } catch (error) {
            handleError(error);
        } finally {
            submitBtn.classList.remove('loading');
        }
    });
}

// 添加表单验证
function validateForm(form) {
    const email = form.querySelector('input[name="email"]');
    const phone = form.querySelector('input[name="phone"]');

    if (email && !isValidEmail(email.value)) {
        showToast('请输入有效的邮箱地址', 'error');
        return false;
    }

    if (phone && !isValidPhone(phone.value)) {
        showToast('请输入有效的手机号码', 'error');
        return false;
    }

    return true;
}

function isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function isValidPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
}

// 在表单提交前调用验证
handleFormSubmit(document.getElementById('profileForm'), async (formData) => {
    if (!validateForm(form)) {
        return;
    }
    // ... 提交逻辑
});

// 获取头像URL的函数
function getAvatarUrl(path) {
    if (!path) return '/images/default-avatar.png';

    // Use the fixAvatarUrl helper if available
    if (window.fixAvatarUrl) {
        path = window.fixAvatarUrl(path);
    }
    
    // The server already gives us the correct path with 'avatars/' prefix
    // So we just need to prepend '/uploads/'
    return `/uploads/${path}?t=${new Date().getTime()}`;
}