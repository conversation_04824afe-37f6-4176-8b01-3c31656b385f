/**
 * 试卷查重分析页面JavaScript
 */

$(document).ready(function() {
    // 初始化页面
    initializePage();
    
    // 绑定事件
    bindEvents();
    
    // 加载试卷列表
    loadPapers();
});

// 全局变量
let selectedPapers = new Set();
let currentPage = 0;
let totalPages = 0;
let currentKeyword = '';
let duplicateMode = 'EXACT';

/**
 * 初始化页面
 */
function initializePage() {
    // 初始化相似度阈值显示
    updateSimilarityThreshold();
    
    // 初始化查重模式
    updateDuplicateMode();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索框事件
    $('#paperSearchInput').on('input', debounce(function() {
        currentKeyword = $(this).val().trim();
        currentPage = 0;
        loadPapers();
    }, 500));
    
    // 算法卡片点击
    $('.algorithm-card').click(function() {
        $('.algorithm-card').removeClass('selected');
        $(this).addClass('selected');
        duplicateMode = $(this).data('mode');
        updateDuplicateMode();
    });
    
    // 相似度阈值变化
    $('#similaritySlider').on('input', function() {
        updateSimilarityThreshold();
    });
    
    // 开始分析按钮
    $('#startCheckBtn').click(function() {
        startAnalysis();
    });
    
    // 清空选择按钮
    $('#clearSelectionBtn').click(function() {
        clearSelection();
    });
}

/**
 * 更新查重模式
 */
function updateDuplicateMode() {
    const similaritySettings = $('#similaritySettings');
    if (duplicateMode === 'SIMILAR') {
        similaritySettings.addClass('visible');
    } else {
        similaritySettings.removeClass('visible');
    }
}

/**
 * 更新相似度阈值显示
 */
function updateSimilarityThreshold() {
    const value = $('#similaritySlider').val();
    $('#similarityValue').text(value + '%');
}

/**
 * 加载试卷列表
 */
function loadPapers() {
    const params = {
        page: currentPage,
        size: 10
    };
    
    if (currentKeyword) {
        params.keyword = currentKeyword;
    }
    
    $.ajax({
        url: '/papers/duplicate-check/papers',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.code === 200) {
                renderPapersList(response.data);
                renderPagination(response.data);
            } else {
                showError('加载试卷列表失败：' + response.message);
            }
        },
        error: function() {
            showError('加载试卷列表失败，请稍后重试');
        }
    });
}

/**
 * 渲染试卷列表
 */
function renderPapersList(pageData) {
    const container = $('#paperList');
    
    if (pageData.content.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-inbox text-muted" style="font-size: 3em;"></i>
                <p class="mt-3 text-muted">没有找到试卷</p>
                ${currentKeyword ? '<p class="text-muted">尝试调整搜索关键词</p>' : ''}
            </div>
        `);
        return;
    }
    
    let html = '';
    pageData.content.forEach(paper => {
        const isSelected = selectedPapers.has(paper.id);
        const selectedClass = isSelected ? 'selected' : '';
        
        html += `
            <div class="paper-item ${selectedClass}" data-paper-id="${paper.id}">
                <div class="paper-title">
                    <i class="fas fa-file-alt text-primary me-2"></i>
                    ${escapeHtml(paper.title)}
                    ${isSelected ? '<i class="fas fa-check-circle text-success float-end"></i>' : ''}
                </div>
                <div class="paper-meta">
                    <span class="me-3">
                        <i class="fas fa-calendar text-muted me-1"></i>
                        ${formatDateTime(paper.createTime)}
                    </span>
                    <span class="me-3">
                        <i class="fas fa-star text-muted me-1"></i>
                        总分：${paper.totalScore || 0}分
                    </span>
                    <span>
                        <i class="fas fa-chart-bar text-muted me-1"></i>
                        难度：${formatDifficulty(paper.difficulty)}
                    </span>
                </div>
            </div>
        `;
    });
    
    container.html(html);
    
    // 绑定点击事件
    $('.paper-item').click(function() {
        const paperId = parseInt($(this).data('paper-id'));
        togglePaperSelection(paperId, $(this));
    });
}

/**
 * 渲染分页
 */
function renderPagination(pageData) {
    const container = $('#pagination');
    totalPages = pageData.totalPages;
    
    if (totalPages <= 1) {
        container.empty();
        return;
    }
    
    let html = '';
    
    // 上一页
    if (currentPage > 0) {
        html += `<li class="page-item">
            <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
        </li>`;
    }
    
    // 页码
    const startPage = Math.max(0, currentPage - 2);
    const endPage = Math.min(totalPages - 1, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        html += `<li class="page-item ${activeClass}">
            <a class="page-link" href="#" data-page="${i}">${i + 1}</a>
        </li>`;
    }
    
    // 下一页
    if (currentPage < totalPages - 1) {
        html += `<li class="page-item">
            <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
        </li>`;
    }
    
    container.html(html);
    
    // 绑定分页点击事件
    $('.page-link').click(function(e) {
        e.preventDefault();
        const page = parseInt($(this).data('page'));
        if (page !== currentPage) {
            currentPage = page;
            loadPapers();
        }
    });
}

/**
 * 切换试卷选择状态
 */
function togglePaperSelection(paperId, element) {
    if (selectedPapers.has(paperId)) {
        // 取消选择
        selectedPapers.delete(paperId);
        element.removeClass('selected');
        element.find('.fa-check-circle').remove();
    } else {
        // 检查选择数量限制
        if (selectedPapers.size >= 20) {
            Swal.fire('提示', '最多只能选择20套试卷进行对比', 'warning');
            return;
        }
        
        // 添加选择
        selectedPapers.add(paperId);
        element.addClass('selected');
        element.find('.paper-title').append('<i class="fas fa-check-circle text-success float-end"></i>');
    }
    
    updateSelectedPapersPanel();
}

/**
 * 更新已选择试卷面板
 */
function updateSelectedPapersPanel() {
    const panel = $('#selectedPapersContainer');
    const count = selectedPapers.size;
    
    $('#selectedCount').text(count);
    
    if (count === 0) {
        panel.hide();
        $('#startCheckBtn').prop('disabled', true);
        return;
    }
    
    panel.show();
    $('#startCheckBtn').prop('disabled', count < 2);
    
    // 更新已选择试卷列表
    const selectedList = $('#selectedPapersList');
    let html = '';
    
    $('.paper-item.selected').each(function() {
        const paperId = $(this).data('paper-id');
        const paperTitle = $(this).find('.paper-title').text().trim();
        
        html += `
            <span class="selected-paper-tag">
                ${escapeHtml(paperTitle)}
                <span class="remove-btn" data-paper-id="${paperId}">
                    <i class="fas fa-times"></i>
                </span>
            </span>
        `;
    });
    
    selectedList.html(html);
    
    // 绑定移除按钮事件
    $('.remove-btn').click(function(e) {
        e.stopPropagation();
        const paperId = parseInt($(this).data('paper-id'));
        const paperElement = $(`.paper-item[data-paper-id="${paperId}"]`);
        togglePaperSelection(paperId, paperElement);
    });
}

/**
 * 清空选择
 */
function clearSelection() {
    selectedPapers.clear();
    $('.paper-item').removeClass('selected');
    $('.paper-item .fa-check-circle').remove();
    updateSelectedPapersPanel();
}

/**
 * 开始分析
 */
function startAnalysis() {
    if (selectedPapers.size < 2) {
        Swal.fire('提示', '请至少选择2套试卷进行对比', 'warning');
        return;
    }

    const request = {
        paperIds: Array.from(selectedPapers),
        mode: duplicateMode,
        similarityThreshold: parseInt($('#similaritySlider').val()) / 100,
        includeTopicDetails: $('#includeDetailsSwitch').is(':checked')
    };

    // 显示加载遮罩
    $('#loadingOverlay').fadeIn(100);

    $.ajax({
        url: '/papers/duplicate-check/analyze',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(request),
        success: function(response) {
            $('#loadingOverlay').fadeOut(100);

            if (response.code === 200) {
                // 跳转到结果页面
                const paperIds = Array.from(selectedPapers).join(',');
                window.location.href = `/papers/duplicate-check/result?paperIds=${paperIds}`;
            } else {
                showError('分析失败：' + response.message);
            }
        },
        error: function() {
            $('#loadingOverlay').fadeOut(100);
            showError('分析失败，请稍后重试');
        }
    });
}

/**
 * 工具函数
 */

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '未知';
    const date = new Date(dateTimeStr);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
}

// 格式化难度
function formatDifficulty(difficulty) {
    if (difficulty == null) return '未知';
    if (difficulty <= 2.0) return '简单';
    if (difficulty <= 4.0) return '中等';
    return '困难';
}

// 显示错误信息
function showError(message) {
    Swal.fire('错误', message, 'error');
}
