/**
 * 快速组卷功能
 * 提供简化的试卷生成流程
 */

$(document).ready(function() {
    // 默认配置
    const defaultConfigs = {
        balanced: {
            singleChoice: 40,
            multipleChoice: 25,
            judgment: 20,
            fillBlank: 10,
            shortAnswer: 5,
            difficulty: {
                easy: 30,
                medium: 50,
                hard: 20
            }
        },
        easy: {
            singleChoice: 50,
            multipleChoice: 20,
            judgment: 20,
            fillBlank: 10,
            shortAnswer: 0,
            difficulty: {
                easy: 60,
                medium: 30,
                hard: 10
            }
        },
        medium: {
            singleChoice: 35,
            multipleChoice: 30,
            judgment: 20,
            fillBlank: 10,
            shortAnswer: 5,
            difficulty: {
                easy: 30,
                medium: 60,
                hard: 10
            }
        },
        hard: {
            singleChoice: 30,
            multipleChoice: 30,
            judgment: 15,
            fillBlank: 15,
            shortAnswer: 10,
            difficulty: {
                easy: 10,
                medium: 50,
                hard: 40
            }
        }
    };

    // 更新知识点统计
    function updateKnowledgeStats() {
        const selectedCheckboxes = $('.knowledge-checkbox:checked');
        const count = selectedCheckboxes.length;

        // 更新知识点数量
        $('#totalSelectedKnowledgePoints').text(count);

        // 计算可用题目总数
        let totalQuestions = 0;
        selectedCheckboxes.each(function() {
            const card = $(this).closest('.card');
            const topicCount = parseInt(card.find('.badge-info').text().replace('题目: ', '').trim()) || 0;
            totalQuestions += topicCount;
        });

        $('#totalAvailableQuestions').text(totalQuestions);

        // 显示或隐藏统计卡片
        if (count > 0) {
            $('#selectedKnowledgeStatsCard').fadeIn();
            $('#quickConfigOptions').fadeIn();
            $('#noKnowledgePointsSelected').hide();
            $('#quickGenerateBtn').prop('disabled', false);
        } else {
            $('#selectedKnowledgeStatsCard').fadeOut();
            $('#quickConfigOptions').fadeOut();
            $('#noKnowledgePointsSelected').fadeIn();
            $('#quickGenerateBtn').prop('disabled', true);
        }

        // 更新题型分布预览
        updateQuestionTypePreview();
    }

    // 更新题型分布预览
    function updateQuestionTypePreview() {
        const paperType = $('#quickPaperType').val();
        const isCustom = paperType === 'custom';

        // 获取当前配置
        let config = isCustom ? getCurrentCustomConfig() : defaultConfigs[paperType];

        // 更新进度条
        $('#singleChoiceBar').css('width', config.singleChoice + '%');
        $('#multipleChoiceBar').css('width', config.multipleChoice + '%');
        $('#judgmentBar').css('width', config.judgment + '%');
        $('#fillBlankBar').css('width', config.fillBlank + '%');
        $('#shortAnswerBar').css('width', config.shortAnswer + '%');

        // 更新比例文本
        $('#questionTypeRatio').text(
            `单选:${config.singleChoice}% 多选:${config.multipleChoice}% 判断:${config.judgment}% 填空:${config.fillBlank}% 简答:${config.shortAnswer}%`
        );
    }

    // 获取当前自定义配置
    function getCurrentCustomConfig() {
        // 获取已选择的题型
        const selectedTypes = [];
        $('.quick-question-type:checked').each(function() {
            selectedTypes.push($(this).data('type'));
        });

        // 如果没有选择任何题型，默认选择单选题
        if (selectedTypes.length === 0) {
            selectedTypes.push('singleChoice');
            $('#quickSingleChoice').prop('checked', true);
        }

        // 计算每种题型的百分比
        const typePercentage = Math.floor(100 / selectedTypes.length);
        const remainder = 100 - (typePercentage * selectedTypes.length);

        // 创建配置对象
        const config = {
            singleChoice: 0,
            multipleChoice: 0,
            judgment: 0,
            fillBlank: 0,
            shortAnswer: 0,
            difficulty: {
                easy: 30,
                medium: 50,
                hard: 20
            }
        };

        // 分配百分比
        selectedTypes.forEach((type, index) => {
            // 第一个类型获得余数
            config[type] = typePercentage + (index === 0 ? remainder : 0);
        });

        return config;
    }

    // 生成试卷配置
    function generatePaperConfig() {
        const selectedCheckboxes = $('.knowledge-checkbox:checked');

        //  检查是否有题型配置
        const singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
        const multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
        const judgment = parseInt($('#judgmentCount').val()) || 0;
        const fillBlank = parseInt($('#fillCount').val()) || 0;
        const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

        const totalQuestions = singleChoice + multipleChoice + judgment + fillBlank + shortAnswer;

        if (totalQuestions === 0) {
            Swal.fire({
                icon: 'warning',
                title: '未配置题型',
                text: '请至少配置一种题型的数量后再生成试卷',
                confirmButtonText: '确定'
            });
            return null;
        }

        //  如果没有选择知识点，但配置了简答题，允许生成
        if (selectedCheckboxes.length === 0) {
            if (shortAnswer > 0) {
                // 只有简答题，允许生成
                console.log('快速组卷：简答题模式，无需知识点');
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: '无法生成试卷',
                    html: `
                        <div class="text-center">
                            <p>请选择以下任一方式：</p>
                            <ul class="text-start">
                                <li><strong>选择知识点</strong>：生成基于知识点的试卷</li>
                                <li><strong>配置简答题</strong>：生成纯简答题试卷</li>
                            </ul>
                        </div>
                    `,
                    confirmButtonText: '确定'
                });
                return null;
            }
        }

        // 获取总题量
        const totalQuestions = parseInt($('#quickTotalQuestions').val()) || 20;

        // 获取试卷类型
        const paperType = $('#quickPaperType').val();
        const config = paperType === 'custom' ? getCurrentCustomConfig() : defaultConfigs[paperType];

        // 计算各题型数量
        const singleChoiceCount = Math.round(totalQuestions * config.singleChoice / 100);
        const multipleChoiceCount = Math.round(totalQuestions * config.multipleChoice / 100);
        const judgmentCount = Math.round(totalQuestions * config.judgment / 100);
        const fillBlankCount = Math.round(totalQuestions * config.fillBlank / 100);
        const shortAnswerCount = Math.round(totalQuestions * config.shortAnswer / 100);

        // 调整总数，确保等于totalQuestions
        let actualTotal = singleChoiceCount + multipleChoiceCount + judgmentCount + fillBlankCount + shortAnswerCount;
        let diff = totalQuestions - actualTotal;

        // 如果有差异，调整最大的题型数量
        if (diff !== 0) {
            const types = [
                { type: 'singleChoice', count: singleChoiceCount },
                { type: 'multipleChoice', count: multipleChoiceCount },
                { type: 'judgment', count: judgmentCount },
                { type: 'fillBlank', count: fillBlankCount },
                { type: 'shortAnswer', count: shortAnswerCount }
            ].sort((a, b) => b.count - a.count);

            // 找到第一个非零的最大题型
            for (const type of types) {
                if (type.count > 0) {
                    if (type.type === 'singleChoice') singleChoiceCount += diff;
                    else if (type.type === 'multipleChoice') multipleChoiceCount += diff;
                    else if (type.type === 'judgment') judgmentCount += diff;
                    else if (type.type === 'fillBlank') fillBlankCount += diff;
                    else if (type.type === 'shortAnswer') shortAnswerCount += diff;
                    break;
                }
            }
        }

        // 收集知识点配置
        const knowledgeConfigs = [];
        const knowledgeNames = [];

        selectedCheckboxes.each(function() {
            const knowledgeId = parseInt($(this).val());
            const card = $(this).closest('.card');
            const knowledgeName = card.find('.card-title').text().trim();

            knowledgeConfigs.push({
                knowledgeId: knowledgeId,
                questionCount: Math.max(1, Math.floor(totalQuestions / selectedCheckboxes.length)),
                includeShortAnswer: false // 默认关闭简答题开关
            });

            knowledgeNames.push(knowledgeName);
        });

        // 创建标题
        let paperTitle = '';
        if (knowledgeNames.length === 1) {
            paperTitle = knowledgeNames[0] + ' 专项练习';
        } else if (knowledgeNames.length <= 3) {
            paperTitle = knowledgeNames.join('+') + ' 组合练习';
        } else {
            paperTitle = knowledgeNames[0] + '等' + knowledgeNames.length + '个知识点 组合练习';
        }

        // 返回配置
        return {
            knowledgeConfigs: knowledgeConfigs,
            paperTitle: paperTitle,
            singleChoiceCount: singleChoiceCount,
            multipleChoiceCount: multipleChoiceCount,
            judgmentCount: judgmentCount,
            fillBlankCount: fillBlankCount,
            shortAnswerCount: shortAnswerCount,
            difficulty: config.difficulty
        };
    }

    // 绑定事件

    // 监听知识点选择变化
    $(document).on('change', '.knowledge-checkbox', updateKnowledgeStats);

    // 监听试卷类型变化
    $('#quickPaperType').on('change', function() {
        const isCustom = $(this).val() === 'custom';

        // 如果是自定义配置，启用题型选择
        $('.quick-question-type').prop('disabled', !isCustom);

        // 更新题型分布预览
        updateQuestionTypePreview();
    });

    // 监听题型选择变化
    $('.quick-question-type').on('change', function() {
        // 确保至少选择一种题型
        if ($('.quick-question-type:checked').length === 0) {
            $(this).prop('checked', true);

            Swal.fire({
                icon: 'warning',
                title: '至少选择一种题型',
                text: '请至少选择一种题型',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }

        // 更新题型分布预览
        updateQuestionTypePreview();
    });

    // 题目总数增减按钮
    $('#decreaseTotalQuestions').on('click', function() {
        const input = $('#quickTotalQuestions');
        let value = parseInt(input.val()) || 20;
        if (value > 5) {
            input.val(value - 5);
        }
    });

    $('#increaseTotalQuestions').on('click', function() {
        const input = $('#quickTotalQuestions');
        let value = parseInt(input.val()) || 20;
        if (value < 50) {
            input.val(value + 5);
        }
    });

    // 一键组卷按钮
    $('#quickGenerateBtn, #quickGenerateConfirmBtn').on('click', function() {
        const config = generatePaperConfig();
        if (!config) return;

        // 显示加载中
        Swal.fire({
            title: '准备中...',
            text: '正在准备试卷配置',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            },
            timer: 800
        });

        // 打开高级配置模态框
        setTimeout(() => {
            // 设置表单值
            $('#paperTitle').val(config.paperTitle);

            $('#singleChoiceCount').val(config.singleChoiceCount);
            $('#multipleChoiceCount').val(config.multipleChoiceCount);
            $('#judgmentCount').val(config.judgmentCount);
            $('#fillCount').val(config.fillBlankCount);
            $('#shortAnswerCount').val(config.shortAnswerCount);

            $('#easyPercentage').val(config.difficulty.easy);
            $('#mediumPercentage').val(config.difficulty.medium);
            $('#hardPercentage').val(config.difficulty.hard);

            // 打开模态框
            openPaperGenerationModal(config.knowledgeConfigs, config.paperTitle);
        }, 800);
    });

    // 高级配置按钮
    $('#advancedConfigBtn').on('click', function() {
        const config = generatePaperConfig();
        if (!config) return;

        // 打开高级配置模态框
        openPaperGenerationModal(config.knowledgeConfigs, config.paperTitle);
    });
});
