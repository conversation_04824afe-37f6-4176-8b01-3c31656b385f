/**
 * 试卷预览功能
 * 提供试卷预览和模拟生成功能
 */

$(document).ready(function() {
    // 预览按钮点击事件
    $('#previewPaperBtn').on('click', function() {
        // 切换到预览选项卡
        $('#preview-tab').tab('show');
        
        // 更新预览内容
        updatePaperPreview();
    });
    
    // 更新试卷预览
    window.updatePaperPreview = function() {
        // 获取试卷标题
        const title = $('#paperTitle').val() || '未命名试卷';
        
        // 获取题型数量和分值
        const singleChoiceCount = parseInt($('#singleChoiceCount').val()) || 0;
        const singleChoiceScore = parseFloat($('#singleChoiceScore').val()) || 0;
        
        const multipleChoiceCount = parseInt($('#multipleChoiceCount').val()) || 0;
        const multipleChoiceScore = parseFloat($('#multipleChoiceScore').val()) || 0;
        
        const judgmentCount = parseInt($('#judgmentCount').val()) || 0;
        const judgmentScore = parseFloat($('#judgmentScore').val()) || 0;
        
        const fillCount = parseInt($('#fillCount').val()) || 0;
        const fillScore = parseFloat($('#fillScore').val()) || 0;
        
        const shortAnswerCount = parseInt($('#shortAnswerCount').val()) || 0;
        const shortAnswerScore = parseFloat($('#shortAnswerScore').val()) || 0;
        
        // 计算总分和总题数
        const totalScore = 
            singleChoiceCount * singleChoiceScore +
            multipleChoiceCount * multipleChoiceScore +
            judgmentCount * judgmentScore +
            fillCount * fillScore +
            shortAnswerCount * shortAnswerScore;
            
        const totalQuestions = 
            singleChoiceCount +
            multipleChoiceCount +
            judgmentCount +
            fillCount +
            shortAnswerCount;
        
        // 获取知识点配置
        const knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs') || [];
        
        // 更新预览标题和统计信息
        $('#previewTitle').text(title);
        $('#previewTotalScore').text(totalScore);
        $('#previewTotalQuestions').text(totalQuestions);
        
        // 生成预览内容
        let previewHtml = '';
        let questionIndex = 1;
        
        // 添加单选题
        if (singleChoiceCount > 0) {
            previewHtml += `
                <div class="question-section mb-4">
                    <h4 class="section-title">一、单选题（每题 ${singleChoiceScore} 分，共 ${singleChoiceCount} 题）</h4>
                    <div class="question-list">
            `;
            
            for (let i = 0; i < singleChoiceCount; i++) {
                previewHtml += generateSampleQuestion('SINGLE_CHOICE', questionIndex++, knowledgeConfigs);
            }
            
            previewHtml += `
                    </div>
                </div>
            `;
        }
        
        // 添加多选题
        if (multipleChoiceCount > 0) {
            previewHtml += `
                <div class="question-section mb-4">
                    <h4 class="section-title">二、多选题（每题 ${multipleChoiceScore} 分，共 ${multipleChoiceCount} 题）</h4>
                    <div class="question-list">
            `;
            
            for (let i = 0; i < multipleChoiceCount; i++) {
                previewHtml += generateSampleQuestion('MULTIPLE_CHOICE', questionIndex++, knowledgeConfigs);
            }
            
            previewHtml += `
                    </div>
                </div>
            `;
        }
        
        // 添加判断题
        if (judgmentCount > 0) {
            previewHtml += `
                <div class="question-section mb-4">
                    <h4 class="section-title">三、判断题（每题 ${judgmentScore} 分，共 ${judgmentCount} 题）</h4>
                    <div class="question-list">
            `;
            
            for (let i = 0; i < judgmentCount; i++) {
                previewHtml += generateSampleQuestion('JUDGMENT', questionIndex++, knowledgeConfigs);
            }
            
            previewHtml += `
                    </div>
                </div>
            `;
        }
        
        // 添加填空题
        if (fillCount > 0) {
            previewHtml += `
                <div class="question-section mb-4">
                    <h4 class="section-title">四、填空题（每题 ${fillScore} 分，共 ${fillCount} 题）</h4>
                    <div class="question-list">
            `;
            
            for (let i = 0; i < fillCount; i++) {
                previewHtml += generateSampleQuestion('FILL_BLANK', questionIndex++, knowledgeConfigs);
            }
            
            previewHtml += `
                    </div>
                </div>
            `;
        }
        
        // 添加简答题
        if (shortAnswerCount > 0) {
            previewHtml += `
                <div class="question-section mb-4">
                    <h4 class="section-title">五、简答题（每题 ${shortAnswerScore} 分，共 ${shortAnswerCount} 题）</h4>
                    <div class="question-list">
            `;
            
            for (let i = 0; i < shortAnswerCount; i++) {
                previewHtml += generateSampleQuestion('SHORT_ANSWER', questionIndex++, knowledgeConfigs);
            }
            
            previewHtml += `
                    </div>
                </div>
            `;
        }
        
        // 如果没有题目，显示提示信息
        if (totalQuestions === 0) {
            previewHtml = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>
                    请先配置题型数量，然后再预览试卷。
                </div>
            `;
        }
        
        // 更新预览内容
        $('#paperPreviewContent').html(previewHtml);
    };
    
    // 生成示例题目
    function generateSampleQuestion(type, index, knowledgeConfigs) {
        // 随机选择一个知识点
        const randomKnowledgeIndex = Math.floor(Math.random() * knowledgeConfigs.length);
        const knowledgeConfig = knowledgeConfigs[randomKnowledgeIndex] || {};
        
        // 随机选择难度
        const difficulties = ['简单', '中等', '困难'];
        const randomDifficulty = difficulties[Math.floor(Math.random() * difficulties.length)];
        
        // 根据题型生成不同的题目
        let questionHtml = '';
        
        switch (type) {
            case 'SINGLE_CHOICE':
                questionHtml = `
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-index">${index}.</span>
                            <span class="question-title">[示例] 单选题示例问题？（知识点ID: ${knowledgeConfig.knowledgeId || '未知'}，难度: ${randomDifficulty}）</span>
                        </div>
                        <div class="question-options">
                            <div class="option-item">A. 选项A</div>
                            <div class="option-item">B. 选项B</div>
                            <div class="option-item">C. 选项C</div>
                            <div class="option-item">D. 选项D</div>
                        </div>
                    </div>
                `;
                break;
                
            case 'MULTIPLE_CHOICE':
                questionHtml = `
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-index">${index}.</span>
                            <span class="question-title">[示例] 多选题示例问题？（知识点ID: ${knowledgeConfig.knowledgeId || '未知'}，难度: ${randomDifficulty}）</span>
                        </div>
                        <div class="question-options">
                            <div class="option-item">A. 选项A</div>
                            <div class="option-item">B. 选项B</div>
                            <div class="option-item">C. 选项C</div>
                            <div class="option-item">D. 选项D</div>
                        </div>
                    </div>
                `;
                break;
                
            case 'JUDGMENT':
                questionHtml = `
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-index">${index}.</span>
                            <span class="question-title">[示例] 判断题示例问题。（知识点ID: ${knowledgeConfig.knowledgeId || '未知'}，难度: ${randomDifficulty}）</span>
                        </div>
                        <div class="question-options">
                            <div class="option-item">A. 正确</div>
                            <div class="option-item">B. 错误</div>
                        </div>
                    </div>
                `;
                break;
                
            case 'FILL_BLANK':
                questionHtml = `
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-index">${index}.</span>
                            <span class="question-title">[示例] 填空题示例问题，请填写 ________ 。（知识点ID: ${knowledgeConfig.knowledgeId || '未知'}，难度: ${randomDifficulty}）</span>
                        </div>
                    </div>
                `;
                break;
                
            case 'SHORT_ANSWER':
                questionHtml = `
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-index">${index}.</span>
                            <span class="question-title">[示例] 简答题示例问题？（知识点ID: ${knowledgeConfig.knowledgeId || '未知'}，难度: ${randomDifficulty}）</span>
                        </div>
                        <div class="answer-area">
                            <div class="answer-lines"></div>
                        </div>
                    </div>
                `;
                break;
                
            default:
                questionHtml = `
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-index">${index}.</span>
                            <span class="question-title">[示例] 未知题型示例问题。（知识点ID: ${knowledgeConfig.knowledgeId || '未知'}，难度: ${randomDifficulty}）</span>
                        </div>
                    </div>
                `;
        }
        
        return questionHtml;
    }
});
