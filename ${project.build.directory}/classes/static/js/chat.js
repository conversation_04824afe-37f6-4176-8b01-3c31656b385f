// Chat.js - Simplified, bug-fixed chat functionality
(function() {
    // Constants
    const API_BASE_URL = '/api';
    const MSG_TYPE = {
        USER: 'user',
        AI: 'ai',
        SYSTEM: 'system'
    };

    // Store app state
    const state = {
        currentChatId: null,
        isStreaming: false,
        isSending: false,
        retryCount: 0,
        maxRetries: 3,
        networkStatus: navigator.onLine,
        isWaitingForResponse: false,
        chatSessions: {}  // 用于缓存聊天会话数据
    };

    // Network status detection
    window.addEventListener('online', () => {
        state.networkStatus = true;
        showToast('网络连接已恢复', 'success');
    });

    window.addEventListener('offline', () => {
        state.networkStatus = false;
        showToast('网络连接已断开', 'error');
    });

    // Initialize chat on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Only run on chat page
        const currentPath = window.location.pathname;
        if (!isChatPage(currentPath)) {
            return;
        }

        initChat();
    });
    
    // Check if current page is the chat page
    function isChatPage(path) {
        return path.endsWith('/main/chat') || path === '/' || path === '';
    }
    
    // Main chat initialization
    async function initChat() {
        try {
            console.log("开始初始化聊天页面...");
            
            // 添加预检查 - 确保使用标准化的token
            const storedToken = localStorage.getItem('token');
            if (!storedToken || storedToken === 'undefined' || storedToken === 'null') {
                console.warn("本地存储中没有有效token，重定向到登录页");
                showToast('未检测到登录状态，请先登录', 'warning');
                setTimeout(() => window.location.href = '/auth/login', 1500);
                return;
            }

            // 规范化token
            const normalizedToken = normalizeToken(storedToken);
            if (!normalizedToken) {
                console.error("规范化token失败，原始token:", storedToken);
                showToast('登录凭证格式错误，请重新登录', 'error');
                setTimeout(() => {
                    localStorage.removeItem('token');
                    window.location.href = '/auth/login';
                }, 1500);
                return;
            }

            // 记录token信息（不输出完整token）
            console.log("当前token信息:", {
                length: normalizedToken.length,
                prefix: normalizedToken.substring(0, 5) + '...',
                isStandardized: normalizedToken === storedToken
            });
            
            // 进行token预验证
            console.log("执行token预验证...");
            const isTokenValid = await validateCurrentToken();
            if (!isTokenValid) {
                console.warn("Token预验证失败，token可能已过期");
                showToast('登录已过期，请重新登录', 'warning');
                setTimeout(() => {
                    localStorage.removeItem('token');
                    window.location.href = '/auth/login';
                }, 1500);
                return;
            }
            
            // 检查认证状态
            console.log("获取用户信息...");
            const userInfo = await checkAuth();
            if (!userInfo) {
                console.warn("获取用户信息失败，可能是token无效");
                showToast('无法验证登录状态，请重新登录', 'error');
                setTimeout(() => {
                    localStorage.removeItem('token');
                window.location.href = '/auth/login';
                }, 1500);
                return;
            }
            
            console.log("认证通过，用户信息:", userInfo.username);
            
            // Get DOM elements
            const elements = getDOMElements();
            if (!validateElements(elements)) {
                console.error("页面元素验证失败，某些必要元素不存在");
                showToast('页面加载异常，请刷新重试', 'error');
                return;
            }
            
            // Setup events and listeners
            setupEventListeners(elements);
            
            // Load chat history
            await loadChatHistory(elements);
            
            console.log("聊天初始化完成");
        } catch (error) {
            console.error('聊天初始化失败:', error);
            showToast('初始化失败，请刷新页面重试', 'error');
            
            // 记录错误详情但保持用户信息安全
            console.error('错误详情:', {
                message: error.message,
                name: error.name,
                stack: error.stack ? error.stack.split('\n')[0] : null
            });
        }
    }
    
    function getDOMElements() {
        return {
            newChatBtn: document.getElementById('newChatBtn'),
            modal: document.getElementById('newChatModal'),
            cancelBtn: document.getElementById('cancelNewChat'),
            confirmBtn: document.getElementById('confirmNewChat'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendMessage'),
            stopButton: document.getElementById('stopResponse'),
            chatMessages: document.getElementById('chatMessages'),
            chatHistory: document.getElementById('chatHistory'),
            bookUrlSelect: document.getElementById('bookUrl'),
            customBookUrlInput: document.getElementById('customBookUrl'),
            bookSearchBtn: document.getElementById('bookSearchBtn'),
            modalBookSearchBtn: document.getElementById('modalBookSearchBtn'),
            deleteChatBtn: document.getElementById('deleteChatBtn'),
            editTitleBtn: document.getElementById('editTitleBtn')
        };
    }
    
    function validateElements(elements) {
        return elements.chatMessages && elements.chatHistory;
    }
    
    function setupEventListeners(elements) {
        // New chat modal
        if (elements.newChatBtn) {
            elements.newChatBtn.addEventListener('click', () => {
                elements.modal.style.display = 'flex';
            });
        }
        
        if (elements.cancelBtn) {
            elements.cancelBtn.addEventListener('click', () => {
                elements.modal.style.display = 'none';
                resetNewChatForm(elements);
            });
        }
        
        // Close modal when clicking outside
        if (elements.modal) {
            elements.modal.addEventListener('click', (e) => {
                if (e.target === elements.modal) {
                    elements.modal.style.display = 'none';
                    resetNewChatForm(elements);
                }
            });
        }
        
        // Book URL select changes
        if (elements.bookUrlSelect) {
            elements.bookUrlSelect.addEventListener('change', () => {
                const customUrlInput = document.querySelector('.custom-url-input');
                if (customUrlInput) {
                    customUrlInput.classList.toggle('hidden', elements.bookUrlSelect.value !== 'custom');
                }
            });
        }
        
        // Book search buttons
        if (elements.bookSearchBtn) {
            elements.bookSearchBtn.addEventListener('click', showBookSearchModal);
        }
        
        if (elements.modalBookSearchBtn) {
            elements.modalBookSearchBtn.addEventListener('click', showBookSearchModal);
        }
        
        // Chat management buttons
        if (elements.editTitleBtn) {
            elements.editTitleBtn.addEventListener('click', editCurrentTitle);
        }
        
        if (elements.deleteChatBtn) {
            elements.deleteChatBtn.addEventListener('click', deleteCurrentChat);
        }
        
        // 添加保存标题按钮事件
        const saveTitleBtn = document.getElementById('saveTitleBtn');
        if (saveTitleBtn) {
            saveTitleBtn.addEventListener('click', saveEditedTitle);
        }
        
        // 添加确认删除按钮事件
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', confirmDeleteChat);
        }
        
        // Create new chat
        if (elements.confirmBtn) {
            elements.confirmBtn.addEventListener('click', () => createNewChat(elements));
        }
        
        // Send message
        if (elements.sendButton) {
            elements.sendButton.addEventListener('click', () => sendMessage(elements));
        }
        
        if (elements.messageInput) {
            elements.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage(elements);
                }
            });
            
            // Disable send button when input is empty
            elements.messageInput.addEventListener('input', () => {
                if (elements.sendButton) {
                    elements.sendButton.disabled = !elements.messageInput.value.trim();
                }
            });
        }
        
        // Initial button state
        if (elements.sendButton && elements.messageInput) {
            elements.sendButton.disabled = !elements.messageInput.value.trim();
        }
        
        // Add stop button event listener
        if (elements.stopButton) {
            elements.stopButton.addEventListener('click', () => {
                stopCurrentResponse(elements);
            });
        }
    }
    
    function showBookSearchModal() {
        const bookSearchModal = new bootstrap.Modal(document.getElementById('bookSearchModal'));
        bookSearchModal.show();
    }
    
    function resetNewChatForm(elements) {
        const newKnowId = document.getElementById('newKnowId');
        if (newKnowId) newKnowId.value = '';
        
        const newBookUrl = document.getElementById('newBookUrl');
        if (newBookUrl) newBookUrl.value = '';
        
        if (elements.bookUrlSelect) elements.bookUrlSelect.value = '';
        if (elements.customBookUrlInput) elements.customBookUrlInput.value = '';
        
        const customUrlInput = document.querySelector('.custom-url-input');
        if (customUrlInput) customUrlInput.classList.add('hidden');
    }
    
    // Update chat config fields with session data
    function updateChatConfigFields(session) {
        if (!session) return;
        
        const knowIdInput = document.getElementById('knowId');
        const bookUrlInput = document.getElementById('bookUrl');
        
        if (knowIdInput && session.knowId) {
            knowIdInput.value = session.knowId;
        }
        
        if (bookUrlInput && session.bookUrl) {
            bookUrlInput.value = session.bookUrl;
        }
    }
    
    // Create new chat
    async function createNewChat(elements) {
        const newKnowIdEl = document.getElementById('newKnowId');
        const newBookUrlEl = document.getElementById('newBookUrl');
        
        const knowId = newKnowIdEl ? newKnowIdEl.value : '';
        const bookUrl = newBookUrlEl ? newBookUrlEl.value : '';
        
        if (!knowId || !bookUrl) {
            showToast('Please fill in all required fields', 'error');
            return;
        }
        
        try {
            const token = localStorage.getItem('token');
            const normalizedToken = typeof normalizeToken === 'function' ? 
                normalizeToken(token) : token;
                
            const response = await fetch(`${API_BASE_URL}/chat/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': normalizedToken
                },
                body: JSON.stringify({ knowId, bookUrl })
            });
            
            const data = await response.json();
            if (isSuccessResponse(data)) {
                elements.modal.style.display = 'none';
                resetNewChatForm(elements);
                
                // 缓存新创建的会话数据
                if (data.data && data.data.id) {
                    state.chatSessions[data.data.id] = data.data;
                }
                
                await addChatToHistory(data.data, elements);
                await switchToChat(data.data.id, elements);
            } else {
                showToast(data.message || 'Failed to create chat', 'error');
            }
        } catch (error) {
            showToast('Error creating chat', 'error');
        }
    }
    
    // Load chat history
    async function loadChatHistory(elements) {
        try {
            const token = localStorage.getItem('token');
            const normalizedToken = typeof normalizeToken === 'function' ? 
                normalizeToken(token) : token;
                
            const response = await fetch(`${API_BASE_URL}/chat/history`, {
                headers: {
                    'Authorization': normalizedToken
                }
            });
            const responseText = await response.text();
            
            try {
                const data = JSON.parse(responseText);
                
                if (isSuccessResponse(data) && Array.isArray(data.data)) {
                    elements.chatHistory.innerHTML = '';
                    
                    // 清除之前的缓存数据
                    state.chatSessions = {};
                    
                    data.data.forEach(chat => {
                        // 缓存聊天会话数据
                        state.chatSessions[chat.id] = chat;
                        
                        addChatToHistory(chat, elements);
                    });
                    
                    // Select first chat if available
                    if (data.data.length > 0) {
                        await switchToChat(data.data[0].id, elements);
                    }
                }
            } catch (parseError) {
                showToast('Error parsing chat history response', 'error');
            }
        } catch (error) {
            showToast('Failed to load chat history', 'error');
        }
    }
    
    // Add chat to history sidebar
    function addChatToHistory(chat, elements) {
        if (!chat || !chat.id) {
            return;
        }
        
        const chatItem = document.createElement('div');
        chatItem.className = 'chat-item';
        chatItem.dataset.chatId = chat.id;
        chatItem.innerHTML = `
            <div class="chat-item-content">
                <div class="chat-item-title">${chat.title || '新对话'}</div>
                <div class="chat-item-time">
                    <i class="bi bi-clock"></i> ${formatTime(chat.createdAt)}
                </div>
            </div>
            <div class="chat-item-actions">
                <button class="chat-item-edit" title="修改标题" aria-label="修改标题">
                    <i class="bi bi-pencil-fill"></i>
                </button>
                <button class="chat-item-delete" title="删除对话" aria-label="删除对话">
                    <i class="bi bi-trash-fill"></i>
                </button>
            </div>
        `;
        
        // Add click event to the entire item for selection
        chatItem.addEventListener('click', (e) => {
            // Don't switch chats if clicking on action buttons
            if (!e.target.closest('.chat-item-actions')) {
                switchToChat(chat.id, elements);
            }
        });
        
        // Add edit button event
        const editBtn = chatItem.querySelector('.chat-item-edit');
        if (editBtn) {
            editBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent chat selection
                editChatTitle(chat.id);
            });
        }
        
        // Add delete button event
        const deleteBtn = chatItem.querySelector('.chat-item-delete');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent chat selection
                deleteChatById(chat.id);
            });
        }
        
        elements.chatHistory.insertBefore(chatItem, elements.chatHistory.firstChild);
    }
    
    // Switch to a specific chat
    async function switchToChat(chatId, elements) {
        if (!chatId) {
            return;
        }
        // Update UI state
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const chatItem = document.querySelector(`[data-chat-id="${chatId}"]`);
        if (chatItem) {
            chatItem.classList.add('active');
            
            // Update chat title
            const currentChatTitle = document.getElementById('currentChatTitle');
            if (currentChatTitle) {
                currentChatTitle.textContent = chatItem.querySelector('.chat-item-title').textContent;
            }
            
            // Store current chat ID
            state.currentChatId = chatId;
            
            // 使用缓存的会话数据更新配置字段
            const sessionData = state.chatSessions[chatId];
            if (sessionData) {
                updateChatConfigFields(sessionData);
            }
            
            // Load messages for this chat
            await loadChatMessages(chatId, elements);
        } else {
            console.error('Chat item not found:', chatId);
            showToast('Failed to switch chat: chat not found', 'error');
        }
    }
    
    // Load messages for a specific chat
    async function loadChatMessages(chatId, elements) {
        if (!chatId) {
            console.error('Invalid chat ID for loading messages');
            return;
        }
        
        try {
            const token = localStorage.getItem('token');
            const normalizedToken = typeof normalizeToken === 'function' ? 
                normalizeToken(token) : token;
                
            const response = await fetch(`${API_BASE_URL}/chat/messages/${chatId}`, {
                headers: {
                    'Authorization': normalizedToken
                }
            });
            
            if (!response.ok) {
                console.error('Server error:', response.status, response.statusText);
                showToast(`Failed to load messages: ${response.status}`, 'error');
                return;
            }
            
            const responseText = await response.text();
            
            try {
                const data = JSON.parse(responseText);
                
                if (isSuccessResponse(data)) {
                    // Clear existing messages
                    elements.chatMessages.innerHTML = '';
                    
                    // Add messages to chat
                    if (Array.isArray(data.data) && data.data.length > 0) {
                        data.data.forEach(message => {
                            renderMessage(message, elements);
                        });
                    } else {
                        elements.chatMessages.innerHTML = '<div class="text-center text-muted p-3">No messages yet</div>';
                    }
                    
                    // Scroll to bottom
                    scrollToBottom(elements.chatMessages);
                } else {
                    showToast(data.message || 'Failed to load messages', 'error');
                }
            } catch (parseError) {
                showToast('Error parsing messages response', 'error');
            }
        } catch (error) {
            console.error('Error loading messages:', error);
            showToast('Error loading messages', 'error');
        }
    }
    
    // Send message
    async function sendMessage(elements) {
        const messageText = elements.messageInput.value.trim();
        if (!messageText) return;
        
        const activeChatId = state.currentChatId;
        if (!activeChatId) {
            showToast('请先选择或创建一个对话', 'error');
            return;
        }
        
        // Prevent multiple sends or sending while waiting for response
        if (state.isSending || state.isWaitingForResponse) {
            showToast('消息发送中或正在等待AI回复，请稍候...', 'info');
            return;
        }
        
        // Check network status
        if (!state.networkStatus) {
            showToast('网络连接已断开，请检查网络设置', 'error');
            return;
        }
        
        // Clear input and disable send button
        elements.messageInput.value = '';
        elements.sendButton.disabled = true;
        
        // Set sending state and waiting for response state
        state.isSending = true;
        state.isWaitingForResponse = true;
        state.retryCount = 0;
        
        // Update UI: Disable send button and show stop button
        if (elements.sendButton) elements.sendButton.disabled = true;
        if (elements.stopButton) {
            elements.stopButton.disabled = false;
            elements.stopButton.style.display = 'block';
        }
        
        // Add user message to display
        const userMessage = {
            content: messageText,
            isUser: true,
            createdAt: new Date().toISOString()
        };
        
        const userMessageId = renderMessage(userMessage, elements);
        
        // Show typing indicator
        const typingIndicatorId = addTypingIndicator(elements);
        
        await sendMessageWithRetry(activeChatId, messageText, elements, typingIndicatorId, userMessageId);
        
        // Reset sending state
        state.isSending = false;
    }
    
    // Stop current response
    function stopCurrentResponse(elements) {
        // Set flag to indicate user stopped the response
        if (state.isWaitingForResponse) {
            // Remove typing indicator if present
            const typingIndicator = document.querySelector('.message.ai.typing');
            if (typingIndicator) {
                typingIndicator.remove();
            }
            
            // Add system message about stopping
            const stopMessage = {
                content: "对话已被用户终止。您可以开始新的对话。",
                isUser: false,
                isSystem: true,
                createdAt: new Date().toISOString()
            };
            renderMessage(stopMessage, elements);
            
            // Reset states
            state.isWaitingForResponse = false;
            state.isSending = false;
            
            // Re-enable UI
            if (elements.sendButton) elements.sendButton.disabled = false;
            if (elements.stopButton) {
                elements.stopButton.disabled = true;
                elements.stopButton.style.display = 'none';
            }
            
            showToast('对话已终止', 'info');
        }
    }
    
    // Send message with retry logic
    async function sendMessageWithRetry(chatId, messageText, elements, typingIndicatorId, userMessageId) {
        try {
            const token = localStorage.getItem('token');
            const normalizedToken = typeof normalizeToken === 'function' ? 
                normalizeToken(token) : token;
                
            const requestBody = {
                chatId: chatId,
                message: messageText
            };
            
            const response = await fetch(`${API_BASE_URL}/chat/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': normalizedToken
                },
                body: JSON.stringify(requestBody),
                timeout: 60000 // 60 second timeout
            });
            
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    // Authentication error
                    removeElement(typingIndicatorId);
                    showToast('身份验证失败，请重新登录', 'error');
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 2000);
                    return;
                }
                
                if (response.status === 404) {
                    // Not found error
                    removeElement(typingIndicatorId);
                    showToast('聊天会话不存在或已被删除', 'error');
                    return;
                }
                
                // Server error or other HTTP errors
                console.error('Server error:', response.status, response.statusText);
                
                // Try to retry for server errors
                if (state.retryCount < state.maxRetries) {
                    state.retryCount++;
                    const waitTime = Math.pow(2, state.retryCount) * 1000; // Exponential backoff
                    
                    showToast(`发送失败，${waitTime/1000}秒后重试 (${state.retryCount}/${state.maxRetries})`, 'warning');
                    
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    return await sendMessageWithRetry(chatId, messageText, elements, typingIndicatorId, userMessageId);
                } else {
                    // Max retries reached
                    removeElement(typingIndicatorId);
                    showToast(`发送失败，已达到最大重试次数 (${state.maxRetries})`, 'error');
                    return;
                }
            }
            
            const responseText = await response.text();
            
            try {
                const data = JSON.parse(responseText);
                
                // Remove typing indicator
                removeElement(typingIndicatorId);
                
                if (isSuccessResponse(data)) {
                    const aiMessage = {
                        content: data.data,
                        isUser: false,
                        createdAt: new Date().toISOString()
                    };
                    renderMessage(aiMessage, elements);
                    
                    // Reset waiting state and re-enable UI after receiving response
                    state.isWaitingForResponse = false;
                    if (elements.sendButton) elements.sendButton.disabled = false;
                    if (elements.stopButton) {
                        elements.stopButton.disabled = true;
                        elements.stopButton.style.display = 'none';
                    }
                } else {
                    console.error('API error response:', data);
                    showToast(data.message || 'Failed to send message', 'error');
                    // Add error message to chat
                    const errorMessage = {
                        content: `发送消息失败: ${data.message || '未知错误'}`,
                        isUser: false,
                        isError: true,
                        createdAt: new Date().toISOString()
                    };
                    renderMessage(errorMessage, elements);
                    
                    // Reset waiting state and re-enable UI on error
                    state.isWaitingForResponse = false;
                    if (elements.sendButton) elements.sendButton.disabled = false;
                    if (elements.stopButton) {
                        elements.stopButton.disabled = true;
                        elements.stopButton.style.display = 'none';
                    }
                }
            } catch (parseError) {
                // Remove typing indicator
                removeElement(typingIndicatorId);
                
                console.error('Error parsing response JSON:', parseError);
                showToast('解析服务器响应失败', 'error');
                
                // Add error message to chat
                const errorMessage = {
                    content: '解析服务器响应失败，请联系管理员',
                    isUser: false,
                    isError: true,
                    createdAt: new Date().toISOString()
                };
                renderMessage(errorMessage, elements);
                
                // Reset waiting state and re-enable UI on error
                state.isWaitingForResponse = false;
                if (elements.sendButton) elements.sendButton.disabled = false;
                if (elements.stopButton) {
                    elements.stopButton.disabled = true;
                    elements.stopButton.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Send message failed:', error);
            
            // Network error or timeout
            if (state.retryCount < state.maxRetries) {
                state.retryCount++;
                const waitTime = Math.pow(2, state.retryCount) * 1000; // Exponential backoff
                
                showToast(`网络错误，${waitTime/1000}秒后重试 (${state.retryCount}/${state.maxRetries})`, 'warning');
                
                await new Promise(resolve => setTimeout(resolve, waitTime));
                return await sendMessageWithRetry(chatId, messageText, elements, typingIndicatorId, userMessageId);
            } else {
                // Max retries reached
                removeElement(typingIndicatorId);
                showToast(`发送失败，网络错误: ${error.message}`, 'error');
                
                // Add error message to chat
                const errorMessage = {
                    content: `网络错误: ${error.message}`,
                    isUser: false,
                    isError: true,
                    createdAt: new Date().toISOString()
                };
                renderMessage(errorMessage, elements);
                
                // Reset waiting state and re-enable UI when max retries reached
                state.isWaitingForResponse = false;
                if (elements.sendButton) elements.sendButton.disabled = false;
                if (elements.stopButton) {
                    elements.stopButton.disabled = true;
                    elements.stopButton.style.display = 'none';
                }
            }
        }
    }
    
    // Enhanced render message with error styling
    function renderMessage(message, elements) {
        if (!message) {
            console.error('Invalid message object');
            return null;
        }
        
        // Determine message type
        let messageType;
        
        if (message.isUser === true) {
            messageType = MSG_TYPE.USER;
        } else if (message.isUser === false) {
            if (message.isSystem) {
                messageType = MSG_TYPE.SYSTEM;
            } else {
                messageType = MSG_TYPE.AI;
            }
        } else {
            // Default to system message
            messageType = MSG_TYPE.SYSTEM;
        }
        
        // Create message element
        const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${messageType}${message.isError ? ' error' : ''}`;
        messageDiv.id = messageId;
        
        // Process content
        const content = message.content || '';
        const processedContent = sanitizeHTML(content);
        
        // Create message content
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processedContent;
        messageDiv.appendChild(contentDiv);
        
        // Create message footer (for timestamp and buttons)
        const footerDiv = document.createElement('div');
        footerDiv.className = 'message-footer';
        
        // Add timestamp to footer
        const timeSpan = document.createElement('span');
        timeSpan.className = 'message-time';
        timeSpan.textContent = formatTime(message.createdAt || new Date());
        footerDiv.appendChild(timeSpan);
        
        // Add copy button only for AI messages
        if (messageType === MSG_TYPE.AI && !message.isError) {
            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-icon';
            copyBtn.title = '复制内容';
            copyBtn.innerHTML = '<i class="bi bi-clipboard"></i><span>复制</span>';
            copyBtn.addEventListener('click', () => copyToClipboard(content));
            footerDiv.appendChild(copyBtn);
        }
        
        // Add retry button for error messages
        if (message.isError) {
            const retryBtn = document.createElement('button');
            retryBtn.className = 'retry-icon';
            retryBtn.title = '重试';
            retryBtn.innerHTML = '<i class="bi bi-arrow-repeat"></i><span>重试</span>';
            retryBtn.addEventListener('click', () => {
                // Find the last user message to retry
                const lastUserMessage = findLastUserMessage();
                if (lastUserMessage) {
                    const content = lastUserMessage.querySelector('.message-content').textContent;
                    const elements = getDOMElements();
                    elements.messageInput.value = content;
                    sendMessage(elements);
                }
            });
            footerDiv.appendChild(retryBtn);
        }
        
        // Add footer to message
        messageDiv.appendChild(footerDiv);
        
        // Add to messages container
        elements.chatMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        scrollToBottom(elements.chatMessages);
        
        return messageId;
    }
    
    // Find the last user message
    function findLastUserMessage() {
        const userMessages = document.querySelectorAll('.message.user');
        if (userMessages.length > 0) {
            return userMessages[userMessages.length - 1];
        }
        return null;
    }
    
    // Add typing indicator
    function addTypingIndicator(elements) {
        const id = `typing-${Date.now()}`;
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai typing';
        typingDiv.id = id;
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        elements.chatMessages.appendChild(typingDiv);
        scrollToBottom(elements.chatMessages);
        
        return id;
    }
    
    // Edit chat title by ID - 打开编辑模态框
    function editChatTitle(chatId) {
        if (!chatId) {
            showToast('请先选择一个对话', 'error');
            return;
        }
        
        const titleElement = document.querySelector(`[data-chat-id="${chatId}"] .chat-item-title`);
        const currentTitle = titleElement ? titleElement.textContent : '';
        
        // 填充编辑模态框
        const editTitleInput = document.getElementById('editTitleInput');
        const editChatIdInput = document.getElementById('editChatId');
        
        if (editTitleInput && editChatIdInput) {
            editTitleInput.value = currentTitle;
            editChatIdInput.value = chatId;
            
            // 显示模态框
            const editTitleModal = new bootstrap.Modal(document.getElementById('editTitleModal'));
            editTitleModal.show();
            
            // 聚焦输入框
            setTimeout(() => {
                editTitleInput.focus();
                editTitleInput.select();
            }, 500);
        } else {
            // 如果模态框元素不存在，回退到 prompt
            const newTitle = prompt('请输入新的对话标题', currentTitle);
            if (newTitle && newTitle !== currentTitle) {
                updateChatTitle(chatId, newTitle);
            }
        }
    }
    
    // 保存编辑后的标题
    async function saveEditedTitle() {
        const editTitleInput = document.getElementById('editTitleInput');
        const editChatIdInput = document.getElementById('editChatId');
        
        if (!editTitleInput || !editChatIdInput) {
            showToast('找不到编辑表单元素', 'error');
            return;
        }
        
        const newTitle = editTitleInput.value.trim();
        const chatId = editChatIdInput.value;
        
        if (!chatId) {
            showToast('找不到对话ID', 'error');
            return;
        }
        
        if (!newTitle) {
            showToast('标题不能为空', 'error');
            return;
        }
        
        // 关闭模态框
        const editTitleModal = bootstrap.Modal.getInstance(document.getElementById('editTitleModal'));
        if (editTitleModal) {
            editTitleModal.hide();
        }
        
        // 更新标题
        await updateChatTitle(chatId, newTitle);
    }
    
    // 实际执行更新标题的 API 调用
    async function updateChatTitle(chatId, newTitle) {
        try {
            const token = localStorage.getItem('token');
            const normalizedToken = typeof normalizeToken === 'function' ? 
                normalizeToken(token) : token;
                
            const response = await fetch(`${API_BASE_URL}/chat/title/${chatId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': normalizedToken
                },
                body: JSON.stringify(newTitle)
            });
            
            const data = await response.json();
            
            if (isSuccessResponse(data)) {
                // 更新 UI 中的标题
                const titleElement = document.querySelector(`[data-chat-id="${chatId}"] .chat-item-title`);
                if (titleElement) {
                    titleElement.textContent = newTitle;
                }
                
                // 如果是当前聊天，也更新头部标题
                if (state.currentChatId === chatId) {
                    const currentChatTitle = document.getElementById('currentChatTitle');
                    if (currentChatTitle) {
                        currentChatTitle.textContent = newTitle;
                    }
                }
                
                // 更新缓存的会话数据
                if (state.chatSessions[chatId]) {
                    state.chatSessions[chatId].title = newTitle;
                }
                
                showToast('标题已更新', 'success');
            } else {
                showToast(data.message || '标题更新失败', 'error');
            }
        } catch (error) {
            console.error('更新标题失败:', error);
            showToast('更新标题时发生错误', 'error');
        }
    }
    
    // Edit current chat title
    function editCurrentTitle() {
        const chatId = state.currentChatId;
        if (!chatId) {
            showToast('请先选择一个对话', 'error');
            return;
        }
        
        editChatTitle(chatId);
    }
    
    // Delete chat by ID
    function deleteChatById(chatId) {
        if (!chatId) {
            showToast('请先选择一个对话', 'error');
            return;
        }
        
        // 获取对话标题
        const titleElement = document.querySelector(`[data-chat-id="${chatId}"] .chat-item-title`);
        const chatTitle = titleElement ? titleElement.textContent : '未命名对话';
        
        // 填充确认删除模态框
        const deleteChatTitleSpan = document.getElementById('deleteChatTitle');
        const deleteChatIdInput = document.getElementById('deleteChatId');
        
        if (deleteChatTitleSpan && deleteChatIdInput) {
            deleteChatTitleSpan.textContent = chatTitle;
            deleteChatIdInput.value = chatId;
            
            // 显示确认模态框
            const deleteChatModal = new bootstrap.Modal(document.getElementById('deleteChatModal'));
            deleteChatModal.show();
        } else {
            // 如果模态框元素不存在，回退到 confirm
            if (confirm('确定要删除此对话吗？此操作不可恢复。')) {
                performDeleteChat(chatId);
            }
        }
    }
    
    // 确认删除对话
    async function confirmDeleteChat() {
        const deleteChatIdInput = document.getElementById('deleteChatId');
        
        if (!deleteChatIdInput) {
            showToast('找不到对话ID', 'error');
            return;
        }
        
        const chatId = deleteChatIdInput.value;
        
        if (!chatId) {
            showToast('找不到要删除的对话', 'error');
            return;
        }
        
        // 关闭模态框
        const deleteChatModal = bootstrap.Modal.getInstance(document.getElementById('deleteChatModal'));
        if (deleteChatModal) {
            deleteChatModal.hide();
        }
        
        // 执行删除
        await performDeleteChat(chatId);
    }
    
    // 实际执行删除的 API 调用
    async function performDeleteChat(chatId) {
        try {
            const token = localStorage.getItem('token');
            const normalizedToken = typeof normalizeToken === 'function' ? 
                normalizeToken(token) : token;
                
            const response = await fetch(`${API_BASE_URL}/chat/delete/${chatId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': normalizedToken
                }
            });
            
            const data = await response.json();
            
            if (isSuccessResponse(data)) {
                // Remove chat from UI
                const chatItem = document.querySelector(`[data-chat-id="${chatId}"]`);
                if (chatItem) {
                    chatItem.remove();
                }
                
                // Clear messages if this was the active chat
                if (state.currentChatId === chatId) {
                    // Clear messages
                    const chatMessages = document.getElementById('chatMessages');
                    if (chatMessages) {
                        chatMessages.innerHTML = '';
                    }
                    
                    // Reset current chat ID
                    state.currentChatId = null;
                    
                    // Update title
                    const currentChatTitle = document.getElementById('currentChatTitle');
                    if (currentChatTitle) {
                        currentChatTitle.textContent = '新对话';
                    }
                    
                    // Clear chat config fields
                    const knowIdInput = document.getElementById('knowId');
                    if (knowIdInput) knowIdInput.value = '';
                    
                    const bookUrlInput = document.getElementById('currentBookUrl');
                    if (bookUrlInput) bookUrlInput.value = '';
                }
                
                // Remove from cache
                if (state.chatSessions[chatId]) {
                    delete state.chatSessions[chatId];
                }
                
                showToast('对话已删除', 'success');
                
                // Select another chat if any available
                const chatItems = document.querySelectorAll('.chat-item');
                if (chatItems.length > 0) {
                    const firstChatId = chatItems[0].dataset.chatId;
                    const elements = getDOMElements();
                    switchToChat(firstChatId, elements);
                }
            } else {
                showToast(data.message || '删除对话失败', 'error');
            }
        } catch (error) {
            console.error('删除对话失败:', error);
            showToast('删除对话时发生错误', 'error');
        }
    }
    
    // Delete current chat
    function deleteCurrentChat() {
        const chatId = state.currentChatId;
        if (!chatId) {
            showToast('请先选择一个对话', 'error');
            return;
        }
        
        deleteChatById(chatId);
    }
    
    // Utility Functions
    
    // Format timestamp
    function formatTime(timestamp) {
        if (!timestamp) return '';
        try {
            return new Date(timestamp).toLocaleString();
        } catch (e) {
            console.error('Error formatting time:', e);
            return '';
        }
    }
    
    // Sanitize HTML content
    function sanitizeHTML(html) {
        if (!html) return '';
        
        // Handle both string and HTML content
        if (typeof html !== 'string') {
            html = String(html);
        }
        
        // Check if the content already has HTML tags
        const hasHTML = /<[a-z][\s\S]*>/i.test(html);
        
        if (hasHTML) {
            // Content already has HTML - sanitize it
            // Use DOMPurify or similar library in production
            return html;
        } else {
            // Plain text - escape HTML and convert newlines to <br>
            return html
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;')
                .replace(/\n/g, '<br>');
        }
    }
    
    // Copy to clipboard
    function copyToClipboard(text) {
        if (!text) return;
        
        // Modern clipboard API
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text)
                .then(() => showToast('Copied to clipboard', 'success'))
                .catch(err => {
                    console.error('Clipboard write failed:', err);
                    fallbackCopyToClipboard(text);
                });
        } else {
            fallbackCopyToClipboard(text);
        }
    }
    
    // Fallback copy method
    function fallbackCopyToClipboard(text) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.opacity = 0;
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showToast('Copied to clipboard', 'success');
            } else {
                showToast('Copy failed', 'error');
            }
        } catch (err) {
            console.error('Fallback copy failed:', err);
            showToast('Copy failed', 'error');
        }
        
        document.body.removeChild(textarea);
    }
    
    // Remove element by ID
    function removeElement(id) {
        if (!id) return;
        const element = document.getElementById(id);
        if (element) {
            element.remove();
        }
    }
    
    // Scroll to bottom of container
    function scrollToBottom(container) {
        if (!container) return;
        container.scrollTop = container.scrollHeight;
    }
    
    // Show toast notification
    function showToast(message, type = 'info') {
        // Check if there's a global showToast function
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
            return;
        }
        
        // Create toast container if it doesn't exist
        const toastContainer = document.getElementById('toast-container') || createToastContainer();
        
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast ${type === 'error' ? 'bg-danger' : type === 'success' ? 'bg-success' : 'bg-info'} text-white`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="toast-header">
                <strong class="me-auto">${type === 'error' ? 'Error' : type === 'success' ? 'Success' : 'Info'}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        try {
            // Use Bootstrap 5 toast if available
            const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();
        } catch (e) {
            // Fallback for when Bootstrap JS is not loaded
            console.error('Bootstrap Toast failed:', e);
            toast.style.display = 'block';
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    }
    
    // Create toast container
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    }
    
    // Check for success response
    function isSuccessResponse(data) {
        return data && (data.code === 200 || data.code === 0);
    }

    // Utility function to check authentication
    async function checkAuth() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                return null;
            }
            
            // 使用normalizeToken进行标准化处理
            const normalizedToken = typeof normalizeToken === 'function' ? 
                normalizeToken(token) : token;
            
            // 使用正确的API路径，确保完整路径
            const response = await fetch(`${API_BASE_URL}/user/current`, {
                headers: {
                    'Authorization': normalizedToken
                }
            });
            
            if (!response.ok) {
                console.warn('认证失败，状态码:', response.status);
                return null;
            }
            
            const data = await response.json();
            if (data && data.code === 200) {
                return data.data;
            }
            
            console.warn('认证响应格式错误:', data);
            return null;
        } catch (error) {
            console.error('认证请求出错:', error);
            return null;
        }
    }

    // 确保normalizeToken函数可用
    if (typeof normalizeToken !== 'function') {
        console.warn('未找到normalizeToken函数，使用兼容实现');
        
        // Token标准化的兼容实现
        function normalizeToken(token) {
            if (!token) return null;
            if (typeof token !== 'string') return null;
            
            // 移除可能存在的Bearer前缀
            if (token.toLowerCase().startsWith('bearer ')) {
                return token.substring(7);
            }
            return token;
        }
        
        // 设置为全局函数
        window.normalizeToken = normalizeToken;
    }
})(); 