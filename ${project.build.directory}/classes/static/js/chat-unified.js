// ================= CHAT UNIFIED SCRIPT =================
// This script combines logic from chat-state.js, chat-state-fix.js, and chat-fix.js

(function(window, jQuery) {
    'use strict';

    // Ensure jQuery is available
    if (typeof jQuery === 'undefined') {
        console.error("jQuery is not loaded. Chat functionality will be severely limited.");
        // Optionally, you could implement a very basic non-jQuery fallback here for critical UI,
        // but most of the current logic relies on it.
        return;
    }

    const $ = jQuery; // Use $ alias for convenience

    // -------------------------------------------------------------------------
    // ChatState Management (Based on chat-state.js, with robustness from fix)
    // -------------------------------------------------------------------------
    const ChatState = {
        version: '2.0.0-unified',
        current: {
            chatId: null, // Added for clarity, will be set by switchToChat
            bookUrl: '',
            knowId: '',
            title: '新对话'
        },
        // newChat state seems less used if new chats are immediately created via API
        // For now, we'll keep it simple and focus on current chat state.
        // If a pre-creation state is needed, it can be re-added.

        _initialized: false,

        init: function() {
            if (this._initialized) {
                console.warn('[ChatState] Already initialized.');
                return;
            }
            this.loadFromStorage(); // Load initial state if any
            // Event bindings for direct state inputs (knowId, bookUrl) are now part of chat-fix logic
            // as they are tied to the current chat context.
            this.updateGlobalStateUI(); // Update UI elements tied to global state if any
            this._initialized = true;
        },

        loadFromStorage: function() {
            try {
                const savedCurrent = localStorage.getItem('chatUnified_currentChat');
                if (savedCurrent) {
                    const parsed = JSON.parse(savedCurrent);
                    // Merge carefully, don't overwrite with potentially stale/incomplete data
                    this.current.bookUrl = parsed.bookUrl || this.current.bookUrl;
                    this.current.knowId = parsed.knowId || this.current.knowId;
                    this.current.title = parsed.title || this.current.title;
                    this.current.chatId = parsed.chatId || this.current.chatId;
                }
            } catch (e) {
                console.error('[ChatState] Error loading state from localStorage:', e);
            }
        },

        saveToStorage: function() {
            try {
                localStorage.setItem('chatUnified_currentChat', JSON.stringify(this.current));
                console.log('[ChatState] Saved state to localStorage:', JSON.parse(JSON.stringify(this.current)));
            } catch (e) {
                console.error('[ChatState] Error saving state to localStorage:', e);
            }
        },

        // Updates UI elements directly tied to the ChatState.current object
        // This is primarily for the knowId and bookUrl input fields.
        updateCurrentChatUIFields: function() {
            console.log('[ChatState] Updating UI fields for current chat:', JSON.parse(JSON.stringify(this.current)));
            $('#knowId').val(this.current.knowId || '');
            $('#bookUrl').val(this.current.bookUrl || '');
            // Title is updated by switchToChat or handleEditTitle
        },
        
        // Updates UI elements that reflect global or non-chat specific state
        updateGlobalStateUI: function() {
            // Example: if there was a global setting managed by ChatState
            // For now, this can be a placeholder.
        },

        setCurrentChatDetails: function(details) {
            if (!details) return;
            this.current.chatId = details.id ? String(details.id) : this.current.chatId;
            this.current.title = details.title || '新对话';
            this.current.knowId = details.knowId || '';
            this.current.bookUrl = details.bookUrl || '';
            console.log('[ChatState] Set current chat details:', JSON.parse(JSON.stringify(this.current)));
            this.saveToStorage();
            this.updateCurrentChatUIFields(); // Update input fields
        },

        resetCurrentChatDetails: function() {
            this.current.chatId = null;
            this.current.title = '新对话';
            this.current.knowId = '';
            this.current.bookUrl = '';
            console.log('[ChatState] Reset current chat details.');
            this.saveToStorage();
            this.updateCurrentChatUIFields();
            $('#currentChatTitle').text(this.current.title);
        },

        // Getter for current chat ID (used by other parts of the app)
        getCurrentChatId: function() {
            return this.current.chatId;
        },

        // Getters for knowId and bookUrl for the current chat
        getCurrentKnowId: function() {
            return $('#knowId').val() || this.current.knowId || '';
        },
        getCurrentBookUrl: function() {
            return $('#bookUrl').val() || this.current.bookUrl || '';
        }
    };

    // Expose ChatState globally if needed, or pass as dependency
    window.UnifiedChatState = ChatState;


    // -------------------------------------------------------------------------
    // Main Chat Application Logic (Based on chat-fix.js)
    // -------------------------------------------------------------------------
    const MainChatApp = {
        state: { // Local state for MainChatApp, distinct from ChatState's persisted state
            chatSessionsCache: {}, // Cache for full chat session objects { id: { full_chat_object } }
            currentUser: null,     // { username, avatar }
            currentFetchController: null,
            isSendingMessage: false, // Flag to prevent multiple send requests
            isDeleting: false       // Added for delete operation control
        },

        // --- UTILITY FUNCTIONS ---
        getAuthToken: function() {
            const token = localStorage.getItem('token');
            // 如果已存储的 token 包含 "Bearer "，则移除它
            if (token && token.startsWith("Bearer ")) {
                return token.substring(7).trim();
            }
            return token;
        },

        escapeHtml: function(unsafe) {
            if (typeof unsafe !== 'string') return '';
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        },

        formatDateTime: function(dateString) {
            if (!dateString) return new Date().toLocaleString('zh-CN', { hour12: false });
            try {
                return new Date(dateString).toLocaleString('zh-CN', { hour12: false });
            } catch (e) { return dateString; }
        },

        showToast: function(message, type = 'info', duration = 3000) { // Added duration
            if (typeof window.showToast === 'function') { // Prefer global toast if available
                window.showToast(message, type, duration);
            } else {
                console.log(`[Toast-${type}] (${duration}ms): ${message}`);
                // A simple fallback toast mechanism could be added here if window.showToast isn't guaranteed
                const toastId = 'chat-unified-toast';
                let $toast = $('#' + toastId);
                if (!$toast.length) {
                    $toast = $('<div id="' + toastId + '"></div>').css({
                        position: 'fixed', top: '20px', right: '20px', padding: '10px 20px',
                        backgroundColor: type === 'error' ? '#f8d7da' : (type === 'success' ? '#d4edda' : '#cce5ff'),
                        color: type === 'error' ? '#721c24' : (type === 'success' ? '#155724' : '#004085'),
                        border: '1px solid',
                        borderColor: type === 'error' ? '#f5c6cb' : (type === 'success' ? '#c3e6cb' : '#b8daff'),
                        borderRadius: '5px', zIndex: 1050, display: 'none'
                    }).appendTo('body');
                }
                $toast.text(message).fadeIn();
                setTimeout(() => $toast.fadeOut(() => $toast.remove()), duration);
            }
        },

        enableChatInputs: function(enable) {
            $('#messageInput').prop('disabled', !enable);
            $('#sendMessage').prop('disabled', !enable);
            $('#knowId').prop('disabled', !enable); // These might be global or per-chat
            $('#bookUrl').prop('disabled', !enable);
            console.log('[MainChatApp] Chat inputs ' + (enable ? 'enabled' : 'disabled'));
        },

        // --- AVATAR FUNCTIONS ---
        fixDefaultAvatar: function() {
            const defaultUserAvatarPath = '/static/images/default-avatar.png';
            const $navUserAvatar = $('.nav-user .avatar');

            if ($navUserAvatar.length) {
                $navUserAvatar.off('error.avatarfix').on('error.avatarfix', function() {
                    console.warn('[MainChatApp] Navbar avatar failed, using default.');
                    $(this).attr('src', defaultUserAvatarPath);
                });
                // Check if already broken (e.g. cached broken image)
                if ($navUserAvatar[0].complete && (typeof $navUserAvatar[0].naturalWidth === "undefined" || $navUserAvatar[0].naturalWidth === 0)) {
                     console.warn('[MainChatApp] Navbar avatar already broken, setting default.');
                    $navUserAvatar.attr('src', defaultUserAvatarPath);
                }
            }
            // Set current user avatar for messages
            this.state.currentUser = this.state.currentUser || {};
            this.state.currentUser.avatar = ($navUserAvatar.length && $navUserAvatar.attr('src') !== defaultUserAvatarPath) ? $navUserAvatar.attr('src') : defaultUserAvatarPath;
        },

        getMessageAvatar: function(isUser) {
            const defaultUserAvatarPath = '/static/images/default-avatar.png';
            const defaultAiAvatarPath = '/static/images/ai-avatar.png'; // Ensure this exists
            if (isUser) {
                return (this.state.currentUser && this.state.currentUser.avatar) ? this.state.currentUser.avatar : defaultUserAvatarPath;
            }
            return defaultAiAvatarPath;
        },

        // --- CHAT HISTORY AND SESSION MANAGEMENT ---
        loadChatHistory: async function() {
            const $chatHistoryEl = $('#chatHistory');
            if (!$chatHistoryEl.length) {
                console.error('[MainChatApp] #chatHistory element not found!');
                return;
            }
            $chatHistoryEl.html('<div class="p-3 text-center text-muted">Loading history...</div>');

            try {
                const token = this.getAuthToken();
                
                const response = await fetch('/api/chat/history', {
                    headers: { 'Authorization': token }
                });
                if (!response.ok) throw new Error(`History fetch failed: ${response.status}`);
                const result = await response.json();

                if (result.code === 200 && Array.isArray(result.data)) {
                    $chatHistoryEl.empty();
                    this.state.chatSessionsCache = {}; // Clear local cache of full sessions
                    UnifiedChatState.resetCurrentChatDetails(); // Reset persisted state for current chat initially

                    if (result.data.length === 0) {
                        $chatHistoryEl.html('<div class="p-3 text-center text-muted">No conversations yet.</div>');
                        $('#currentChatTitle').text('New Chat');
                        $('#chatMessages').html('<div class="text-center text-muted p-4">Start by creating a new conversation.</div>');
                        this.enableChatInputs(false);
                        history.replaceState(null, '', '/main/chat');
                        return;
                    }

                    result.data.forEach(chat => {
                        // Store minimal info in ChatState if needed, or just use chatSessionsCache
                        // For now, addChatToHistoryDOM will handle display
                        this.addChatToHistoryDOM(chat);
                    });

                    const urlParams = new URLSearchParams(window.location.search);
                    const chatIdFromUrl = urlParams.get('id');

                    if (chatIdFromUrl && result.data.some(c => String(c.id) === chatIdFromUrl)) {
                        await this.switchToChat(chatIdFromUrl);
                    } else if (result.data.length > 0) {
                        const mostRecentChat = result.data.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))[0];
                        await this.switchToChat(String(mostRecentChat.id));
                    }
                } else {
                    throw new Error(result.message || 'Failed to parse chat history');
                }
            } catch (error) {
                console.error('[MainChatApp] Error loading chat history:', error);
                $chatHistoryEl.html(`<div class="alert alert-warning m-2">Could not load history: ${this.escapeHtml(error.message)}</div>`);
                this.enableChatInputs(false);
            }
        },

        addChatToHistoryDOM: function(chat, prepend = false) {
            const $chatHistoryEl = $('#chatHistory');
            if (!$chatHistoryEl.length) return;

            const formattedTime = this.formatDateTime(chat.updatedAt);
            const $chatItemEl = $(`
                <div class="chat-item" data-chat-id="${String(chat.id)}" role="button" tabindex="0">
                    <div class="chat-item-icon"><i class="bi bi-chat-left-text"></i></div>
                    <div class="chat-item-content">
                        <div class="chat-title">${this.escapeHtml(chat.title || '未命名对话')}</div>
                        <div class="chat-item-time">
                            <i class="bi bi-clock"></i> ${formattedTime}
                        </div>
                    </div>
                    <div class="chat-item-actions">
                        <button class="chat-item-edit" title="编辑标题">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="chat-item-delete" title="删除聊天">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `);

            // 绑定编辑按钮点击事件
            $chatItemEl.find('.chat-item-edit').on('click', (e) => {
                e.stopPropagation(); // 防止触发聊天项点击事件
                this.handleEditChatTitle(chat.id, chat.title);
            });
            
            // 绑定删除按钮点击事件
            $chatItemEl.find('.chat-item-delete').on('click', (e) => {
                e.stopPropagation(); // 防止触发聊天项点击事件
                this.handleDeleteChat(chat.id, chat.title);
            });

            // 重要：添加整个聊天项的点击事件
            $chatItemEl.on('click', () => {
                this.switchToChat(chat.id);
            });

            if (prepend) {
                $chatHistoryEl.prepend($chatItemEl);
            } else {
                $chatHistoryEl.append($chatItemEl);
            }
        },

        switchToChat: async function(chatId) {
            chatId = String(chatId); // Ensure string
            console.log(`[MainChatApp] Switching to chat: ${chatId}`);
            if (!chatId) {
                console.error('[MainChatApp] switchToChat: Chat ID is null.');
                return;
            }

            UnifiedChatState.setCurrentChatDetails({ id: chatId }); // Update ChatState's currentChatId

            $('.chat-item').removeClass('active');
            $(`.chat-item[data-chat-id="${chatId}"]`).addClass('active');

            const newUrl = `/main/chat?id=${chatId}`;
            if (window.location.pathname + window.location.search !== newUrl) {
                history.pushState({ chatId }, document.title, newUrl);
            }

            try {
                const token = this.getAuthToken();
                const response = await fetch(`/api/chat/${chatId}`, {
                    headers: { 'Authorization': token }
                });
                if (!response.ok) throw new Error(`Chat details fetch failed: ${response.status}`);
                const result = await response.json();

                if (result.code === 200 && result.data) {
                    const chatSession = result.data;
                    this.state.chatSessionsCache[chatId] = chatSession; // Cache full session
                    
                    UnifiedChatState.setCurrentChatDetails(chatSession); // Update persisted state and UI fields

                    $('#currentChatTitle').text(chatSession.title || 'Untitled Chat');
                    
                    await this.loadChatMessages(chatId);
                    this.enableChatInputs(true);
                    $('#messageInput').focus();
                } else {
                    throw new Error(result.message || 'Could not parse chat details');
                }
            } catch (error) {
                console.error('[MainChatApp] Error in switchToChat (fetching details):', error);
                this.showToast(`Error loading chat ${chatId}: ${error.message}`, 'error');
                $('#currentChatTitle').text('Error Loading Chat');
                $('#chatMessages').html(`<div class="alert alert-danger m-3">Failed to load chat: ${this.escapeHtml(error.message)}</div>`);
                this.enableChatInputs(false);
            }
        },

        // --- MESSAGES ---
        loadChatMessages: async function(chatId) {
            chatId = String(chatId);
            console.log(`[MainChatApp] Loading messages for chat ID: ${chatId}`);
            const $chatMessagesEl = $('#chatMessages');
            if (!$chatMessagesEl.length) {
                console.error('[MainChatApp] #chatMessages element not found!');
                return;
            }
            $chatMessagesEl.html('<div class="loading-indicator p-3 text-center">Loading messages...</div>');

            // Endpoint from ChatController.java: @GetMapping("/messages/{chatId}")
            const messagesApiEndpoint = `/api/chat/messages/${chatId}`;

            try {
                const token = this.getAuthToken();
                const response = await fetch(messagesApiEndpoint, {
                    headers: { 'Authorization': token }
                });
                if (!response.ok) throw new Error(`Messages fetch failed: ${response.status}`);
                const result = await response.json();
                console.log(`[MainChatApp] Messages API response for ${chatId}:`, result);

                $chatMessagesEl.empty();
                if (result.code === 200 && Array.isArray(result.data)) {
                    if (result.data.length === 0) {
                        $chatMessagesEl.html('<div class="text-center text-muted p-4">No messages yet. Send one to start!</div>');
                    } else {
                        result.data.forEach(msg => this.addMessageToUI(msg));
                    }
                } else {
                     $chatMessagesEl.html('<div class="text-center text-muted p-4">Could not load messages.</div>');
                    throw new Error(result.message || 'Failed to parse messages');
                }
                $chatMessagesEl.scrollTop($chatMessagesEl[0].scrollHeight);
            } catch (error) {
                console.error('[MainChatApp] Error loading chat messages:', error);
                $chatMessagesEl.html(`<div class="alert alert-warning m-2">Could not load messages: ${this.escapeHtml(error.message)}</div>`);
            }
        },

        addMessageToUI: function(message, isUserMessage = undefined, isTyping = false) {
            const $chatMessagesEl = $('#chatMessages');
            if (!$chatMessagesEl.length) return;

            // 确定消息类型
            let isUser;
            if (typeof isUserMessage === 'boolean') {
                isUser = isUserMessage;
            } else if (message.isUser !== undefined) {
                isUser = message.isUser;
            } else {
                isUser = message.sender === 'user';
            }
            
            const senderClass = isUser ? 'user' : 'ai';
            const avatarSrc = this.getMessageAvatar(isUser);
            
            // 处理消息内容
            let contentHtml = '';
            let rawContent = (message.content || '').trim();
            let contentType = 'text'; // 默认文本类型
            
            if (!isTyping) {
                if (message.messageType === 'code') {
                    contentHtml = `<pre><code>${this.escapeHtml(rawContent)}</code></pre>`;
                    contentType = 'code';
                } else if (rawContent.startsWith('{') && rawContent.endsWith('}')) {
                    try {
                        let jsonObj = JSON.parse(rawContent);
                        contentHtml = `<pre class="json-content">${this.escapeHtml(JSON.stringify(jsonObj, null, 2))}</pre>`;
                        contentType = 'json';
                    } catch (e) {
                        contentHtml = this.formatMessageText(rawContent);
                    }
                } else {
                    contentHtml = this.formatMessageText(rawContent);
                    // 短文本使用特殊类
                    if (rawContent.length < 50 && !rawContent.includes('\n')) {
                        contentType = 'short-text';
                    }
                }
            } else {
                contentHtml = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
                contentType = 'typing';
            }

            // 修改消息结构 - 将时间戳放在气泡外部
            const $messageEl = $(`
                <div class="message ${senderClass} ${contentType} ${isTyping ? 'typing' : ''}" data-message-id="${message.id || ''}">
                    <img src="${avatarSrc}" alt="Avatar" class="avatar">
                    <div class="message-wrapper">
                        <div class="message-bubble">
                            <div class="message-text">${contentHtml}</div>
                            ${!isTyping && !isUser ? `<button class="copy-message" title="复制内容"><i class="bi bi-clipboard"></i></button>` : ''}
                        </div>
                        ${!isTyping && message.timestamp ? 
                            `<div class="message-time">${this.formatDateTime(message.timestamp)}</div>` : ''}
                    </div>
                </div>
            `);

            if ($chatMessagesEl.find('.message.ai.typing').length > 0) {
                $chatMessagesEl.find('.message.ai.typing').remove();
            }

            $chatMessagesEl.append($messageEl);
            $chatMessagesEl.scrollTop($chatMessagesEl[0].scrollHeight);
            
            // 添加复制功能
            if (!isTyping && !isUser) {
                const self = this;
                $messageEl.find('.copy-message').on('click', function() {
                    const textToCopy = message.content || '';
                    navigator.clipboard.writeText(textToCopy).then(
                        function() { self.showToast('已复制到剪贴板', 'success', 1500); }, 
                        function(err) {
                            console.error('复制失败:', err);
                            self.showToast('复制失败', 'error', 1500);
                        }
                    );
                });
            }
        },

        // 更精简的文本格式化
        formatMessageText: function(text) {
            // 去除多余空行
            text = text.replace(/\n{3,}/g, '\n\n')
                       .replace(/^\s+|\s+$/g, ''); // 移除首尾空白
            return this.escapeHtml(text).replace(/\n/g, '<br>');
        },

        handleSendMessage: async function() {
            if (this.state.isSendingMessage) {
                return; // 防止重复发送
            }
            
            const $messageInput = $('#messageInput');
            const messageText = $messageInput.val().trim();
            const currentChatId = UnifiedChatState.getCurrentChatId();
            
            if (!messageText) {
                return; // 如果消息为空，不做任何处理
            }
            
            // 切换到停止按钮
            $('#sendMessage').hide();
            $('#stopResponse').show();
            
            // 设置发送状态
            this.state.isSendingMessage = true;
            
            const userMessage = {
                id: `temp-${Date.now()}`, // Temporary ID for UI
                content: messageText,
                sender: 'user', // Or use isUser: true
                isUser: true,
                timestamp: new Date().toISOString()
            };
            this.addMessageToUI(userMessage, true);
            $messageInput.val(''); // Clear input

            // Add AI typing indicator
            this.addMessageToUI({ content: '' }, false, true);

            try {
                // Endpoint from ChatController.java: @PostMapping("/send")
                // Request body: ChatMessageRequest { chatId, message, knowId, bookUrl }
                const requestBody = {
                    chatId: currentChatId,
                    message: messageText,
                    knowId: UnifiedChatState.getCurrentKnowId(),
                    bookUrl: UnifiedChatState.getCurrentBookUrl()
                };
                console.log('[MainChatApp] Sending message with body:', requestBody);

                const token = this.getAuthToken();
                const response = await fetch('/api/chat/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token
                    },
                    body: JSON.stringify(requestBody)
                });
                
                // Remove AI typing indicator
                $('#chatMessages .message.ai.typing').remove();

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: `API Error (${response.status})` }));
                    throw new Error(errorData.message || `Send failed: ${response.status}`);
                }
                const result = await response.json();

                if (result.code === 200 && result.data) {
                    // Assuming result.data is the AI's response message object
                    this.addMessageToUI(result.data, false);
                    // Update chat history item's timestamp
                    const chatSession = this.state.chatSessionsCache[currentChatId] || UnifiedChatState.current;
                    if (chatSession) {
                        chatSession.updatedAt = new Date().toISOString(); // Update with server time if available
                        this.updateChatHistoryItemDOM(currentChatId, chatSession.title, chatSession.updatedAt);
                    }
                } else {
                    throw new Error(result.message || 'Invalid response from send message API.');
                }
            } catch (error) {
                console.error('[MainChatApp] Error sending message:', error);
                this.showToast(`Send error: ${error.message}`, 'error');
                // Remove AI typing indicator on error too
                $('#chatMessages .message.ai.typing').remove();
                // Optionally add an error message to UI
                this.addMessageToUI({ content: `Error: ${error.message}`, sender: 'system' }, false);
            } finally {
                this.state.isSendingMessage = false;
                $('#stopResponse').hide();
                $('#sendMessage').show();
                $('#messageInput').trigger('input'); // 重新检查输入状态
            }
        },
        
        updateChatHistoryItemDOM: function(chatId, newTitle, newTimestamp) {
            const $chatItem = $(`.chat-item[data-chat-id="${chatId}"]`);
            if ($chatItem.length) {
                if (newTitle) {
                    $chatItem.find('.chat-item-title').text(this.escapeHtml(newTitle));
                }
                if (newTimestamp) {
                    $chatItem.find('.chat-item-time').html(`<i class="bi bi-clock"></i> ${this.formatDateTime(newTimestamp)}`);
                }
                // Re-sort history visually if timestamp changed
                const $chatHistoryEl = $('#chatHistory');
                const items = Array.from($chatHistoryEl.children('.chat-item'));
                items.sort((a,b) => {
                    // This sort requires access to the full session object or updatedAt on the element
                    // For simplicity, if we only update one item, we might just move it to top
                    // Or, re-fetch history for perfect order, but that's heavy.
                    // Let's assume for now the order is mostly maintained or minor re-ordering is acceptable.
                    // A more robust sort would need to fetch updatedAt from a reliable source for each item.
                    const timeA = new Date($(a).find('.chat-item-time').text().replace(/.* /, '') || 0); // Crude extraction
                    const timeB = new Date($(b).find('.chat-item-time').text().replace(/.* /, '') || 0);
                    return timeB - timeA;
                });
                // $chatHistoryEl.append(items); // Re-append sorted items
            }
        },

        // --- CHAT MANAGEMENT ACTIONS (New, Edit, Delete) ---
        handleNewChat: async function() {
            console.log('[MainChatApp] handleNewChat called.');
            try {
                this.showToast('正在创建新对话...', 'info');
                const knowIdVal = UnifiedChatState.getCurrentKnowId(); // Get from ChatState/UI
                const bookUrlVal = UnifiedChatState.getCurrentBookUrl();

                const token = this.getAuthToken();
                const response = await fetch('/api/chat/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token
                    },
                    body: JSON.stringify({
                        title: "新对话", // Backend might set a default based on book etc.
                        knowId: knowIdVal || null, // Send null if empty, backend handles defaults
                        bookUrl: bookUrlVal || null
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: `API Error (${response.status})` }));
                    throw new Error(errorData.message || `Create chat failed: ${response.status}`);
                }
                const result = await response.json();

                if (result.code === 200 && result.data && result.data.id) {
                    const newChatSession = result.data; // This should be the full session object
                    this.state.chatSessionsCache[String(newChatSession.id)] = newChatSession;
                    this.addChatToHistoryDOM(newChatSession, true); // Prepend
                    await this.switchToChat(String(newChatSession.id));
                    this.showToast('新对话已创建！', 'success');
                } else {
                    throw new Error(result.message || '创建对话失败，响应格式无效。');
                }
            } catch (error) {
                console.error('[MainChatApp] Error creating new chat:', error);
                this.showToast(`创建对话失败: ${error.message}`, 'error');
            }
        },

        handleEditChatTitle: function(chatId, currentTitle) {
            // 如果未传入标题，尝试从UI获取
            if (!currentTitle) {
                currentTitle = $(`.chat-item[data-chat-id="${chatId}"] .chat-title`).text() || '未命名对话';
            }
            
            // 设置模态框中的值
            $('#newTitleInput').val(currentTitle);
            
            // 清除旧的事件绑定
            $('#confirmEditTitleBtn').off('click');
            
            // 绑定保存按钮点击事件
            $('#confirmEditTitleBtn').on('click', async () => {
                const newTitle = $('#newTitleInput').val().trim();
                if (!newTitle) {
                    this.showToast('标题不能为空', 'warning');
                    return;
                }
                
                try {
                    const token = this.getAuthToken();
                    const response = await fetch('/api/chat/update-title', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': token
                        },
                        body: JSON.stringify({
                            chatId: chatId,
                            newTitle: newTitle
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 200) {
                        // 更新UI
                        $(`.chat-item[data-chat-id="${chatId}"] .chat-title`).text(newTitle);
                        
                        // 如果是当前聊天，同时更新顶部标题
                        if (UnifiedChatState.getCurrentChatId() === String(chatId)) {
                            $('#currentChatTitle').text(newTitle);
                        }
                        
                        this.showToast('标题已更新', 'success');
                        bootstrap.Modal.getInstance(document.getElementById('editTitleModal')).hide();
                    } else {
                        throw new Error(result.message || '更新标题失败');
                    }
                } catch (error) {
                    console.error('[MainChatApp] 更新标题出错:', error);
                    this.showToast(`更新标题失败: ${error.message}`, 'error');
                }
            });
            
            // 使用Bootstrap 5 API显示模态框
            const editModalEl = document.getElementById('editTitleModal');
            const editModal = new bootstrap.Modal(editModalEl);
            editModal.show();
            
            // 确保在显示后聚焦输入框
            editModalEl.addEventListener('shown.bs.modal', function() {
                $('#newTitleInput').focus();
            }, { once: true });
        },

        handleDeleteChat: function(chatId, chatTitle) {
            // 防止重复点击
            if (this.state.isDeleting) {
                console.log('Delete operation already in progress');
                return;
            }
            
            this.state.isDeleting = true;
            
            // 如果未传入标题，尝试从UI获取
            if (!chatTitle) {
                chatTitle = $(`.chat-item[data-chat-id="${chatId}"] .chat-title`).text() || '未命名对话';
            }
            
            // 设置确认对话框中的标题
            $('#deleteChatTitlePreview').text(chatTitle);
            
            // 使用Bootstrap 5 API显示模态框
            const deleteModalEl = document.getElementById('deleteConfirmModal');
            const deleteModal = new bootstrap.Modal(deleteModalEl);
            deleteModal.show();
            
            // 解除旧的事件绑定，防止重复
            $('#confirmDeleteModalBtn').off('click');
            
            // 绑定确认删除按钮点击事件
            $('#confirmDeleteModalBtn').on('click', async () => {
                try {
                    const token = this.getAuthToken();
                    const response = await fetch(`/api/chat/${chatId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': token
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 200) {
                        // 从UI中移除
                        $(`.chat-item[data-chat-id="${chatId}"]`).fadeOut(300, function() {
                            $(this).remove();
                        });
                        
                        // 如果删除的是当前聊天，切换到其他聊天或创建新聊天
                        if (UnifiedChatState.getCurrentChatId() === String(chatId)) {
                            // 查找第一个可用的聊天
                            const firstChatId = $('.chat-item').not(`[data-chat-id="${chatId}"]`).first().data('chat-id');
                            
                            if (firstChatId) {
                                // 切换到第一个可用的聊天
                                this.switchToChat(firstChatId);
                            } else {
                                // 没有其他聊天，创建新聊天
                                this.handleNewChat();
                            }
                        }
                        
                        this.showToast('聊天已删除', 'success');
                        bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
                    } else {
                        throw new Error(result.message || '删除聊天失败');
                    }
                } catch (error) {
                    console.error('[MainChatApp] 删除聊天出错:', error);
                    this.showToast(`删除聊天失败: ${error.message}`, 'error');
                } finally {
                    // 解除删除状态锁定
                    this.state.isDeleting = false;
                }
            });
            
            // 在模态框关闭时解除删除状态锁定
            deleteModalEl.addEventListener('hidden.bs.modal', () => {
                this.state.isDeleting = false;
            });
        },

        // --- EVENT LISTENERS SETUP ---
        setupEventListeners: function() {
            
            const self = this; // To access MainChatApp methods inside handlers

            // Ensure DOM is fully loaded
            $(document).ready(function() {

                function ensureElementAndBind(selector, eventType, handler, isDelegated = false, delegateTarget = null) {
                    const $targetElement = isDelegated ? $(selector) : $(selector);
                    if ($targetElement.length) {
                        if (isDelegated && delegateTarget) {
                            $targetElement.off(eventType + '.chatapp').on(eventType + '.chatapp', delegateTarget, handler);
                        } else {
                            $targetElement.off(eventType + '.chatapp').on(eventType + '.chatapp', handler);
                        }
                        console.log(`[MainChatApp] Bound '${eventType}' to '${selector}'` + (isDelegated ? ` (delegated for '${delegateTarget}')` : ''));
                    } else {
                        console.warn(`[MainChatApp] Element NOT FOUND for binding: '${selector}'.`);
                    }
                }

                ensureElementAndBind('#newChatBtn', 'click', function() { self.handleNewChat(); });
                ensureElementAndBind('#chatHistory', 'click', function(e) {
                    e.preventDefault();
                    const chatId = $(this).data('id'); // 'this' is '.chat-item'
                    if (chatId) self.switchToChat(String(chatId));
                }, true, '.chat-item');
                
                ensureElementAndBind('#sendMessage', 'click', function() { self.handleSendMessage(); });
                ensureElementAndBind('#editTitleBtn', 'click', function() {
                    const currentChatId = UnifiedChatState.getCurrentChatId();
                    if (currentChatId) {
                        const currentChatTitle = $('#currentChatTitle').text();
                        self.handleEditChatTitle(currentChatId, currentChatTitle);
                    } else {
                        self.showToast('请先选择一个聊天', 'warning');
                    }
                });
                ensureElementAndBind('#deleteChatBtn', 'click', function() {
                    const currentChatId = UnifiedChatState.getCurrentChatId();
                    if (currentChatId) {
                        const currentChatTitle = $('#currentChatTitle').text();
                        self.handleDeleteChat(currentChatId, currentChatTitle);
                    } else {
                        self.showToast('请先选择一个聊天', 'warning');
                    }
                });
                
                ensureElementAndBind('#messageInput', 'keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        self.handleSendMessage();
                    }
                });

                // Listen to changes on knowId and bookUrl to update ChatState
                ensureElementAndBind('#knowId', 'change input', function() {
                    if (UnifiedChatState.getCurrentChatId()) { // Only if a chat is active
                        UnifiedChatState.current.knowId = $(this).val();
                        UnifiedChatState.saveToStorage();
                    }
                });
                ensureElementAndBind('#bookUrl', 'change input', function() {
                     if (UnifiedChatState.getCurrentChatId()) { // Only if a chat is active
                        UnifiedChatState.current.bookUrl = $(this).val();
                        UnifiedChatState.saveToStorage();
                    }
                });

                // 监听输入框内容变化，控制发送按钮状态
                $('#messageInput').on('input', function() {
                    const isEmpty = $(this).val().trim() === '';
                    $('#sendMessage').prop('disabled', isEmpty);
                });
                
                // 初始状态下检查输入框
                $('#messageInput').trigger('input');
                
                // 处理发送按钮点击事件
                $('#sendMessage').on('click', function() {
                    if (!$(this).prop('disabled')) {
                        MainChatApp.handleSendMessage();
                    }
                });
                
                // 处理Enter键发送
                $('#messageInput').on('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (!$('#sendMessage').prop('disabled')) {
                            MainChatApp.handleSendMessage();
                        }
                    }
                });

                // 绑定停止按钮事件
                $('#stopResponse').on('click', function() {
                    MainChatApp.handleStopResponse();
                });

                // 全局委托处理聊天项点击
                $('#chatHistory').on('click', '.chat-item', function(e) {
                    if (!$(e.target).closest('.chat-item-edit, .chat-item-delete').length) {
                        const chatId = $(this).data('chat-id');
                        console.log('Chat item clicked:', chatId);
                        MainChatApp.switchToChat(chatId);
                    }
                });

                // 全局委托处理删除按钮点击，避免多次绑定
                $('#chatHistory').off('click.deleteChat').on('click.deleteChat', '.chat-item-delete', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const $chatItem = $(e.target).closest('.chat-item');
                    const chatId = $chatItem.data('chat-id');
                    const chatTitle = $chatItem.find('.chat-title').text();
                    this.handleDeleteChat(chatId, chatTitle);
                });
            });
        },

        // --- INITIALIZATION ---
        init: async function() {
            
            const customStyles = `
                /* 基础聊天布局 */
                #chatMessages {
                    padding: 5px 0;
                }
                
                .message {
                    display: flex;
                    margin: 4px 8px;
                    align-items: flex-start;
                }
                
                .message.user {
                    flex-direction: row-reverse;
                }
                
                /* 头像样式 */
                .message .avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    flex-shrink: 0;
                }
                
                .message.user .avatar {
                    margin-left: 6px;
                }
                
                .message.ai .avatar {
                    margin-right: 6px;
                }
                
                /* 消息包装器 - 新增 */
                .message-wrapper {
                    display: flex;
                    flex-direction: column;
                    max-width: 80%;
                }
                
                .message.user .message-wrapper {
                    align-items: flex-end;
                }
                
                .message.ai .message-wrapper {
                    align-items: flex-start;
                }
                
                /* 消息气泡 */
                .message-bubble {
                    background-color: #eff6ff;
                    border-radius: 10px;
                    padding: 5px 8px;
                    position: relative;
                    display: inline-block;
                    max-width: max-content;
                }
                
                /* 短文本特殊处理 */
                .message.short-text .message-bubble {
                    padding: 4px 8px;
                }
                
                /* 消息文本 */
                .message-text {
                    font-size: 14px;
                    line-height: 1.3;
                    margin: 0;
                    word-wrap: break-word;
                    white-space: normal;
                }
                
                /* 消息时间 - 修改为非绝对定位 */
                .message-time {
                    font-size: 10px;
                    color: #aaa;
                    margin: 2px 4px 0;
                    line-height: 1;
                }
                
                /* 复制按钮 */
                .copy-message {
                    position: absolute;
                    bottom: 2px;
                    right: 8px;
                    background: none;
                    border: none;
                    font-size: 14px;
                    color: #888;
                    cursor: pointer;
                    padding: 0;
                    line-height: 1;
                }
                
                .copy-message:hover {
                    color: #000;
                }
                
                .copy-message i {
                    font-size: 14px;
                    vertical-align: middle;
                }
                
                /* 代码和JSON内容 */
                .message-text pre {
                    margin: 0;
                    font-family: monospace;
                    font-size: 13px;
                    white-space: pre-wrap;
                }
                
                .message-text pre.json-content {
                    font-size: 12px;
                }
                
                .message-text code {
                    padding: 0;
                }
                
                /* 确保换行不会产生过多空间 */
                .message-text br {
                    line-height: 1;
                    content: "";
                    display: block;
                    margin: 1px 0;
                }
            `;

            $('<style>').text(customStyles).appendTo('head');
            
            if (document.readyState === 'loading') {
                await new Promise(resolve => $(document).ready(resolve));
            }

            try {
                UnifiedChatState.init(); // Initialize our state manager

                // Load user info for navbar
                const token = this.getAuthToken();
                if (token) {
                    try {
                        const userResponse = await fetch('/api/user/current', { headers: { 'Authorization': `Bearer ${token}` }});
                        if (userResponse.ok) {
                            const result = await userResponse.json();
                            if (result.code === 200 && result.data) {
                                const user = result.data;
                                $('.nav-user .username').text(user.username || 'User');
                                if (user.avatar) $('.nav-user .avatar').attr('src', user.avatar);
                                this.state.currentUser = { username: user.username, avatar: user.avatar };
                            }
                        }
                    } catch (e) { console.warn('[MainChatApp] Error fetching current user:', e); }
                }
                this.fixDefaultAvatar(); // Fix avatars after attempting to load user's
                
                this.setupEventListeners();
                await this.loadChatHistory(); // This will also call switchToChat for the initial chat

                // Handle browser back/forward navigation
                window.addEventListener('popstate', (event) => {
                    const stateChatId = event.state && event.state.chatId;
                    if (stateChatId) {
                        this.switchToChat(stateChatId);
                    } else {
                        // Fallback if popstate has no chatId, try URL
                        const urlParams = new URLSearchParams(window.location.search);
                        const chatIdFromUrl = urlParams.get('id');
                        if (chatIdFromUrl) {
                             this.switchToChat(chatIdFromUrl);
                        } else {
                            // If no chat in URL and no history, loadChatHistory should handle empty state
                            // If there is history, loadChatHistory would have switched to the most recent.
                            // This path might be hit if user clears URL and goes back to a state before any chat was loaded.
                            console.log('[MainChatApp] Popstate with no specific chat ID, relying on default load behavior.');
                            if ($('#chatHistory .chat-item').length === 0) {
                                UnifiedChatState.resetCurrentChatDetails();
                                $('#currentChatTitle').text('New Chat');
                                $('#chatMessages').html('<div class="text-center text-muted p-4">No conversations. Create one!</div>');
                                this.enableChatInputs(false);
                            }
                        }
                    }
                });
                
                console.log('[MainChatApp] Chat system initialization complete.');
            } catch (error) {
                console.error('[MainChatApp] Critical error during initialization:', error);
                this.showToast(`Chat initialization failed: ${error.message}`, 'error', 10000);
                $('#chatMessages').html(`<div class="alert alert-danger m-3">Chat system failed to initialize. Please try refreshing. Details: ${this.escapeHtml(error.message)}</div>`);
            }
        },

        // 添加停止生成方法
        handleStopResponse: function() {
            if (this.state.currentFetchController) {
                this.state.currentFetchController.abort();
                this.state.currentFetchController = null;
                
                // 移除打字指示器
                $('#chatMessages .message.ai.typing').remove();
                
                // 恢复界面状态
                this.state.isSendingMessage = false;
                $('#stopResponse').hide();
                $('#sendMessage').show();
                $('#messageInput').trigger('input');
                
                this.showToast('已停止生成回复', 'info');
            }
        }
    };

    // Expose MainChatApp globally for debugging or specific calls if needed
    window.UnifiedChatApp = MainChatApp;

    // Start the application
    MainChatApp.init();

    // 自动调整文本区域高度
    function autoResizeTextarea() {
        // 为messageInput添加自动调整高度功能
        const textarea = document.getElementById('messageInput');
        if (!textarea) return;
        
        textarea.addEventListener('input', function() {
            // 重置高度以获取正确的scrollHeight
            this.style.height = 'auto';
            // 设置新高度
            const newHeight = Math.min(this.scrollHeight, 200);
            this.style.height = newHeight + 'px';
            
            // 更新字数计数
            const charCounter = document.querySelector('.char-counter');
            if (charCounter) {
                const maxLength = 4000; // 设置最大字符数
                const currentLength = this.value.length;
                charCounter.textContent = `${currentLength}/${maxLength}`;
                
                // 添加字数警告
                if (currentLength > maxLength * 0.8) {
                    charCounter.style.color = '#f59e0b'; // 黄色警告
                } else if (currentLength > maxLength) {
                    charCounter.style.color = '#ef4444'; // 红色警告
                } else {
                    charCounter.style.color = '#9ca3af'; // 默认颜色
                }
            }
        });
        
        // 初始调整
        textarea.dispatchEvent(new Event('input'));
    }

    // 在页面加载完成后调用
    $(document).ready(function() {
        // 现有的初始化代码...
        
        // 添加自动调整文本区域高度
        autoResizeTextarea();
    });

})(window, window.jQuery); // Pass window and jQuery 