// Avatar path fix script
(function() {
    // Function to fix avatar paths
    function fixAvatarPaths() {
        // Find all image elements (be more comprehensive)
        const avatarImages = document.querySelectorAll('img');
        
        avatarImages.forEach(img => {
            // Skip images that are already fixed or default images
            if (img.dataset.fixed || img.src.includes('default-avatar.png') || !img.src) return;
            
            // Store the original src
            const originalSrc = img.src;
            let needsFix = false;
            let fixedSrc = originalSrc;
            
            // Check for various path issues
            if (originalSrc.includes('/uploads/avatars/avatars/')) {
                // Fix duplicate avatars prefix
                fixedSrc = originalSrc.replace('/uploads/avatars/avatars/', '/uploads/avatars/');
                needsFix = true;
            } else if (originalSrc.match(/\/uploads\/avatars\/\d{14}_[a-f0-9]+\.jpg$/)) {
                // This is already correct, no need to fix
                // Just add a dataset to avoid redundant checks
                img.dataset.fixed = 'true';
                return;
            } else if (originalSrc.match(/\/uploads\/\d{14}_[a-f0-9]+\.jpg$/)) {
                // Missing avatars prefix completely
                fixedSrc = originalSrc.replace('/uploads/', '/uploads/avatars/');
                needsFix = true;
            }
            
            // Apply the fix if needed
            if (needsFix) {
                // Set the fixed src
                img.src = fixedSrc;
                img.dataset.fixed = 'true'; // Mark as fixed to avoid double processing
                console.log('Fixed avatar path from', originalSrc, 'to', fixedSrc);
            }
            
            // Handle image error for any avatar
            img.onerror = function() {
                if (!this.dataset.errorHandled && this.src !== '/images/default-avatar.png') {
                    console.error('Failed to load avatar from', this.src, ', using default');
                    this.src = '/images/default-avatar.png';
                    this.dataset.errorHandled = 'true'; // Prevent infinite error loops
                }
            };
        });
    }

    // Run fix immediately for already loaded images
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixAvatarPaths);
    } else {
        fixAvatarPaths();
    }
    
    // Fix on window load to catch images loaded after DOM is ready
    window.addEventListener('load', fixAvatarPaths);
    
    // Also fix paths after any DOM changes that might add new images
    // Use a MutationObserver to watch for changes
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            let hasNewImages = false;
            
            mutations.forEach(function(mutation) {
                // Check for added nodes
                if (mutation.addedNodes.length) {
                    hasNewImages = true;
                }
                
                // Also check for attribute changes on images
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'src' && 
                    mutation.target.tagName === 'IMG') {
                    
                    const img = mutation.target;
                    if (!img.dataset.fixed && img.src.includes('/uploads/')) {
                        hasNewImages = true;
                    }
                }
            });
            
            if (hasNewImages) {
                setTimeout(fixAvatarPaths, 0); // Defer to next tick for better performance
            }
        });
        
        // Start observing immediately if DOM is ready
        if (document.readyState !== 'loading') {
            observer.observe(document.body, { 
                childList: true, 
                subtree: true,
                attributes: true,
                attributeFilter: ['src']
            });
        } else {
            // Otherwise start observing once DOM is fully loaded
            document.addEventListener('DOMContentLoaded', function() {
                observer.observe(document.body, { 
                    childList: true, 
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['src']
                });
            });
        }
    }
    
    // Define a global helper function for other scripts to use
    window.fixAvatarUrl = function(url) {
        if (!url) return '/images/default-avatar.png';
        
        // Check for duplicate path and fix it
        if (url.includes('avatars/avatars/')) {
            return url.replace('avatars/avatars/', 'avatars/');
        }
        
        // If it doesn't have avatars/ prefix at all, add it
        if (url.match(/^[0-9]{14}_[a-f0-9]+\.[a-z]+$/)) {
            return 'avatars/' + url;
        }
        
        return url;
    };
})(); 