// (Self-executing function wrapper)
(function() {
    console.log('[chat-fix.js] Script loaded and executing.');

    const state = {
        chatSessions: {}, // Store chat session details { id: {id, title, updatedAt, knowId, bookUrl, ...}, ... }
        currentChatId: null,
        currentUser: null, // { username, avatar }
    };
    window.currentChatId = null; // For any legacy access, though prefer state.currentChatId
    let currentFetchController = null;

    // --- UTILITY FUNCTIONS ---
    function getAuthToken() {
        const token = localStorage.getItem('token') || '';
        // console.log('[chat-fix] getAuthToken called, token:', token ? 'found' : 'not found');
        return token; // Assuming token is stored directly, not "Bearer token"
    }

    function escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    function formatDateTime(dateString) {
        if (!dateString) return new Date().toLocaleString(); // Fallback to now
        try {
            return new Date(dateString).toLocaleString('zh-CN', { hour12: false });
        } catch (e) {
            return dateString;
        }
    }

    function showToast(message, type = 'info') {
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
        } else {
            console.log(`[Toast-${type}] ${message}`);
            // alert(`[${type}] ${message}`); // Avoid alert if possible
        }
    }

    function enableChatInputs(enable) {
        const messageInput = document.getElementById('messageInput');
        const sendMessageBtn = document.getElementById('sendMessage');
        const knowIdInput = document.getElementById('knowId');
        const bookUrlInput = document.getElementById('bookUrl');

        if (messageInput) messageInput.disabled = !enable;
        if (sendMessageBtn) sendMessageBtn.disabled = !enable;
        if (knowIdInput) knowIdInput.disabled = !enable;
        if (bookUrlInput) bookUrlInput.disabled = !enable;
        // console.log('[chat-fix] Chat inputs ' + (enable ? 'enabled' : 'disabled'));
    }
    
    const thinkingAnimation = {
        intervalId: null,
        elements: [],
        stop: function() {
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }
            this.elements.forEach(el => {
                if (el && el.parentNode) el.parentNode.removeChild(el);
            });
            this.elements = [];
        }
        // Start animation logic would be more complex, usually handled by CSS or a dedicated function
    };

    function stopThinkingAnimation() { // Simplified
        thinkingAnimation.stop();
        const typingIndicator = document.querySelector('.message.ai.typing');
        if (typingIndicator) typingIndicator.remove();
    }


    // --- AVATAR FUNCTIONS ---
    function fixDefaultAvatar() {
        // console.log('[chat-fix] Fixing default avatars...');
        const transparentPixel = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
        // Ensure these paths are correct and files exist in static/images/
        const defaultUserAvatarPath = '/static/images/default-avatar.png'; // More generic default
        const defaultAiAvatarPath = '/static/images/ai-avatar.png'; // Specific AI avatar

        // Fix navbar avatar first
        const navUserAvatar = document.querySelector('.nav-user .avatar');
        if (navUserAvatar) {
            navUserAvatar.onerror = function() {
                console.warn('[chat-fix] Navbar avatar failed to load, using default.');
                this.src = defaultUserAvatarPath;
                this.onerror = null; // Prevent infinite loop if default also fails
            };
            if (navUserAvatar.complete && typeof navUserAvatar.naturalWidth !== "undefined" && navUserAvatar.naturalWidth === 0) {
                console.warn('[chat-fix] Navbar avatar already broken, setting default.');
                navUserAvatar.src = defaultUserAvatarPath;
            }
        }
        // Set global current user avatar (used for messages)
        // Try to get it from navbar after potential fix, or use default.
        state.currentUser = state.currentUser || {}; // Ensure currentUser object exists
        state.currentUser.avatar = (navUserAvatar && navUserAvatar.src !== transparentPixel && !navUserAvatar.src.endsWith('default-avatar.png')) ? navUserAvatar.src : defaultUserAvatarPath;
        // console.log('[chat-fix] User avatar for messages set to:', state.currentUser.avatar);

        // This function will be called by addMessageToUI for message avatars
    }
    
    function getMessageAvatar(isUser) {
        const defaultUserAvatarPath = '/static/images/default-avatar.png';
        const defaultAiAvatarPath = '/static/images/ai-avatar.png';
        if (isUser) {
            return (state.currentUser && state.currentUser.avatar) ? state.currentUser.avatar : defaultUserAvatarPath;
        }
        return defaultAiAvatarPath;
    }


    // --- CHAT HISTORY AND SESSION MANAGEMENT ---
    async function loadChatHistory() {
        console.log('[chat-fix] Loading chat history...');
        const chatHistoryEl = document.getElementById('chatHistory');
        if (!chatHistoryEl) {
            console.error('[chat-fix] chatHistory element not found!');
            return;
        }
        chatHistoryEl.innerHTML = '<div class="p-3 text-center text-muted">Loading history...</div>';

        try {
            const response = await fetch('/api/chat/history', {
                headers: { 'Authorization': getAuthToken() }
            });
            if (!response.ok) throw new Error(`Failed to load chat history: ${response.status} ${response.statusText}`);
            const result = await response.json();

            if (result.code === 200 && Array.isArray(result.data)) {
                chatHistoryEl.innerHTML = ''; // Clear loading
                state.chatSessions = {}; // Reset local cache
                if (result.data.length === 0) {
                    chatHistoryEl.innerHTML = '<div class="p-3 text-center text-muted">No conversations yet.</div>';
                    document.getElementById('currentChatTitle').textContent = 'New Chat';
                    document.getElementById('chatMessages').innerHTML = '<div class="text-center text-muted p-4">Start by creating a new conversation.</div>';
                    enableChatInputs(false); // Disable if no chats
                    history.replaceState(null, '', `/main/chat`); // Clear chat ID from URL
                    return;
                }
                result.data.forEach(chat => {
                    state.chatSessions[String(chat.id)] = chat; // Cache it
                    addChatToHistoryDOM(chat);
                });

                // Determine which chat to switch to
                const urlParams = new URLSearchParams(window.location.search);
                const chatIdFromUrl = urlParams.get('id');

                if (chatIdFromUrl && state.chatSessions[chatIdFromUrl]) {
                    switchToChat(chatIdFromUrl);
                } else if (result.data.length > 0) {
                    // Default to the most recently updated chat if no valid ID in URL
                    const mostRecentChat = result.data.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))[0];
                    switchToChat(mostRecentChat.id);
                }
            } else {
                throw new Error(result.message || 'Failed to parse chat history');
            }
        } catch (error) {
            console.error('[chat-fix] Error loading chat history:', error);
            chatHistoryEl.innerHTML = `<div class="alert alert-warning m-2">Could not load history: ${escapeHtml(error.message)}</div>`;
            enableChatInputs(false);
        }
    }

    function addChatToHistoryDOM(chat, prepend = false) {
        const chatHistoryEl = document.getElementById('chatHistory');
        if (!chatHistoryEl) return;

        const chatItemEl = document.createElement('div');
        chatItemEl.className = 'chat-item';
        chatItemEl.dataset.id = String(chat.id);
        chatItemEl.setAttribute('role', 'button');
        chatItemEl.tabIndex = 0;

        const formattedTime = formatDateTime(chat.updatedAt);
        chatItemEl.innerHTML = `
            <div class="chat-item-icon"><i class="bi bi-chat-left-text"></i></div>
            <div class="chat-item-content">
                <div class="chat-item-title">${escapeHtml(chat.title || 'Untitled Chat')}</div>
                <div class="chat-item-time">
                    <i class="bi bi-clock"></i> ${formattedTime}
                </div>
            </div>
        `;
        // chatItemEl.addEventListener('click', () => switchToChat(String(chat.id))); // Event listener added by delegation

        if (prepend && chatHistoryEl.firstChild) {
            chatHistoryEl.insertBefore(chatItemEl, chatHistoryEl.firstChild);
        } else {
            chatHistoryEl.appendChild(chatItemEl);
        }
    }

    async function switchToChat(chatId) {
        console.log(`[chat-fix] Switching to chat: ${chatId}`);
        if (!chatId) {
            console.error('[chat-fix] switchToChat: Chat ID is null.');
            return;
        }

        // 更新状态
        state.currentChatId = String(chatId);
        window.currentChatId = String(chatId); // 为了兼容性

        // 更新 UI 激活状态
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.id === String(chatId)) {
                item.classList.add('active');
            }
        });

        // 更新 URL
        const newUrl = `/main/chat?id=${chatId}`;
        if (window.location.pathname + window.location.search !== newUrl) {
            history.pushState({ chatId }, '', newUrl);
        }

        try {
            // 获取聊天会话详情
            const response = await fetch(`/api/chat/${chatId}`, {
                headers: { 'Authorization': getAuthToken() }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch chat details: ${response.status}`);
            }

            const result = await response.json();
            if (result.code === 200 && result.data) {
                const chatSession = result.data;
                
                // 更新缓存
                state.chatSessions[String(chatId)] = chatSession;
                
                // 更新 UI
                document.getElementById('currentChatTitle').textContent = chatSession.title || 'Untitled Chat';
                
                // 更新输入框
                const knowIdInput = document.getElementById('knowId');
                const bookUrlInput = document.getElementById('bookUrl');
                if (knowIdInput) knowIdInput.value = chatSession.knowId || '';
                if (bookUrlInput) bookUrlInput.value = chatSession.bookUrl || '';

                // 加载消息
                await loadChatMessages(chatId);
                
                // 启用输入
                enableChatInputs(true);
                
                // 聚焦到输入框
                const messageInput = document.getElementById('messageInput');
                if (messageInput) {
                    messageInput.focus();
                }
            } else {
                throw new Error(result.message || 'Invalid response format');
            }
        } catch (error) {
            console.error('[chat-fix] Error in switchToChat:', error);
            showToast(`Failed to switch chat: ${error.message}`, 'error');
            
            // 显示错误状态
            document.getElementById('currentChatTitle').textContent = 'Error Loading Chat';
            document.getElementById('chatMessages').innerHTML = 
                `<div class="alert alert-danger m-3">Failed to load chat: ${escapeHtml(error.message)}</div>`;
            enableChatInputs(false);
        }
    }


    // --- MESSAGE HANDLING ---
    async function loadChatMessages(chatId) {
        console.log(`[chat-fix] Loading messages for chat ID: ${chatId}`);
        const chatMessagesEl = document.getElementById('chatMessages');
        if (!chatMessagesEl) {
            console.error('[chat-fix] chatMessages element not found!');
            return;
        }
        chatMessagesEl.innerHTML = '<div class="loading-indicator p-3 text-center">Loading messages...</div>';

        // 使用正确的端点 (根据 ChatController.java 中的 @GetMapping("/messages/{chatId}"))
        const messagesApiEndpoint = `/api/chat/messages/${chatId}`;

        try {
            console.log(`[chat-fix] Attempting to fetch messages from: ${messagesApiEndpoint}`);
            const response = await fetch(messagesApiEndpoint, {
                headers: { 'Authorization': getAuthToken() }
            });

            if (!response.ok) {
                const errorText = await response.text(); // Get error message from server if available
                console.error(`[chat-fix] API Error (${response.status}) from ${messagesApiEndpoint}:`, errorText);
                throw new Error(`Failed to load messages: ${response.status} ${response.statusText}. Server: ${errorText}`);
            }

            const result = await response.json();
            console.log('[chat-fix] Messages API response:', result);

            let messagesArray = [];
            if (result.code === 200) {
                if (Array.isArray(result.data)) {
                    messagesArray = result.data;
                } else if (result.data && Array.isArray(result.data.messages)) {
                    messagesArray = result.data.messages;
                } else if (result.data && Array.isArray(result.data.content)) {
                    messagesArray = result.data.content;
                } else {
                    console.warn('[chat-fix] Messages data from API is not in a recognized array format. Response data:', result.data);
                }
            } else {
                 console.warn(`[chat-fix] API call to ${messagesApiEndpoint} was successful (HTTP 200) but application code was not 200:`, result);
            }
            displayMessagesDOM(messagesArray, chatMessagesEl, chatId);

        } catch (error) {
            console.error('[chat-fix] Error in loadChatMessages:', error);
            chatMessagesEl.innerHTML = `<div class="alert alert-danger m-3">Error loading messages: ${escapeHtml(error.message)} <button onclick="window.chatFix.loadMessages('${chatId}')" class="btn btn-sm btn-outline-danger">Retry</button></div>`;
        }
    }

    function displayMessagesDOM(messages, containerElement, chatIdForDisplay) {
        containerElement.innerHTML = ''; // Clear loading/previous messages
        if (!messages || messages.length === 0) {
            containerElement.innerHTML = `
                <div class="empty-messages text-center text-muted p-4">
                    No messages in this conversation yet.
                    <p class="small text-muted mt-2">Chat ID: ${chatIdForDisplay}</p>
                </div>`;
            return;
        }
        messages.forEach(msg => addMessageToUI(msg, containerElement));
        containerElement.scrollTop = containerElement.scrollHeight; // Scroll to bottom
    }

    function addMessageToUI(message, container = null) {
        const chatMessagesEl = container || document.getElementById('chatMessages');
        if (!chatMessagesEl) return;

        // Normalize message structure
        let isUser, content, time, avatarSrc;
        if (message.role === 'user' || message.sender === 'user' || message.isUser === true) {
            isUser = true;
        } else if (message.role === 'assistant' || message.sender === 'assistant' || message.isUser === false) {
            isUser = false;
        } else {
            console.warn('[chat-fix] Unknown message sender type:', message);
            isUser = false; // Default to AI for unknown
        }
        
        content = message.content || message.text || message.message || '';
        time = formatDateTime(message.createdAt || message.timestamp || message.sendTime || new Date());
        avatarSrc = getMessageAvatar(isUser);

        const messageEl = document.createElement('div');
        messageEl.className = `message ${isUser ? 'user' : 'ai'}`;
        
        const formattedContent = isUser ? escapeHtml(content) : formatAIMessage(content);

        messageEl.innerHTML = `
            <img src="${avatarSrc}" alt="${isUser ? 'User' : 'AI'} Avatar" class="message-avatar" onerror="this.src='${getMessageAvatar(isUser)}'; this.onerror=null;">
            <div class="message-bubble">
                <div class="message-content">${formattedContent}</div>
                <div class="message-time">${time}</div>
            </div>
        `;
        
        // Remove "Loading messages..." if it's the first message
        const loadingIndicator = chatMessagesEl.querySelector('.loading-indicator, .empty-messages');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }

        chatMessagesEl.appendChild(messageEl);
        chatMessagesEl.scrollTop = chatMessagesEl.scrollHeight;

        if (content.includes('```') && window.Prism) {
            setTimeout(() => { // Ensure DOM is updated
                messageEl.querySelectorAll('pre code').forEach(Prism.highlightElement);
            }, 0);
        }
    }
    
    function formatAIMessage(rawContent) {
        let html = escapeHtml(rawContent); // Basic sanitization

        // Markdown-like features: Bold, Italic, Code blocks
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>'); // Bold
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');       // Italic
        
        // Code blocks (simple version, assumes ```lang\ncode\n```)
        html = html.replace(/```(\w*)\n([\s\S]*?)\n```/g, (match, lang, code) => {
            const languageClass = lang ? `language-${escapeHtml(lang)}` : 'language-none';
            // Prism expects unescaped HTML entities within the code block for highlighting
            const unescapedCode = code.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
            return `<pre><code class="${languageClass}">${escapeHtml(unescapedCode)}</code></pre>`;
        });
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>'); // Inline code

        // Basic lists (very simplified)
        html = html.replace(/^\s*-\s+(.*)/gm, '<li>$1</li>');
        html = html.replace(/(\<li\>.*\<\/li\>)/s, '<ul>$1</ul>'); // Wrap LIs in UL

        // Newlines to <br> (but not inside pre/code)
        const parts = html.split(/(<pre>.*?<\/pre>)/s);
        for (let i = 0; i < parts.length; i++) {
            if (i % 2 === 0) { // Not a <pre> block
                parts[i] = parts[i].replace(/\n/g, '<br>');
            }
        }
        html = parts.join('');

        return html;
    }

    async function handleSendMessage() {
        const messageInput = document.getElementById('messageInput');
        const stopButton = document.getElementById('stopResponse');
        const sendButton = document.getElementById('sendMessage');

        if (!messageInput || !state.currentChatId) {
            console.warn('[chat-fix] SendMessage: Message input or currentChatId missing.');
            return;
        }
        const messageText = messageInput.value.trim();
        if (!messageText) return;

        enableChatInputs(false);
        if (stopButton) stopButton.style.display = 'inline-block';
        if (sendButton) sendButton.style.display = 'none';

        addMessageToUI({ isUser: true, content: messageText, createdAt: new Date().toISOString() });
        messageInput.value = '';
        messageInput.style.height = 'auto'; // Reset height

        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'message ai typing';
        typingIndicator.innerHTML = `
            <img src="${getMessageAvatar(false)}" alt="AI Avatar" class="message-avatar">
            <div class="message-bubble">
                <div class="ai-thinking">
                    <div class="thinking-dots"><span>.</span><span>.</span><span>.</span></div>
                </div>
            </div>`;
        document.getElementById('chatMessages').appendChild(typingIndicator);
        document.getElementById('chatMessages').scrollTop = document.getElementById('chatMessages').scrollHeight;
        
        currentFetchController = new AbortController();
        const { signal } = currentFetchController;

        const knowId = document.getElementById('knowId') ? document.getElementById('knowId').value.trim() : '';
        const bookUrl = document.getElementById('bookUrl') ? document.getElementById('bookUrl').value.trim() : '';

        // *** YOU MUST VERIFY THIS ENDPOINT AND REQUEST BODY WITH YOUR ChatController.java ***
        const sendMessageApiEndpoint = `/api/chat/${state.currentChatId}/message`;
        const requestBody = { content: messageText, knowId, bookUrl };

        try {
            const response = await fetch(sendMessageApiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': getAuthToken()
                },
                body: JSON.stringify(requestBody),
                signal
            });

            typingIndicator.remove(); // Remove typing indicator once response starts or finishes

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API Error (${response.status}): ${errorText || response.statusText}`);
            }
            const result = await response.json();

            if (result.code === 200 && result.data) {
                addMessageToUI({
                    isUser: false,
                    content: result.data.content || (typeof result.data === 'string' ? result.data : 'Received response.'),
                    createdAt: result.data.createdAt || new Date().toISOString()
                });
                // Update chat session's updatedAt timestamp and re-sort history
                if(state.chatSessions[state.currentChatId]) {
                    state.chatSessions[state.currentChatId].updatedAt = new Date().toISOString();
                    // Visually update history (optional, loadChatHistory does full re-render)
                    const historyEl = document.getElementById('chatHistory');
                    const currentItem = historyEl.querySelector(`.chat-item[data-id="${state.currentChatId}"]`);
                    if(currentItem) {
                        const timeEl = currentItem.querySelector('.chat-item-time');
                        if(timeEl) timeEl.innerHTML = `<i class="bi bi-clock"></i> ${formatDateTime(state.chatSessions[state.currentChatId].updatedAt)}`;
                        // Move to top
                        if (historyEl.firstChild !== currentItem) {
                            historyEl.insertBefore(currentItem, historyEl.firstChild);
                        }
                    }
                }

            } else {
                throw new Error(result.message || 'Failed to process AI response.');
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('[chat-fix] Error sending message:', error);
                showToast(`Error: ${error.message}`, 'error');
                addMessageToUI({ isUser: false, content: `Error: ${error.message}`, createdAt: new Date().toISOString() });
            } else {
                console.log('[chat-fix] Message sending aborted.');
                addMessageToUI({ isUser: false, content: `Response generation stopped.`, createdAt: new Date().toISOString() });
            }
        } finally {
            if (!signal.aborted) { // Only re-enable if not aborted by user
                enableChatInputs(true);
            }
            if (stopButton) stopButton.style.display = 'none';
            if (sendButton) sendButton.style.display = 'inline-block';
            currentFetchController = null;
            if (messageInput) messageInput.focus();
        }
    }

    function handleStopResponse() {
        if (currentFetchController) {
            console.log('[chat-fix] Aborting current fetch operation.');
            currentFetchController.abort();
            // UI cleanup is handled in handleSendMessage's finally block when abort occurs
        } else {
            console.warn('[chat-fix] No active fetch operation to stop.');
            // Ensure UI is reset if somehow stop is called without active fetch
            stopThinkingAnimation();
            enableChatInputs(true);
            const stopButton = document.getElementById('stopResponse');
            const sendButton = document.getElementById('sendMessage');
            if (stopButton) stopButton.style.display = 'none';
            if (sendButton) sendButton.style.display = 'inline-block';
        }
    }

    // --- CHAT MANAGEMENT ACTIONS (New, Edit, Delete) ---
    async function handleNewChat() {
        console.log('[chat-fix] handleNewChat called.');
        try {
            showToast('正在创建新对话...', 'info');
            const knowIdVal = document.getElementById('knowId')?.value || 'default'; // 提供默认值
            const bookUrlVal = document.getElementById('bookUrl')?.value || 'https://default'; // 提供默认值

            const response = await fetch('/api/chat/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': getAuthToken()
                },
                body: JSON.stringify({
                    title: "新对话", // 后端似乎也会处理默认标题
                    knowId: knowIdVal,
                    bookUrl: bookUrlVal
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: `API请求失败，状态码: ${response.status}` }));
                throw new Error(errorData.message || `API请求失败，状态码: ${response.status}`);
            }

            const result = await response.json();
            console.log('[chat-fix] Create chat response:', result);

            if (result.code === 200 && result.data && result.data.id) {
                const newChat = {
                    id: String(result.data.id),
                    title: result.data.title || "新对话",
                    updatedAt: result.data.updatedAt || new Date().toISOString(),
                    knowId: result.data.knowId || knowIdVal,
                    bookUrl: result.data.bookUrl || bookUrlVal
                };
                
                state.chatSessions[newChat.id] = newChat;
                addChatToHistoryDOM(newChat, true); // Prepend
                await switchToChat(newChat.id); // 切换到新对话
                showToast('新对话已创建！', 'success');
            } else {
                throw new Error(result.message || '创建对话失败，响应格式无效。');
            }
        } catch (error) {
            console.error('[chat-fix] Error creating new chat:', error);
            showToast(`创建对话失败: ${error.message}`, 'error');
        }
    }

    async function handleEditTitle() {
        if (!state.currentChatId) {
            showToast('No chat selected to edit.', 'warning'); return;
        }
        const editModalEl = document.getElementById('editTitleModal');
        const titleInputEl = document.getElementById('newTitleInput');
        const confirmBtn = document.getElementById('confirmEditTitleBtn');

        if (!editModalEl || !titleInputEl || !confirmBtn) {
            console.error('[chat-fix] Edit title modal elements missing from DOM.'); return;
        }
        
        const currentSession = state.chatSessions[state.currentChatId];
        titleInputEl.value = currentSession ? currentSession.title : '';
        
        const modalInstance = bootstrap.Modal.getOrCreateInstance(editModalEl);
        
        // Use a fresh event listener for the confirm button to avoid multiple bindings
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        newConfirmBtn.addEventListener('click', async function onConfirm() {
            const newTitle = titleInputEl.value.trim();
            if (!newTitle) {
                showToast('Title cannot be empty.', 'warning'); return;
            }

            // *** YOU MUST VERIFY THIS ENDPOINT, METHOD, AND BODY WITH YOUR ChatController.java ***
            const editTitleApiEndpoint = `/api/chat/${state.currentChatId}/title`;
            const requestBody = { title: newTitle };

            try {
                const response = await fetch(editTitleApiEndpoint, {
                    method: 'PUT', // Or POST/PATCH
                    headers: { 'Content-Type': 'application/json', 'Authorization': getAuthToken() },
                    body: JSON.stringify(requestBody)
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API Error (${response.status}): ${errorText || response.statusText}`);
                }
                const result = await response.json();
                if (result.code === 200) { // Or other success codes like 204
                    showToast('Title updated!', 'success');
                    if (state.chatSessions[state.currentChatId]) {
                        state.chatSessions[state.currentChatId].title = newTitle;
                        state.chatSessions[state.currentChatId].updatedAt = new Date().toISOString();
                    }
                    document.getElementById('currentChatTitle').textContent = newTitle;
                    // Update in history list
                    const historyItem = document.querySelector(`.chat-item[data-id="${state.currentChatId}"] .chat-item-title`);
                    if (historyItem) historyItem.textContent = newTitle;
                    // Re-sort history visually
                    const chatHistoryEl = document.getElementById('chatHistory');
                    const items = Array.from(chatHistoryEl.querySelectorAll('.chat-item'));
                    items.sort((a,b) => {
                        const sessionA = state.chatSessions[a.dataset.id];
                        const sessionB = state.chatSessions[b.dataset.id];
                        return (new Date(sessionB.updatedAt)) - (new Date(sessionA.updatedAt));
                    });
                    items.forEach(item => chatHistoryEl.appendChild(item)); // Re-append in sorted order

                    modalInstance.hide();
                } else {
                    throw new Error(result.message || 'Failed to update title via API.');
                }
            } catch (error) {
                console.error('[chat-fix] Error updating title:', error);
                showToast(`Update title error: ${error.message}`, 'error');
            }
        }, { once: true }); // Ensure listener is only for this confirmation
        
        modalInstance.show();
    }

    async function handleDeleteChat() {
        if (!state.currentChatId) {
            showToast('No chat selected to delete.', 'warning'); return;
        }
        const deleteModalEl = document.getElementById('deleteConfirmModal');
        const confirmBtn = document.getElementById('confirmDeleteBtn');

        if (!deleteModalEl || !confirmBtn) {
            console.error('[chat-fix] Delete confirm modal elements missing from DOM.'); return;
        }
        
        const modalInstance = bootstrap.Modal.getOrCreateInstance(deleteModalEl);

        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        newConfirmBtn.addEventListener('click', async function onConfirmDelete() {
            // *** YOU MUST VERIFY THIS ENDPOINT AND METHOD WITH YOUR ChatController.java ***
            const deleteChatApiEndpoint = `/api/chat/${state.currentChatId}`;

            try {
                const response = await fetch(deleteChatApiEndpoint, {
                    method: 'DELETE',
                    headers: { 'Authorization': getAuthToken() }
                });
                if (!response.ok && response.status !== 204) { // 204 No Content is also success
                    const errorText = await response.text();
                    throw new Error(`API Error (${response.status}): ${errorText || response.statusText}`);
                }
                // For 204, response.json() will fail. Check status first.
                let resultMessage = 'Chat deleted successfully.';
                if (response.status !== 204) {
                    const result = await response.json();
                    if (result.code !== 200) {
                         throw new Error(result.message || 'Failed to delete chat via API.');
                    }
                    resultMessage = result.message || resultMessage;
                }

                showToast(resultMessage, 'success');
                const deletedChatId = state.currentChatId;
                delete state.chatSessions[deletedChatId];
                
                const historyItem = document.querySelector(`.chat-item[data-id="${deletedChatId}"]`);
                if (historyItem) historyItem.remove();

                state.currentChatId = null;
                window.currentChatId = null;
                modalInstance.hide();

                // Switch to the newest chat or show empty state
                const remainingChats = Object.values(state.chatSessions);
                if (remainingChats.length > 0) {
                    const mostRecentChat = remainingChats.sort((a,b) => new Date(b.updatedAt) - new Date(a.updatedAt))[0];
                    switchToChat(mostRecentChat.id);
                } else {
                    document.getElementById('currentChatTitle').textContent = 'New Chat';
                    document.getElementById('chatMessages').innerHTML = '<div class="text-center text-muted p-4">No conversations. Create one!</div>';
                    enableChatInputs(false);
                    history.replaceState(null, '', `/main/chat`);
                }

            } catch (error) {
                console.error('[chat-fix] Error deleting chat:', error);
                showToast(`Delete chat error: ${error.message}`, 'error');
            }
        }, { once: true });
        
        modalInstance.show();
    }


    // --- EVENT LISTENERS SETUP ---
    function setupEventListeners() {
        
        // 使用 jQuery $(document).ready() 来确保 DOM 结构加载完成
        jQuery(document).ready(function($) { // 使用 jQuery 并传入 $ 作为参数，避免 $ 冲突
            // 辅助函数：检查元素是否存在并记录日志
            function ensureElementAndBind(selector, eventType, handler, isDelegated = false, delegateSelector = null) {
                const $element = isDelegated ? $(selector) : $(selector); // For delegation, selector is the parent
                
                if ($element.length) {
                    console.log(`[chat-fix] Element found: '${selector}'. Binding '${eventType}'. Delegated: ${isDelegated}`);
                    if (isDelegated && delegateSelector) {
                        $element.off(eventType + '.chatfix', delegateSelector).on(eventType + '.chatfix', delegateSelector, handler);
                    } else {
                        $element.off(eventType + '.chatfix').on(eventType + '.chatfix', handler);
                    }
                    console.log(`[chat-fix] Successfully bound '${eventType}' to '${selector}'` + (isDelegated ? ` (delegated from '${delegateSelector}')` : ''));
                } else {
                    console.warn(`[chat-fix] Element NOT FOUND for binding: '${selector}'. Event '${eventType}' not bound.`);
                }
            }

            // 新建对话按钮
            ensureElementAndBind('#newChatBtn', 'click', function() {
                console.log('[chat-fix] #newChatBtn clicked (event handler).');
                handleNewChat();
            });

            // 聊天历史点击事件 (事件委托)
            // 确保 #chatHistory 元素是静态存在的
            ensureElementAndBind('#chatHistory', 'click', function(e) {
                e.preventDefault();
                const chatId = $(this).data('id'); // 'this' refers to '.chat-item'
                console.log('[chat-fix] .chat-item clicked via delegation (event handler). Chat ID:', chatId);
                if (chatId) {
                    switchToChat(String(chatId));
                } else {
                    console.warn('[chat-fix] Clicked chat item has no ID in data attribute.');
                }
            }, true, '.chat-item'); // true for delegated, '.chat-item' is the target selector

            // 发送消息按钮
            ensureElementAndBind('#sendMessage', 'click', function() {
                console.log('[chat-fix] #sendMessage clicked (event handler).');
                handleSendMessage();
            });

            // 编辑标题按钮
            ensureElementAndBind('#editTitleBtn', 'click', function() {
                console.log('[chat-fix] #editTitleBtn clicked (event handler).');
                handleEditTitle();
            });
            
            // 删除对话按钮
            ensureElementAndBind('#deleteChatBtn', 'click', function() {
                console.log('[chat-fix] #deleteChatBtn clicked (event handler).');
                handleDeleteChat();
            });
            
            // 消息输入框 (Enter 发送)
            ensureElementAndBind('#messageInput', 'keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    console.log('[chat-fix] Enter pressed in #messageInput (event handler).');
                    handleSendMessage();
                }
            });

            console.log('[chat-fix] All event binding attempts in jQuery document.ready finished.');
        });
        
        console.log('[chat-fix] setupEventListeners function execution finished.');
    }


    // --- INITIALIZATION ---
    async function init() {
        console.log('[chat-fix] Initializing chat system...');
        
        if (document.readyState === 'loading') {
            console.log('[chat-fix] DOM not ready, waiting for DOMContentLoaded.');
            await new Promise(resolve => document.addEventListener('DOMContentLoaded', resolve, { once: true }));
        }
        console.log('[chat-fix] DOM content loaded, proceeding with init.');

        try {
            // Load user info for navbar and to set state.currentUser.avatar
            // This needs to happen early so message avatars can be set correctly.
            const token = getAuthToken();
            if (token) {
                try {
                    const userResponse = await fetch('/api/user/current', { headers: { 'Authorization': token }});
                    if (userResponse.ok) {
                        const result = await userResponse.json();
                        if (result.code === 200 && result.data) {
                            const user = result.data;
                            const usernameEl = document.querySelector('.nav-user .username');
                            const avatarEl = document.querySelector('.nav-user .avatar');
                            if (usernameEl) usernameEl.textContent = user.username || 'User';
                            
                            state.currentUser = { username: user.username, avatar: null }; // Initialize
                            if (avatarEl && user.avatar) {
                                avatarEl.src = user.avatar; // This will trigger its onerror if it fails
                                // Wait for image to potentially load or error out before setting state.currentUser.avatar
                                await new Promise(resolve => {
                                    avatarEl.onload = () => { state.currentUser.avatar = avatarEl.src; resolve(); };
                                    avatarEl.onerror = () => { /* fixDefaultAvatar will handle this */ resolve(); };
                                    // If already loaded/broken
                                    if (avatarEl.complete) resolve();
                                });
                            }
                        }
                    } else { console.warn('[chat-fix] Failed to load current user for navbar.');}
                } catch (e) { console.warn('[chat-fix] Error fetching current user for navbar:', e); }
            }

            fixDefaultAvatar(); // Now fix all avatars, including potentially navbar one if it errored
            setupEventListeners(); 
            await loadChatHistory(); // This will also call switchToChat

            window.addEventListener('popstate', (event) => {
                if (event.state && event.state.chatId) {
                    // console.log('[chat-fix] Popstate: switching to chat:', event.state.chatId);
                    switchToChat(event.state.chatId);
                } else {
                    const urlParams = new URLSearchParams(window.location.search);
                    const chatIdFromUrl = urlParams.get('id');
                    if (chatIdFromUrl && state.chatSessions[chatIdFromUrl]) {
                        switchToChat(chatIdFromUrl);
                    } else if (Object.keys(state.chatSessions).length > 0) {
                        const mostRecentChat = Object.values(state.chatSessions).sort((a,b) => new Date(b.updatedAt) - new Date(a.updatedAt))[0];
                        if(mostRecentChat) switchToChat(mostRecentChat.id);
                    } else {
                        // No chat in URL, no history, show empty state
                         document.getElementById('currentChatTitle').textContent = 'New Chat';
                         document.getElementById('chatMessages').innerHTML = '<div class="text-center text-muted p-4">No conversations. Create one!</div>';
                         enableChatInputs(false);
                    }
                }
            });
            
            console.log('[chat-fix] Chat system initialization complete.');
        } catch (error) {
            console.error('[chat-fix] Critical error during initialization:', error);
            showToast(`Chat initialization failed: ${error.message}`, 'error');
            const chatArea = document.getElementById('chatMessages');
            if (chatArea) chatArea.innerHTML = `<div class="alert alert-danger m-3">Chat system failed to initialize. Please try refreshing the page. Details: ${escapeHtml(error.message)}</div>`;
        }
    }

    // Expose a minimal API for debugging or specific calls if needed
    window.chatFix = {
        init: init, // Allow re-init for debugging
        loadMessages: loadChatMessages, // For retry buttons
        getState: () => state // For debugging
    };

    init(); // Start the initialization process

})(); 