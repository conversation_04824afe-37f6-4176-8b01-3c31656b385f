/**
 *  通用导航栏样式
 * 包含导航栏基础样式、用户下拉菜单、头像、用户信息编辑等样式
 */

/*  全局样式 - 为固定导航栏预留空间 */
body {
    padding-top: 70px;
    margin: 0;
}

/*  导航栏基础样式 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0 2rem;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    margin: 0;
}

.nav-brand a {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-brand a:hover {
    color: #f8f9fa;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-item {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

/*  用户下拉菜单样式 */
.nav-user {
    position: relative;
}

.nav-user .user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: white;
}

.nav-user .user-info:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-user .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.nav-user .username {
    font-weight: 500;
    margin-right: 4px;
}

.nav-user .dropdown-arrow {
    margin-left: 8px;
    font-size: 0.8rem;
    transition: transform 0.3s ease;
    opacity: 0.8;
}

.nav-user .user-info.active .dropdown-arrow {
    transform: rotate(180deg);
}

.nav-user .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    border: 1px solid #e0e0e0;
    margin-top: 8px;
}

.nav-user .dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 下拉菜单头部 */
.dropdown-header {
    padding: 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
    color: white;
}

.dropdown-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.dropdown-user-info {
    flex: 1;
}

.dropdown-username {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 2px;
}

.dropdown-email {
    font-size: 0.85rem;
    opacity: 0.9;
}

/* 下拉菜单分隔线 */
.dropdown-divider {
    height: 1px;
    background: #e9ecef;
    margin: 8px 0;
}

/* 下拉菜单项 */
.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #007bff;
    text-decoration: none;
}

.dropdown-item.text-danger {
    color: #dc3545;
}

.dropdown-item.text-danger:hover {
    background-color: #fff5f5;
    color: #dc3545;
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-user .dropdown-menu {
        min-width: 260px;
        right: -10px;
    }

    .nav-user .username {
        display: none;
    }

    .dropdown-header {
        padding: 12px;
    }

    .dropdown-avatar {
        width: 40px;
        height: 40px;
    }

    .dropdown-username {
        font-size: 0.9rem;
    }

    .dropdown-email {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .nav-user .dropdown-menu {
        min-width: 240px;
        right: -20px;
    }
}

/* 动画效果 */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-user .dropdown-menu.show {
    animation: fadeInDown 0.3s ease;
}

/* 头像悬停效果 */
.nav-user .user-info:hover .avatar {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.6);
}

.dropdown-header:hover .dropdown-avatar {
    transform: scale(1.05);
}

/* 自定义滚动条（如果下拉菜单内容过多） */
.nav-user .dropdown-menu {
    max-height: 400px;
    overflow-y: auto;
}

.nav-user .dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.nav-user .dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.nav-user .dropdown-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.nav-user .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 退出登录弹窗自定义样式 */
.logout-popup {
    border-radius: 12px !important;
}

.logout-popup .swal2-title {
    color: #dc3545;
}

.logout-popup .swal2-content {
    color: #6c757d;
}

/* 加载状态样式 */
.nav-user .user-info.loading {
    opacity: 0.7;
    pointer-events: none;
}

.nav-user .user-info.loading .username::after {
    content: "...";
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% {
        color: rgba(255, 255, 255, 0);
        text-shadow: .25em 0 0 rgba(255, 255, 255, 0),
                     .5em 0 0 rgba(255, 255, 255, 0);
    }
    40% {
        color: white;
        text-shadow: .25em 0 0 rgba(255, 255, 255, 0),
                     .5em 0 0 rgba(255, 255, 255, 0);
    }
    60% {
        text-shadow: .25em 0 0 white,
                     .5em 0 0 rgba(255, 255, 255, 0);
    }
    80%, 100% {
        text-shadow: .25em 0 0 white,
                     .5em 0 0 white;
    }
}

/*  用户信息编辑模态框样式 */
.avatar-upload-section {
    padding: 20px;
    border: 2px dashed #e9ecef;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.avatar-upload-section:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.avatar-preview {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 15px;
    cursor: pointer;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
}

.avatar-preview:hover {
    border-color: #007bff;
    transform: scale(1.05);
}

.preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.avatar-preview:hover .avatar-overlay {
    opacity: 1;
}

.avatar-overlay i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

/* 模态框自定义样式 */
#userProfileModal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

#userProfileModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

#userProfileModal .modal-title {
    font-weight: 600;
}

#userProfileModal .btn-close {
    filter: invert(1);
}

#userProfileModal .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

#userProfileModal .form-control {
    border-radius: 8px;
    border: 1px solid #e9ecef;
    padding: 10px 12px;
    transition: all 0.3s ease;
}

#userProfileModal .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#userProfileModal .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 20px;
}

/* 响应式设计 - 导航栏 */
@media (max-width: 992px) {
    .navbar {
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 0.25rem;
    }

    .nav-item {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        height: 60px;
        padding: 0 0.5rem;
    }

    .nav-brand a {
        font-size: 1.3rem;
    }

    .nav-menu {
        display: none; /* 在小屏幕上隐藏菜单，可以后续添加汉堡菜单 */
    }

    .nav-user .username {
        display: none;
    }

    .nav-user .dropdown-menu {
        min-width: 260px;
        right: -10px;
    }
}
