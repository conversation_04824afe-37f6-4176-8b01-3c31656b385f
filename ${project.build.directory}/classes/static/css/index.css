.main-content {
    max-width: 1200px;
    margin: var(--nav-height) auto 0;
    padding: 2rem;
}

.welcome-section {
    text-align: center;
    padding: 3rem 0;
}

.welcome-section h1 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--secondary-text);
    line-height: 1.6;
} 