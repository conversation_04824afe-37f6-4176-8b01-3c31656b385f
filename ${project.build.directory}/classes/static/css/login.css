:root {
    --primary-color: #0066FF;
    --text-color: #1A1A1A;
    --secondary-text: #666666;
    --border-color: #E5E5E5;
    --background-color: #F5F5F5;
    --hover-color: #0052CC;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
}

.page-container {
    display: flex;
    min-height: 100vh;
}

.left-section {
    flex: 1;
    background: linear-gradient(135deg, #0066FF 0%, #00A3FF 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: white;
}

.brand {
    text-align: center;
}

.brand h1 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.slogan {
    font-size: 1.25rem;
    opacity: 0.9;
}

.right-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: white;
}

.form-container {
    width: 100%;
    max-width: 420px;
    padding: 2rem;
}

.form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.form-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--secondary-text);
    font-size: 0.875rem;
}

.input-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.input-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
}

.forgot-password {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
    cursor: pointer;
}

.submit-btn {
    width: 100%;
    padding: 0.875rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.submit-btn:hover {
    background-color: var(--hover-color);
}

.switch-form {
    margin-top: 1.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: var(--secondary-text);
}

.switch-form a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.switch-form a:hover {
    text-decoration: underline;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

form {
    animation: fadeIn 0.3s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-container {
        flex-direction: column;
    }
    
    .left-section {
        padding: 3rem 1rem;
    }
    
    .form-container {
        padding: 1.5rem;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #FFFFFF;
        --secondary-text: #A0A0A0;
        --border-color: #333333;
        --background-color: #1A1A1A;
    }
    
    .right-section {
        background: #222222;
    }
    
    .input-group input {
        background: #333333;
        color: white;
    }
}

/* Toast 提示样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast-success {
    background: #10B981;
    color: white;
}

.toast-error {
    background: #EF4444;
    color: white;
}

.toast-info {
    background: #3B82F6;
    color: white;
} 