/**
 * 历史试卷管理样式
 */

/* 历史试卷表格样式 */
#paperHistoryTableContainer {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
}

/* 历史试卷卡片样式 */
.card:has(#paperHistoryTableContainer) {
    max-height: 500px;
    display: flex;
    flex-direction: column;
}

.card:has(#paperHistoryTableContainer) .card-body {
    flex: 1;
    overflow: hidden;
    padding: 0;
}

/* 左侧列布局优化 */
.col-md-4 {
    display: flex;
    flex-direction: column;
    /* 移除max-height限制，允许自然滚动 */
}

.col-md-4 .card {
    flex-shrink: 0;
}

/* 知识点选择区域高度限制 */
.knowledge-points-container {
    max-height: 400px;
    overflow-y: auto;
}

#paperHistoryTableContainer thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#paperHistoryTable tr {
    transition: all 0.2s ease;
}

#paperHistoryTable tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 试卷标题样式 */
.paper-title {
    font-weight: 500;
    color: #2c3e50;
    text-decoration: none;
    display: block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.paper-title:hover {
    color: #007bff;
    text-decoration: none;
}

/* 创建时间样式 */
.paper-create-time {
    font-size: 12px;
    color: #6c757d;
}

/* 总分样式 */
.paper-total-score {
    font-weight: 600;
    color: #28a745;
    font-size: 14px;
}

/* 操作按钮组样式 - 平摊显示 */
.paper-actions {
    display: flex;
    gap: 2px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

/* 超小按钮样式 */
.btn-xs {
    padding: 2px 6px;
    font-size: 10px;
    line-height: 1.2;
    border-radius: 3px;
}

/* 按钮颜色优化 */
.paper-actions .btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.paper-actions .btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.paper-actions .btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.paper-actions .btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.paper-actions .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

.paper-actions .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

.paper-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.paper-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 预览按钮 */
.btn-preview {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-preview:hover {
    background-color: #138496;
    border-color: #117a8b;
    color: white;
}

/* 下载按钮 */
.btn-download {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-download:hover {
    background-color: #218838;
    border-color: #1e7e34;
    color: white;
}

/* 克隆按钮 */
.btn-clone {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-clone:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #212529;
}

/* 删除按钮 */
.btn-delete {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-delete:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
}

/* 分页样式优化 */
.pagination {
    margin: 0;
    padding: 0;
}

.pagination .page-item {
    margin: 0 2px;
}

.pagination .page-link {
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* 响应式分页 */
@media (max-width: 576px) {
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .pagination .page-item {
        margin: 1px;
    }

    .pagination .page-link {
        padding: 4px 8px;
        font-size: 11px;
    }

    /* 在小屏幕上隐藏部分页码 */
    .pagination .page-item:not(.active):not(.disabled):nth-child(n+6):nth-child(-n+20) {
        display: none;
    }
}

/* 分页信息样式 */
#paperHistoryInfo {
    font-size: 11px;
    color: #6c757d;
}

#paperHistoryInfo i {
    color: #007bff;
}

/* 空状态样式 */
.empty-state {
    padding: 40px 20px;
    text-align: center;
}

.empty-state i {
    color: #dee2e6;
    margin-bottom: 15px;
}

.empty-state .lead {
    color: #6c757d;
    font-size: 16px;
    margin-bottom: 8px;
}

.empty-state small {
    color: #adb5bd;
    font-size: 12px;
}

/* 加载状态样式 */
.loading-state {
    padding: 40px 20px;
    text-align: center;
}

.loading-state .spinner-border {
    width: 2rem;
    height: 2rem;
    margin-bottom: 15px;
}

/* 头部按钮样式 */
.card-header .btn-group .btn {
    border: 1px solid rgba(255,255,255,0.3);
    color: rgba(255,255,255,0.8);
    background: transparent;
}

.card-header .btn-group .btn:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-color: rgba(255,255,255,0.5);
}

.card-header .btn-group .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 表格行高亮效果 */
.table-primary {
    background-color: rgba(0,123,255,0.1) !important;
    animation: highlightFade 3s ease-out;
}

@keyframes highlightFade {
    0% {
        background-color: rgba(0,123,255,0.3) !important;
    }
    100% {
        background-color: rgba(0,123,255,0.1) !important;
    }
}

/* 删除确认模态框样式 */
.delete-confirm-modal .modal-header {
    background-color: #dc3545;
    color: white;
}

.delete-confirm-modal .modal-body {
    padding: 30px;
    text-align: center;
}

.delete-confirm-modal .warning-icon {
    color: #dc3545;
    font-size: 48px;
    margin-bottom: 20px;
}

/* 批量操作样式 */
.batch-actions {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 10px 15px;
    display: none;
}

.batch-actions.show {
    display: block;
}

.batch-actions .btn {
    margin-right: 8px;
    font-size: 12px;
}

/* 选择框样式 */
.paper-checkbox {
    transform: scale(1.2);
    margin-right: 8px;
}

/* 工具提示样式 */
.tooltip-inner {
    font-size: 11px;
    max-width: 200px;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 6px;
}

.dropdown-item {
    font-size: 12px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

/* 状态标签样式 */
.paper-status {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.paper-status.status-normal {
    background-color: #d4edda;
    color: #155724;
}

.paper-status.status-draft {
    background-color: #fff3cd;
    color: #856404;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
.table-responsive::-webkit-scrollbar {
    height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 右侧历史试卷区域样式 */
.col-lg-3 #paperHistoryTableContainer {
    max-height: 500px;
    overflow-y: auto;
}

.col-lg-3 .paper-title {
    max-width: 120px;
    font-size: 12px;
}

.col-lg-3 .paper-create-time {
    font-size: 11px;
}

.col-lg-3 .paper-actions .btn {
    padding: 2px 6px;
    font-size: 10px;
}

.col-lg-3 .dropdown-menu {
    min-width: 120px;
    font-size: 11px;
}

.col-lg-3 .dropdown-item {
    padding: 4px 8px;
}

/* 右侧历史试卷卡片高度限制 */
.col-lg-3 .card {
    max-height: 600px;
    display: flex;
    flex-direction: column;
}

.col-lg-3 .card-body {
    flex: 1;
    overflow: hidden;
}

/* 移动端历史试卷样式 */
@media (max-width: 991px) {
    .d-lg-none .card {
        margin-top: 20px;
    }
}

/* 分页信息在右侧的样式调整 */
.col-lg-3 #paperHistoryInfo {
    font-size: 10px;
}

.col-lg-3 .pagination .page-link {
    padding: 4px 8px;
    font-size: 10px;
}

/* 右侧历史试卷表格紧凑样式 */
.col-lg-3 table th,
.col-lg-3 table td {
    padding: 6px 4px;
    font-size: 11px;
    vertical-align: middle;
}

.col-lg-3 table th {
    font-size: 10px;
    font-weight: 600;
}

/* 右侧历史试卷按钮组样式 */
.col-lg-3 .card-header .btn-group .btn {
    padding: 2px 6px;
    font-size: 10px;
}

.col-lg-3 .card-header h5 {
    font-size: 14px;
}

.col-lg-3 .badge {
    font-size: 9px;
    padding: 2px 6px;
}

/* 增强下载菜单样式 */
.enhanced-download-menu {
    min-width: 280px;
    padding: 0;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
}

.enhanced-download-menu .dropdown-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 8px 16px;
    margin: 0;
    font-size: 12px;
}

.enhanced-download-menu .version-selector {
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    padding: 6px 12px;
    margin-bottom: 8px;
}

.enhanced-download-menu .version-selector:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    outline: none;
}

.enhanced-download-menu .version-description {
    font-size: 11px;
    color: #6c757d;
    line-height: 1.4;
    margin-bottom: 8px;
}

.enhanced-download-menu .dropdown-divider {
    margin: 8px 0;
}

.enhanced-download-menu .dropdown-item {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 4px;
    margin: 2px 8px;
    transition: all 0.2s ease;
}

.enhanced-download-menu .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: none; /* 移除transform避免布局问题 */
}

.enhanced-download-menu .dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

/* 防止下拉菜单内容点击时关闭 */
.enhanced-download-menu .px-3 {
    padding-left: 16px !important;
    padding-right: 16px !important;
}

/* 确保下拉菜单在小屏幕上也能正常显示 */
@media (max-width: 576px) {
    .enhanced-download-menu {
        min-width: 250px;
        max-width: 90vw;
    }
}

/* 修复Bootstrap dropdown在表格中的z-index问题 */
.table .dropdown-menu {
    z-index: 1050;
}

/* 确保页面可以正常滚动 */
body {
    overflow-x: hidden;
    overflow-y: auto;
}

.container-fluid {
    min-height: 100vh;
    padding-bottom: 50px; /* 确保底部有足够空间 */
}

/* 修复右侧历史试卷区域的滚动问题 */
.col-lg-3 {
    /* 移除固定高度限制 */
    height: auto;
    max-height: none;
}

.col-lg-3 .card {
    /* 移除固定高度限制，允许内容自然展开 */
    max-height: none;
    height: auto;
}

.col-lg-3 .card-body {
    /* 保持flex布局但允许滚动 */
    flex: 1;
    overflow: visible;
}

/* 历史试卷表格容器的滚动优化 */
#paperHistoryTableContainer {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 移动端历史试卷区域滚动优化 */
@media (max-width: 991px) {
    .d-lg-none .table-responsive {
        max-height: 500px;
        overflow-y: auto;
    }
}

/* 难度分布模态框样式 */
#difficultyDistributionModal .difficulty-stat {
    padding: 15px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.02);
    margin-bottom: 10px;
}

#difficultyDistributionModal .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

#difficultyDistributionModal .stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

#difficultyDistributionModal .question-item {
    transition: all 0.2s ease;
    background: #f8f9fa;
}

#difficultyDistributionModal .question-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#difficultyDistributionModal .question-title {
    font-size: 0.9rem;
    color: #495057;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
}

#difficultyDistributionModal .nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
}

#difficultyDistributionModal .nav-tabs .nav-link.active {
    background-color: #fff;
    border-bottom: 2px solid #007bff;
    color: #007bff;
}

#difficultyDistributionModal .tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 0.25rem 0.25rem;
    padding: 15px;
    background: #fff;
}

/* 难度分布滚动条样式 */
#difficultyDistributionModal .difficulty-questions::-webkit-scrollbar {
    width: 6px;
}

#difficultyDistributionModal .difficulty-questions::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#difficultyDistributionModal .difficulty-questions::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#difficultyDistributionModal .difficulty-questions::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
