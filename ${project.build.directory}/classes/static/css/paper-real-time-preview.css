/**
 * 试卷实时预览样式
 */

.real-time-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.preview-header h4 {
    color: #495057;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.preview-header h4 i {
    color: #007bff;
    margin-right: 8px;
}

.preview-loading {
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.preview-error {
    margin-bottom: 20px;
}

.preview-stats-card .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-item {
    text-align: center;
    padding: 10px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.preview-warnings {
    margin-bottom: 20px;
}

.preview-warnings .alert {
    border-left: 4px solid #ffc107;
}

.preview-content {
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    min-height: 300px;
}

.preview-topics {
    padding: 20px;
}

.topic-type-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 20px;
}

.topic-type-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.topic-type-title {
    color: #495057;
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
    display: inline-block;
}

.topic-type-title i {
    color: #007bff;
    margin-right: 8px;
}

.topics-list {
    margin-left: 20px;
}

.topic-item {
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.topic-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.topic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.topic-number {
    font-weight: bold;
    color: #495057;
    font-size: 16px;
}

.topic-difficulty {
    font-size: 11px;
    padding: 4px 8px;
}

.topic-title {
    font-size: 14px;
    line-height: 1.5;
    color: #212529;
    margin-bottom: 10px;
}

.topic-options {
    margin-top: 10px;
    padding-left: 20px;
}

.option-item {
    margin-bottom: 5px;
    font-size: 13px;
    color: #495057;
    line-height: 1.4;
}

.topic-meta {
    border-top: 1px solid #f1f3f4;
    padding-top: 8px;
    margin-top: 10px;
}

.topic-meta span {
    margin-right: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .real-time-preview {
        padding: 15px;
        margin-top: 15px;
    }
    
    .preview-header h4 {
        font-size: 16px;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .stat-item {
        padding: 8px;
    }
    
    .stat-value {
        font-size: 20px;
    }
    
    .topics-list {
        margin-left: 10px;
    }
    
    .topic-type-title {
        font-size: 16px;
    }
}

/* 加载动画 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 空状态样式 */
.preview-content .text-center {
    padding: 40px 20px;
}

.preview-content .fa-3x {
    opacity: 0.3;
}

/* 开关样式 */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    font-size: 13px;
    color: #6c757d;
}

/* 按钮样式 */
.btn-outline-primary {
    font-size: 12px;
    padding: 4px 12px;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

/* 警告样式增强 */
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-warning i {
    color: #f39c12;
    margin-right: 8px;
}

/* 难度标签样式 */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* 卡片阴影效果 */
.card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.card:hover {
    box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

/* 滚动条样式 */
.preview-content {
    max-height: 600px;
    overflow-y: auto;
}

.preview-content::-webkit-scrollbar {
    width: 6px;
}

.preview-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.preview-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.topic-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.preview-stats-card {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 题目内容样式增强 */
.topic-title {
    word-wrap: break-word;
    word-break: break-all;
}

/* 数学公式支持 */
.topic-title .katex,
.option-item .katex {
    font-size: 1em;
}

/* 打印样式 */
@media print {
    .real-time-preview {
        background: white;
        box-shadow: none;
        border: none;
    }
    
    .preview-header,
    .preview-stats-card,
    .preview-warnings {
        display: none;
    }
    
    .topic-item {
        break-inside: avoid;
        margin-bottom: 15px;
    }
}
