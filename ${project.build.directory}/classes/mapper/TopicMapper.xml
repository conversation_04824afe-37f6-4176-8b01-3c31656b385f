<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.repository.TopicMapper">

    <!-- 获取题目ID列表 -->
    <select id="findIdsByKnowledgeAndTypeAndDifficulty" resultType="java.lang.Integer">
        SELECT id FROM topic_bak 
        WHERE know_id = #{knowId} 
        AND type = #{type} 
        AND difficulty BETWEEN #{difficulty} - 0.1 AND #{difficulty} + 0.1 
        ORDER BY id
    </select>

    <!-- 查询题目总数 -->
    <select id="countAllTopics" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM topic_bak
    </select>

    <!-- 获取任意一道题目 -->
    <select id="findAnyTopic" resultType="com.edu.maizi_edu_sys.entity.Topic">
        SELECT * FROM topic_bak LIMIT 1
    </select>

    <!-- 根据知识点ID查询题目列表 -->
    <select id="selectFromBakByKnowId" resultType="com.edu.maizi_edu_sys.entity.Topic">
        SELECT * FROM topic_bak WHERE know_id = #{knowId}
    </select>

    <!-- 根据题型查询题目列表 -->
    <select id="selectByType" resultType="com.edu.maizi_edu_sys.entity.Topic">
        SELECT * FROM topic_bak WHERE type = #{type}
    </select>

    <!-- 根据知识点ID查询题目ID列表 -->
    <select id="findAnyTopicsByKnowledgeId" resultType="java.lang.Integer">
        SELECT id FROM topic_bak WHERE know_id = #{knowId}
    </select>

    <!-- 获取题目ID列表（较宽范围）-->
    <select id="findIdsByKnowledgeAndTypeWithWiderRange" resultType="java.lang.Integer">
        SELECT id FROM topic_bak 
        WHERE know_id = #{knowId} 
        AND type = #{type} 
        AND difficulty BETWEEN #{difficulty} - 0.2 AND #{difficulty} + 0.2 
        ORDER BY id
    </select>

    <!-- 统计题目数量 -->
    <select id="countFromBakByKnowId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM topic_bak WHERE know_id = #{knowId}
    </select>

    <!-- 根据知识点ID统计各题型的数量 -->
    <select id="countTopicsByTypeForKnowledgePoints" resultType="java.util.Map">
        SELECT 
            t.type as type,
            COUNT(*) as count
        FROM topic_bak t
        WHERE t.knowledge_id IN 
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY t.type
    </select>

    <!-- 统计一个知识点内各个题型的题目数量 -->
    <select id="countTopicsByTypeForKnowledgePoint" resultType="java.util.Map">
        SELECT
            type,
            COUNT(*) as count
        FROM
            topic_bak
        WHERE
            know_id = #{knowId}
        GROUP BY
            type
    </select>

    <!-- 获取题目上传统计数据 -->
    <select id="getTopicStatistics" resultType="java.util.Map">
        SELECT
        <choose>
            <when test="type == 'week'">
                DATE_FORMAT(created_at, '%x-%v') as week,
            </when>
            <when test="type == 'month'">
                DATE_FORMAT(created_at, '%Y-%m') as month,
            </when>
            <otherwise>
                DATE(created_at) as day,
            </otherwise>
        </choose>
        COUNT(*) as count
        FROM topic_bak
        <where>
            <if test="startDate != null and endDate != null">
                created_at BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
        GROUP BY
        <choose>
            <when test="type == 'week'">week</when>
            <when test="type == 'month'">month</when>
            <otherwise>day</otherwise>
        </choose>
        ORDER BY
        <choose>
            <when test="type == 'week'">week</when>
            <when test="type == 'month'">month</when>
            <otherwise>day</otherwise>
        </choose>
        ASC
    </select>

</mapper>