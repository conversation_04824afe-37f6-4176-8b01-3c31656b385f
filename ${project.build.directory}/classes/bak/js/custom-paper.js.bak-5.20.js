/**
 * 自由组卷功能 JavaScript
 * 实现知识点自由选择、题型配置和试卷生成
 */

// 全局变量存储已选择的知识点
let selectedKnowledgePoints = [];

// 初始化自由组卷功能
$(document).ready(function() {
    // 初始化事件监听
    initCustomPaperEvents();
    
    // 初始化难度分布进度条
    updateDifficultyProgressBars();
});

// 初始化自由组卷相关事件
function initCustomPaperEvents() {
    // 打开自由组卷模态框
    $('#customPaperBtn').click(function() {
        $('#customPaperModal').modal('show');
    });
    
    // 添加知识点按钮点击事件
    $('#addKnowledgePointBtn').click(function() {
        loadKnowledgePoints();
        $('#knowledgePointSelectionModal').modal('show');
    });
    
    // 知识点搜索框输入事件
    $('#knowledgePointSearchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterKnowledgePoints(searchTerm);
    });
    
    // 确认知识点选择按钮点击事件
    $('#confirmKnowledgePointSelection').click(function() {
        const selectedItems = $('#knowledgePointsList .list-group-item.active');
        if (selectedItems.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: '未选择知识点',
                text: '请至少选择一个知识点',
                confirmButtonText: '确定'
            });
            return;
        }
        
        selectedItems.each(function() {
            const id = $(this).data('id');
            const name = $(this).text().trim();
            
            // 检查是否已经添加过
            if (!isKnowledgePointSelected(id)) {
                addKnowledgePointItem(id, name);
            }
        });
        
        $('#knowledgePointSelectionModal').modal('hide');
        updateNoKnowledgePointsMessage();
    });
    
    // 难度分布输入框变化事件
    $('.difficulty-input').on('input', function() {
        updateDifficultyProgressBars();
    });
    
    // 题型数量和分值输入框变化事件
    $('.question-count, .question-score').on('input', function() {
        updateTypeTotalScores();
    });
    
    // 生成试卷按钮点击事件
    $('#generateCustomPaperBtn').click(function() {
        if (validateCustomPaperForm()) {
            submitCustomPaperForm();
        }
    });
    
    // 监听知识点容器的事件委托
    $('#knowledgePointsContainer').on('click', '.remove-knowledge-point', function() {
        const knowledgePointItem = $(this).closest('.knowledge-point-item');
        const id = knowledgePointItem.data('id');
        const name = knowledgePointItem.find('.card-title').text().trim();
        
        // 确认删除
        Swal.fire({
            title: '移除知识点',
            html: `确定要移除知识点 <strong>${escapeHtml(name)}</strong> 吗？`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '移除',
            cancelButtonText: '取消',
            confirmButtonColor: '#dc3545'
        }).then((result) => {
            if (result.isConfirmed) {
                // 从已选择列表中移除
                selectedKnowledgePoints = selectedKnowledgePoints.filter(item => item.id !== id);
                
                // 添加渐变效果
                knowledgePointItem.fadeOut(300, function() {
                    // 移除DOM元素
                    $(this).remove();
                    
                    // 显示成功提示
                    Swal.fire({
                        title: '已移除',
                        text: `知识点 ${name} 已成功移除`,
                        icon: 'success',
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000
                    });
                    
                    // 更新空消息显示
                    updateNoKnowledgePointsMessage();
                });
            }
        });
    });
    
    // 添加题目数量增加按钮事件
    $('#knowledgePointsContainer').on('click', '.increase-count', function() {
        const input = $(this).closest('.input-group').find('.knowledge-question-count');
        let value = parseInt(input.val()) || 0;
        input.val(value + 1);
    });
    
    // 添加题目数量减少按钮事件
    $('#knowledgePointsContainer').on('click', '.decrease-count', function() {
        const input = $(this).closest('.input-group').find('.knowledge-question-count');
        let value = parseInt(input.val()) || 0;
        if (value > 0) {
            input.val(value - 1);
        }
    });
    
    // 初始化题型分数计算
    updateTypeTotalScores();
}

// 渲染知识点分类
function renderCategories(categories) {
    console.log('渲染知识点分类:', categories);
    
    if (!categories || categories.length === 0) {
        $('#knowledge-categories').html('<div class="list-group-item text-muted">暂无知识点分类</div>');
        return;
    }
    
    let html = '';
    
    // 渲染分类列表
    categories.forEach(function(category) {
        const categoryId = category.id;
        const categoryName = category.groupName || category.name || '分类 #' + categoryId;
        html += `<a href="javascript:void(0)" class="list-group-item category-item" data-id="${categoryId}">${escapeHtml(categoryName)}</a>`;
    });
    
    $('#knowledge-categories').html(html);
    
    // 添加分类点击事件
    $('.category-item').click(function() {
        // 更新活动状态
        $('.category-item').removeClass('active');
        $(this).addClass('active');
        
        // 获取分类 ID
        const categoryId = $(this).data('id');
        const categoryName = $(this).text();
        
        // 更新当前分类名称
        $('#current-category-name').text(categoryName);
        
        // 显示加载中状态
        $('#knowledge-points-list').html('<div class="text-center py-3"><div class="spinner-border text-info" role="status"></div><p class="mt-2">正在加载知识点...</p></div>');
        
        // 加载该分类的知识点
        loadKnowledgePointsByCategory(categoryId);
    });
    
    // 默认选中第一个分类
    if (categories.length > 0) {
        $('.category-item:first').click();
    }
}

// 根据分类加载知识点
function loadKnowledgePointsByCategory(categoryId) {
    $.ajax({
        url: '/api/knowledge/points',
        type: 'GET',
        data: { groupId: categoryId },
        dataType: 'json',
        success: function(response) {
            console.log('知识点响应:', response);
            
            // 兼容不同的API响应格式
            if (!(response.success === true || response.code === 0 || response.code === 200)) {
                $('#knowledge-points-list').html(`<div class="alert alert-warning">加载知识点失败: ${response.message || '未知错误'}</div>`);
                return;
            }
            
            // 获取数据，兼容不同的响应格式
            const data = response.data || response.result || [];
            
            if (data.length === 0) {
                $('#knowledge-points-list').html('<div class="list-group-item text-muted">该分类下暂无知识点</div>');
                return;
            }
            
            // 标准化知识点数据
            const standardizedPoints = data.map(function(point) {
                if (!point || !point.id) return null;
                
                let standardPoint = {
                    id: point.id,
                    name: null,
                    groupId: null,
                    groupName: null,
                    isFree: 0,
                    topicCount: 0
                };
                
                // 尝试不同的属性名称来获取知识点名称
                const nameProps = ['name', 'pointName', 'knowledgeName', 'title', 'content'];
                for (let prop of nameProps) {
                    if (point[prop] !== undefined && point[prop] !== null && point[prop] !== '') {
                        standardPoint.name = point[prop];
                        break;
                    }
                }
                
                // 如果还是没有名称，使用ID
                if (!standardPoint.name) {
                    standardPoint.name = '知识点 #' + point.id;
                }
                
                // 设置其他属性
                standardPoint.isFree = point.isFree === 1 || point.isFree === true || point.isFree === '1';
                standardPoint.groupId = point.groupId || point.classificationId || categoryId;
                standardPoint.groupName = point.groupName || $('#current-category-name').text();
                standardPoint.topicCount = parseInt(point.topicCount) || 0;
                
                return standardPoint;
            }).filter(point => point !== null);
            
            // 渲染知识点列表
            let html = '<div class="list-group list-group-flush">';
            
            standardizedPoints.forEach(function(point) {
                const isSelected = isKnowledgePointSelected(point.id);
                const activeClass = isSelected ? 'active' : '';
                const disabledAttr = isSelected ? 'disabled' : '';
                
                // 添加免费标记和题目数量信息
                let badgeHtml = '';
                if (point.isFree) {
                    badgeHtml += `<span class="badge badge-success badge-pill ml-1">免费</span>`;
                }
                
                if (point.topicCount > 0) {
                    badgeHtml += `<span class="badge badge-info badge-pill ml-1">${point.topicCount} 题</span>`;
                }
                
                html += `<a href="javascript:void(0)" class="list-group-item list-group-item-action knowledge-point-item ${activeClass}" 
                            data-id="${point.id}" data-name="${escapeHtml(point.name)}" ${disabledAttr}>
                            <span>${escapeHtml(point.name)}</span>
                            <span>${badgeHtml}</span>
                         </a>`;
            });
            
            html += '</div>';
            
            $('#knowledge-points-list').html(html);
            
            // 添加知识点点击事件
            $('#knowledge-points-list .knowledge-point-item:not([disabled])').click(function() {
                $(this).toggleClass('active');
            });
        },
        error: function(xhr, status, error) {
            console.error('加载知识点失败:', error);
            $('#knowledge-points-list').html(`<div class="alert alert-danger">加载知识点失败: ${error}</div>`);
        }
    });
}


// 加载知识点数据
function loadKnowledgePoints() {
    // 显示加载中状态
    $('#loadingKnowledgePoints').show();
    $('#knowledgePointsList').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在加载知识点分类...</p></div>');
    
    // 加载知识点分类
    $.ajax({
        url: '/api/knowledge/groups',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            console.log('知识点分类响应:', response);
            
            if (response.success === true) {
                // 渲染知识点分类
                renderKnowledgePointGroups(response.data);
            } else {
                $('#knowledgePointsList').html('<div class="alert alert-warning">无法加载知识点分类</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('加载知识点分类失败:', error);
            $('#knowledgePointsList').html('<div class="alert alert-danger">加载知识点分类失败: ' + error + '</div>');
        }
    });
}

// 渲染知识点分组
function renderKnowledgePointGroups(groups) {
    console.log('渲染知识点分组:', groups);

    // 隐藏加载指示器
    $('#loadingKnowledgePoints').hide();

    if (!groups || groups.length === 0) {
        $('#knowledgePointsList').html('<div class="alert alert-info">暂无知识点数据</div>');
        return;
    }

    // 创建知识点选择界面的HTML
    let html = `
    <div class="knowledge-points-container">
        <div class="row">
            <!-- 左侧分类列表 -->
            <div class="col-md-4">
                <div class="list-group knowledge-categories" style="max-height: 400px; overflow-y: auto;">
    `;

    // 添加分类列表
    groups.forEach(function(group, index) {
        const active = index === 0 ? 'active' : '';
        html += `<a href="javascript:void(0)" class="list-group-item list-group-item-action ${active}" 
                    data-group-id="${group.id}" data-type="category">${escapeHtml(group.groupName)} 
                    <span class="badge badge-primary badge-pill">${group.count || 0}</span></a>`;
    });

    html += `
                </div>
            </div>
            
            <!-- 右侧知识点列表 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(135deg, #0062cc, #007bff); color: white;">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 id="selected-category-name" class="mb-0" style="font-weight: 500; white-space: nowrap; color: rgba(255, 255, 255, 0.95); text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">${groups[0].groupName}</h5>
                            </div>
                            <div class="col-md-6 text-right">
                                <div class="input-group input-group-sm ml-auto" style="max-width: 220px;">
                                    <input type="text" class="form-control shadow-sm border-0" id="knowledge-point-search" 
                                           placeholder="搜索知识点..." style="border-radius: 20px 0 0 20px;">
                                    <div class="input-group-append">
                                        <span class="input-group-text bg-white text-primary" style="border-radius: 0 20px 20px 0; border: none;"><i class="fas fa-search"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- 已选择知识点标签区域 -->
                        <div id="selected-knowledge-tags" class="px-3 py-2" style="background-color: #f8f9fa; border-bottom: 1px solid #eee; display: none;">
                            <div class="d-flex flex-wrap align-items-center">
                                <small class="text-muted mr-2">已选择:</small>
                                <div id="selected-tags-container" class="d-flex flex-wrap">
                                    <!-- 已选择的知识点标签将在这里动态显示 -->
                                </div>
                            </div>
                        </div>
                        <div id="knowledge-points-container" class="list-group list-group-flush" style="max-height: 400px; overflow-y: auto;">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="mt-2">正在加载知识点...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 确认按钮 -->
    <div class="text-center mt-3">
        <button id="confirm-knowledge-selection" class="btn btn-primary">
            <i class="fas fa-check mr-1"></i>确认选择
        </button>
        <button id="cancel-knowledge-selection" class="btn btn-secondary ml-2">
            <i class="fas fa-times mr-1"></i>取消
        </button>
    </div>
    `;

    // 设置HTML内容
    $('#knowledgePointsList').html(html);

    // 初始化选中的知识点数组，用于跟踪跨分类选择
    // 使用对象数组存储完整的知识点信息，而不仅仅是ID
    window.selectedKnowledgePoints = [];
    
    // 添加分类点击事件
    $('.knowledge-categories .list-group-item').click(function() {
        // 更新活动状态
        $('.knowledge-categories .list-group-item').removeClass('active');
        $(this).addClass('active');
        
        // 获取分类ID和名称
        const categoryId = $(this).data('group-id');
        const categoryName = $(this).text().trim().split(' ')[0]; // 移除徽章部分
        
        // 更新选中的分类名称
        $('#selected-category-name').text(categoryName);
        
        // 加载该分类的知识点，保持之前的选中状态
        loadKnowledgePointsForCategory(categoryId, window.selectedKnowledgePoints);
    });

    // 添加搜索功能
    $('#knowledge-point-search').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();

        $('#knowledge-points-container .knowledge-point-item').each(function() {
            const text = $(this).text().toLowerCase();
            $(this).toggle(text.indexOf(searchTerm) > -1);
        });
    });

    // 添加确认按钮点击事件
    $('#confirm-knowledge-selection').click(function() {
        const tags = $('#selected-tags-container .badge');
        console.log('确认选择按钮点击 - 选中的标签元素:', tags);

        if (tags.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: '未选择知识点',
                text: '请至少选择一个知识点',
                confirmButtonText: '确定'
            });
            return;
        }

        const loadingDialog = Swal.fire({
            title: '正在处理...',
            text: '正在添加知识点',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 使用 setTimeout 来让加载提示有机会渲染
        setTimeout(() => {
            let addedCount = 0;
            const pointsToAdd = [];

            tags.each(function() {
                const pointId = $(this).data('id');
                const pointName = $(this).data('name'); // 从 data-name 获取名称
                const pointType = $(this).data('type');

                // 确保是知识点并且有ID和名称
                if (pointType === 'point' && pointId && pointName) {
                    pointsToAdd.push({ id: pointId, name: pointName });
                } else {
                    console.warn('跳过无效标签:', $(this));
                }
            });
            
            console.log('从标签解析出的待添加知识点:', pointsToAdd);

            pointsToAdd.forEach(point => {
                if (!isKnowledgePointSelected(point.id)) {
                    addKnowledgePointItem(point.id, point.name);
                    addedCount++;
                }
            });

            updateNoKnowledgePointsMessage();
            window.selectedKnowledgePoints = []; // 清空模态框内部的跟踪数组
            $('#selected-tags-container').empty(); // 清空标签显示区域
            $('#selected-counter-container').hide(); // 隐藏计数器
            $('#knowledgePointSelectionModal').modal('hide');
            loadingDialog.close();

            if (addedCount > 0 || pointsToAdd.length > 0) { // Show message if any points were processed
                let messageText = '';
                if (addedCount > 0) {
                    messageText += '已成功添加 ' + addedCount + ' 个新知识点。';
                }
                const skippedCount = pointsToAdd.length - addedCount;
                if (skippedCount > 0) {
                    messageText += (addedCount > 0 ? ' ' : '') + skippedCount + ' 个知识点已存在或无效，已跳过。';
                }
                if (addedCount === 0 && skippedCount === 0 && pointsToAdd.length > 0) {
                     messageText = '所有选中的知识点均已存在或无效。';
                }

                Swal.fire({
                    icon: addedCount > 0 ? 'success' : 'info',
                    title: addedCount > 0 ? '操作完成' : '提示',
                    text: messageText,
                    timer: 2000,
                    showConfirmButton: false
                });
            } else { // This case (pointsToAdd.length === 0) is already handled by the initial check
                // Kept for logical completeness, though unlikely to be hit if initial check for tags.length works
                Swal.fire({
                    icon: 'info',
                    title: '无操作',
                    text: '没有有效的知识点被选中处理。',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        }, 100); // 短暂延迟以允许UI更新
    });

    // 添加取消按钮点击事件
    $('#cancel-knowledge-selection').click(function() {
        // 重置选中状态
        window.selectedKnowledgePoints = [];
        
        // 关闭模态框
        $('#knowledgePointSelectionModal').modal('hide');
    });
    
    // 模态框关闭时重置选中状态
    $('#knowledgePointSelectionModal').on('hidden.bs.modal', function() {
        window.selectedKnowledgePoints = [];
    });

    // 默认加载第一个分类的知识点
    if (groups.length > 0) {
        loadKnowledgePointsForCategory(groups[0].id);
    }
}

// 根据分类ID加载知识点
function loadKnowledgePointsForCategory(categoryId) {
    // 确保选中的知识点数组已初始化
    if (!window.selectedKnowledgePoints) {
        window.selectedKnowledgePoints = [];
    }
    $('#knowledge-points-container').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在加载知识点...</p></div>');

    $.ajax({
        url: '/api/knowledge/points',
        type: 'GET',
        data: { groupId: categoryId },
        dataType: 'json',
        success: function(response) {
            console.log('知识点响应:', response);

            if (response.success !== true) {
                $('#knowledge-points-container').html('<div class="alert alert-warning">加载知识点失败</div>');
                return;
            }

            const points = response.data || [];

            if (points.length === 0) {
                $('#knowledge-points-container').html('<div class="text-center py-3">该分类下暂无知识点</div>');
                return;
            }

            let html = '';

            points.forEach(function(point) {
                // 检查是否已经添加到主界面
                const isSelected = isKnowledgePointSelected(point.id);
                // 检查是否在模态框中选中
                const isActiveInModal = window.selectedKnowledgePoints ? window.selectedKnowledgePoints.some(p => p.id === point.id) : false;
                const activeClass = isActiveInModal ? 'active' : '';
                const disabledAttr = isSelected ? 'disabled' : '';
                
                // 将知识点名称存储到缓存中，以便在跨分类选择时使用
                if (!window.knowledgePointNames) window.knowledgePointNames = {};
                window.knowledgePointNames[point.id] = point.pointName || point.name || '知识点 #' + point.id;

                html += `<a href="javascript:void(0)" class="list-group-item list-group-item-action knowledge-point-item ${activeClass}" 
                            data-id="${point.id}" data-name="${escapeHtml(point.pointName || point.name || '知识点 #' + point.id)}" data-type="point" ${disabledAttr}>
                            ${escapeHtml(point.pointName || point.name || '知识点 #' + point.id)}
                         </a>`;
            });

            $('#knowledge-points-container').html(html);

            // 添加知识点点击事件
            $('#knowledge-points-container .knowledge-point-item:not([disabled])').click(function() {
                $(this).toggleClass('active');
                
                // 检查是否是知识点列表项，而不是分类列表项
                const pointId = $(this).data('id');
                const pointName = $(this).data('name');
                const pointType = $(this).data('type');
                
                // 确保这是知识点而不是分类
                if (!pointId || pointType === 'category') {
                    console.warn('试图选择分类作为知识点，已忽略');
                    $(this).removeClass('active'); // 取消选中状态
                    return;
                }
                
                if ($(this).hasClass('active')) {
                    // 检查是否已经选中
                    const existingPoint = window.selectedKnowledgePoints.find(p => p.id === pointId);
                    if (!existingPoint) {
                        // 添加完整的知识点对象
                        window.selectedKnowledgePoints.push({
                            id: pointId,
                            name: pointName,
                            type: 'point'
                        });
                        console.log('添加知识点到选中列表:', pointId, pointName);
                    }
                } else {
                    // 移除知识点
                    const index = window.selectedKnowledgePoints.findIndex(p => p.id === pointId);
                    if (index > -1) {
                        window.selectedKnowledgePoints.splice(index, 1);
                        console.log('从选中列表移除知识点:', pointId);
                    }
                }
                
                // 更新选中计数器和标签
                updateSelectedCounter();
                updateSelectedTags();
            });
            
            // 更新选中计数器
            function updateSelectedCounter() {
                // 使用新的数据结构计算选中的知识点数量
                const count = window.selectedKnowledgePoints.length;
                $('#selected-counter').text(count);
                if (count > 0) {
                    $('#selected-counter-container').show();
                } else {
                    $('#selected-counter-container').hide();
                    $('#selected-knowledge-tags').hide();
                }
            }
            
            // 更新已选择的知识点标签
            function updateSelectedTags() {
                const tagsContainer = $('#selected-tags-container');
                tagsContainer.empty();
                
                // 如果没有选中的知识点，隐藏标签区域
                if (window.selectedKnowledgePoints.length === 0) {
                    $('#selected-knowledge-tags').hide();
                    return;
                }
                
                // 显示标签区域
                $('#selected-knowledge-tags').show();

                // 为每个选中的知识点创建标签
                window.selectedKnowledgePoints.forEach(function(point) {
                    // 创建标签元素
                    const typeAttribute = point.type || 'point'; // Use point.type, default to 'point'
                    const tagHtml = `
                        <div class="badge badge-info m-1 p-2" data-id="${point.id}" data-name="${escapeHtml(point.name)}" data-type="${typeAttribute}" style="font-size: 0.85rem;">
                            ${escapeHtml(point.name)}
                            <i class="fas fa-times ml-1 remove-tag" data-id="${point.id}" style="cursor: pointer;"></i>
                        </div>
                    `;

                    tagsContainer.append(tagHtml);
                });

                // 添加移除标签的点击事件
                $('.remove-tag').click(function(e) {
                    e.stopPropagation();
                    const pointId = $(this).data('id');
                    
                    // 从选中数组中移除
                    const index = window.selectedKnowledgePoints.findIndex(p => p.id === pointId);
                    if (index > -1) {
                        window.selectedKnowledgePoints.splice(index, 1);
                    }
                    
                    // 更新选中状态
                    $(`.knowledge-point-item[data-id="${pointId}"]`).removeClass('active');
                    
                    // 更新计数器和标签
                    updateSelectedCounter();
                    updateSelectedTags();
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('加载知识点失败:', error);
            $('#knowledge-points-container').html('<div class="alert alert-danger">加载知识点失败: ' + error + '</div>');
        }
    });
}

// 过滤知识点列表
function filterKnowledgePoints(searchTerm) {
    $('#knowledgePointsList .list-group-item-action').each(function() {
        const text = $(this).text().toLowerCase();
        if (text.includes(searchTerm)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
    
    // 隐藏没有可见子项的分组标题
    $('#knowledgePointsList .list-group-item-secondary').each(function() {
        const nextUntilSecondary = $(this).nextUntil('.list-group-item-secondary');
        const hasVisibleItems = nextUntilSecondary.filter(':visible').length > 0;
        
        if (hasVisibleItems) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

// 添加知识点项
function addKnowledgePointItem(id, name) {
    // 添加到已选择列表
    selectedKnowledgePoints.push({
        id: id,
        name: name,
        type: 'point' // Add type for consistency
    });
    
    // 使用模板创建DOM元素
    let templateContent = $('#knowledgePointItemTemplate').html();
    templateContent = templateContent.replace(/{id}/g, id);
    templateContent = templateContent.replace(/{name}/g, escapeHtml(name));
    
    const $newItem = $(templateContent);

    // 找到 "包含简答题" 开关并默认设置为关闭
    const $shortAnswerSwitch = $newItem.find('.include-short-answer');
    if ($shortAnswerSwitch.length > 0) {
        $shortAnswerSwitch.prop('checked', false);
    }

    // 添加到容器
    $('#knowledgePointsContainer').append($newItem);

    // 更新 "暂无知识点" 的提示信息
    updateNoKnowledgePointsMessage(); 
}

// 检查知识点是否已被选择
function isKnowledgePointSelected(id) {
    return selectedKnowledgePoints.some(item => item.id == id);
}

// 更新没有知识点时的提示消息
function updateNoKnowledgePointsMessage() {
    if (selectedKnowledgePoints.length > 0) {
        $('#noKnowledgePointsMessage').hide();
    } else {
        $('#noKnowledgePointsMessage').show();
    }
}

// 更新难度分布进度条
function updateDifficultyProgressBars() {
    let easy = parseInt($('#easyPercentage').val()) || 0;
    let medium = parseInt($('#mediumPercentage').val()) || 0;
    let hard = parseInt($('#hardPercentage').val()) || 0;
    
    // 确保总和为100%
    const total = easy + medium + hard;
    if (total !== 100) {
        $('#difficultyError').text(`当前总和为 ${total}%，请确保总和为 100%`).show();
    } else {
        $('#difficultyError').hide();
    }
    
    // 更新进度条
    $('#easyProgressBar').css('width', `${easy}%`).text(`简单 ${easy}%`);
    $('#mediumProgressBar').css('width', `${medium}%`).text(`中等 ${medium}%`);
    $('#hardProgressBar').css('width', `${hard}%`).text(`困难 ${hard}%`);
}

// 更新题型总分
function updateTypeTotalScores() {
    let totalScore = 0;
    
    // 计算每种题型的总分
    $('.question-count').each(function(index) {
        const count = parseInt($(this).val()) || 0;
        const score = parseInt($('.question-score').eq(index).val()) || 0;
        const total = count * score;
        
        $(this).closest('tr').find('.type-total-score').text(total);
        totalScore += total;
    });
    
    // 更新总分
    $('#customTotalScore').text(totalScore);
}

// 验证自由组卷表单
function validateCustomPaperForm() {
    // 验证标题
    const title = $('#customPaperTitle').val().trim();
    if (!title) {
        Swal.fire({
            icon: 'error',
            title: '表单验证失败',
            text: '请输入试卷标题',
            confirmButtonText: '确定'
        });
        return false;
    }
    
    // 验证难度分布
    const easy = parseInt($('#easyPercentage').val()) || 0;
    const medium = parseInt($('#mediumPercentage').val()) || 0;
    const hard = parseInt($('#hardPercentage').val()) || 0;
    
    if (easy + medium + hard !== 100) {
        Swal.fire({
            icon: 'error',
            title: '难度分布错误',
            text: '难度分布百分比之和必须为100%',
            confirmButtonText: '确定'
        });
        return false;
    }
    
    // 验证题型配置
    let isValid = true;
    let totalQuestions = 0;
    
    $('.question-count').each(function() {
        const count = parseInt($(this).val());
        if (isNaN(count) || count < 0) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
            totalQuestions += count;
        }
    });
    
    if (totalQuestions === 0) {
        $('.question-count').first().addClass('is-invalid');
        Swal.fire({
            icon: 'error',
            title: '配置错误',
            text: '至少需要一道题目才能生成试卷！',
            confirmButtonText: '确定'
        });
        return false;
    }
    
    $('.question-score').each(function() {
        const score = parseInt($(this).val());
        if (isNaN(score) || score < 0) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    if (!isValid) {
        Swal.fire({
            icon: 'error',
            title: '配置错误',
            text: '请检查题型配置，确保所有数量和分值都是有效的非负数！',
            confirmButtonText: '确定'
        });
        return false;
    }
    
    // 验证知识点
    if (selectedKnowledgePoints.length === 0) {
        Swal.fire({
            icon: 'error',
            title: '知识点未配置',
            text: '请至少添加一个知识点',
            confirmButtonText: '确定'
        });
        return false;
    }
    
    return true;
}

// 提交自由组卷表单
function submitCustomPaperForm() {
    // 构建知识点配置
    const knowledgePointConfigs = [];
    $('#knowledgePointsContainer .knowledge-point-item').each(function() {
        const $item = $(this);
        const id = $item.data('id');
        const questionCountInput = $item.find('.knowledge-point-question-count');
        const includeShortAnswerSwitch = $item.find('.include-short-answer'); // Find the switch

        const config = {
            knowledgePointId: parseInt(id),
            // 移除单个知识点数量配置，强制使用全局配置
            questionCount: null, // 已移除，将由后端根据全局设置和所选知识点智能分配
            forceIncludeShortAnswer: includeShortAnswerSwitch.is(':checked') // Add the switch state
        };
        knowledgePointConfigs.push(config);
    });

    // 检查是否有知识点被选择
    if (knowledgePointConfigs.length === 0) {
        Swal.fire({
            icon: 'error',
            title: '知识点未配置',
            text: '请至少添加一个知识点',
            confirmButtonText: '确定'
        });
        return;
    }

    // 构建请求数据
    const formData = {
        title: $('#customPaperTitle').val(),
        type: parseInt($('#customPaperType').val()),
        typeScoreMap: {
            SINGLE_CHOICE: parseInt($('#customSingleChoiceScore').val()) || 0,
            MULTIPLE_CHOICE: parseInt($('#customMultipleChoiceScore').val()) || 0,
            JUDGMENT: parseInt($('#customJudgmentScore').val()) || 0,
            FILL_BLANK: parseInt($('#customFillScore').val()) || 0,
            SHORT_ANSWER: parseInt($('#customShortAnswerScore').val()) || 0,
            SUBJECTIVE: parseInt($('#customSubjectiveScore').val()) || 0
        },
        difficultyDistribution: {
            easy: parseInt($('#easyPercentage').val()) / 100,
            medium: parseInt($('#mediumPercentage').val()) / 100,
            hard: parseInt($('#hardPercentage').val()) / 100
        },
        knowledgePointConfigs: knowledgePointConfigs
    };
    
    // 打印请求数据以便调试
    console.log('Submitting custom paper generation request payload:', JSON.stringify(formData, null, 2));
    
    // 禁用按钮并显示加载效果
    $('#generateCustomPaperBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>生成中...');
    
    // 发送请求
    $.ajax({
        url: '/api/papers/generate-custom',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            // 恢复按钮状态
            $('#generateCustomPaperBtn').prop('disabled', false).html('<i class="fas fa-magic mr-1"></i>生成试卷');
            
            if (response.code === 200) {
                Swal.fire({
                    icon: 'success',
                    title: '生成成功',
                    text: '试卷已生成，可在历史记录中查看。',
                    confirmButtonText: '查看试卷'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 刷新历史记录
                        loadPaperHistory();
                        // 切换到历史记录标签
                        $('#paperHistoryTab').tab('show');
                        // 关闭自由组卷模态框
                        $('#customPaperModal').modal('hide');
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '生成失败',
                    text: response.message || '生成试卷时发生错误，请重试。',
                    confirmButtonText: '确定'
                });
            }
        },
        error: function(xhr, status, error) {
            // 恢复按钮状态
            $('#generateCustomPaperBtn').prop('disabled', false).html('<i class="fas fa-magic mr-1"></i>生成试卷');
            
            let errorMessage = '生成试卷时发生错误，请重试。';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            Swal.fire({
                icon: 'error',
                title: '生成失败',
                text: errorMessage,
                confirmButtonText: '确定'
            });
        }
    });
}