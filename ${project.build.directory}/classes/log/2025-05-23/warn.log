2025-05-23 18:55:38.152 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-23 18:55:38.153 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-23 18:55:38.153 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. <PERSON> already defined with the same name!
2025-05-23 18:55:38.153 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 18:55:46.792 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 18:55:48.543 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 18:56:15.980 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 18:56:15.980 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 18:56:15.985 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics selected or enforced for paper generation. Title: 识记类  专项练习
2025-05-23 19:06:06.299 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:06:06.299 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:06:06.299 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:06:06.300 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:06:17.674 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 19:06:19.468 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 19:06:33.644 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:06:33.644 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:06:33.648 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics selected or enforced for paper generation. Title: 识记类  专项练习
2025-05-23 19:17:08.587 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:17:08.588 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:17:08.588 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:17:08.588 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:17:16.320 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 19:17:17.863 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 19:19:08.539 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:19:09.008 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics found for type singleChoice in database. Trying with TopicTypeUtils.normalize...
2025-05-23 19:19:09.259 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics found for type judgment in database. Trying with TopicTypeUtils.normalize...
2025-05-23 19:19:09.260 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:19:09.262 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics found for type singleChoice in database. Trying with TopicTypeUtils.normalize...
2025-05-23 19:19:09.264 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics found for type judgment in database. Trying with TopicTypeUtils.normalize...
2025-05-23 19:19:09.267 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics selected or enforced for paper generation. Title: 识记类  专项练习
2025-05-23 19:21:23.772 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:21:23.773 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:21:23.773 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:21:23.773 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:21:31.214 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 19:21:32.772 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 19:22:02.613 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:22:02.613 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:22:02.618 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics selected or enforced for paper generation. Title: 集合考点  专项练习
2025-05-23 19:22:15.062 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:22:15.062 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-05-23 19:22:15.063 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - No topics selected or enforced for paper generation. Title: 集合考点  专项练习
2025-05-23 19:30:49.482 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:30:49.482 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:30:49.483 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:30:49.483 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:30:57.257 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 19:30:58.784 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 19:34:49.956 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:34:49.956 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:34:49.956 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:34:49.956 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 19:34:58.256 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 19:35:00.023 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 19:45:51.472 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /paper/generate-user-prefs.js
2025-05-23 19:52:00.659 [http-nio-8081-exec-3] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /paper/generate-user-prefs.js
2025-05-23 19:58:12.245 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /paper/generate-user-prefs.js
2025-05-23 20:10:06.969 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /paper/generate-user-prefs.js
2025-05-23 20:12:36.205 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /paper/generate-user-prefs.js
2025-05-23 20:16:55.194 [http-nio-8081-exec-9] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /paper/generate-user-prefs.js
2025-05-23 20:45:39.571 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:45:39.819 [http-nio-8081-exec-6] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:45:39.881 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:45:39.933 [http-nio-8081-exec-1] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:45:39.968 [http-nio-8081-exec-2] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:45:40.008 [http-nio-8081-exec-2] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:45:40.028 [http-nio-8081-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.edu.maizi_edu_sys.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.edu.maizi_edu_sys.entity.dto.ApiResponse] with preset Content-Type 'application/pdf'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1331)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1142)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 20:48:24.572 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:48:24.572 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:48:24.572 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:48:24.573 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:48:32.377 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 20:48:34.189 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 20:48:43.965 [http-nio-8081-exec-1] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:48:44.232 [http-nio-8081-exec-1] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:48:45.379 [http-nio-8081-exec-3] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:48:45.433 [http-nio-8081-exec-3] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:48:45.472 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:48:45.518 [http-nio-8081-exec-4] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:48:45.540 [http-nio-8081-exec-4] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.edu.maizi_edu_sys.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.edu.maizi_edu_sys.entity.dto.ApiResponse] with preset Content-Type 'application/pdf'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1331)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1142)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 20:48:54.799 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:48:54.829 [http-nio-8081-exec-8] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:48:58.624 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:48:58.666 [http-nio-8081-exec-9] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:48:58.712 [http-nio-8081-exec-10] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:48:58.762 [http-nio-8081-exec-10] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:48:58.781 [http-nio-8081-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.edu.maizi_edu_sys.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.edu.maizi_edu_sys.entity.dto.ApiResponse] with preset Content-Type 'application/pdf'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1331)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1142)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 20:58:33.249 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:58:33.249 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:58:33.249 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:58:33.249 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 20:58:41.040 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 20:58:42.590 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 20:58:51.808 [http-nio-8081-exec-6] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:58:52.039 [http-nio-8081-exec-6] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:58:53.751 [http-nio-8081-exec-7] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:58:53.816 [http-nio-8081-exec-7] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:58:53.854 [http-nio-8081-exec-8] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - SimSun字体加载失败，使用SimHei作为正文字体: C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes\fonts\simsun.ttf is not a valid TTF or OTF file.
2025-05-23 20:58:53.900 [http-nio-8081-exec-8] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1452, SQLState: 23000
2025-05-23 20:58:53.924 [http-nio-8081-exec-8] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.edu.maizi_edu_sys.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.edu.maizi_edu_sys.entity.dto.ApiResponse] with preset Content-Type 'application/pdf'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1331)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1142)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 21:00:41.867 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Type 'judgment' (requested 5) has insufficient questions available (1). Deficit: 4
2025-05-23 21:00:41.870 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Some topic types do not have enough questions available. Will use all available topics of requested types.
2025-05-23 21:00:41.873 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Discrepancy for type 'judgment' (requested key 'JUDGMENT'). Requested: 5, Actual: 1. 知识点中可能没有足够此类型的题目.
2025-05-23 21:00:41.879 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 题库中可用题目不足。
请求总题目数：10，实际可用题目数：6。
这可能是因为选择的知识点中没有足够的题目。
具体题型统计：
- 单选题: 请求5/实际5
- 多选题: 请求0/实际0
- 判断题: 请求5/实际1 (不足4)
- 填空题: 请求0/实际0
- 简答题: 请求0/实际0
2025-05-23 21:00:41.879 [http-nio-8081-exec-4] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 请注意：判断题数量不足，请求5题，实际仅有1题。(全局配置)
2025-05-23 21:23:18.611 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 21:23:18.612 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 21:23:18.612 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 21:23:18.613 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-05-23 21:23:27.509 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-23 21:23:29.033 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-23 21:24:47.975 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Type 'judgment' (requested 5) has insufficient questions available (1). Deficit: 4
2025-05-23 21:24:47.977 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Some topic types do not have enough questions available. Will use all available topics of requested types.
2025-05-23 21:24:47.981 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Discrepancy for type 'judgment' (requested key 'JUDGMENT'). Requested: 5, Actual: 1. 知识点中可能没有足够此类型的题目.
2025-05-23 21:24:47.986 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 题库中可用题目不足。
请求总题目数：10，实际可用题目数：6。
这可能是因为选择的知识点中没有足够的题目。
具体题型统计：
- 单选题: 请求5/实际5
- 多选题: 请求0/实际0
- 判断题: 请求5/实际1 (不足4)
- 填空题: 请求0/实际0
- 简答题: 请求0/实际0
2025-05-23 21:24:47.986 [http-nio-8081-exec-9] WARN  c.e.m.service.impl.PaperGenerationServiceImpl - 请注意：判断题数量不足，请求5题，实际仅有1题。(全局配置)
