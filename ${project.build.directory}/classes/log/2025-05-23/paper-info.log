2025-05-23 19:21:32.679 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-23 19:22:02.480 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=195, questionCount=null, includeShortAnswer=true)], title=集合考点  专项练习, totalScore=53, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-23 19:22:02.549 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 250. Earmarked: 0, General Pool (unique): 250. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=3}
2025-05-23 19:22:02.564 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 250 topics. IDs: [90387, 90388, 90389, 90390, 90391, 90392, 90393, 90394, 90395, 90396, 90397, 90398, 90399, 90400, 90401, 90402, 90403, 90404, 90405, 90406, 90407, 90408, 90409, 90410, 90411, 90412, 90413, 90414, 90415, 90416, 90417, 90418, 90419, 90420, 90421, 90422, 90423, 90424, 90425, 90426, 90427, 90428, 90429, 90430, 90431, 90432, 90433, 90434, 90435, 90436, 90437, 90438, 90439, 90440, 90441, 90442, 90443, 90444, 90445, 90446, 91690, 91691, 91692, 91693, 91695, 91696, 91698, 91699, 91701, 91702, 91704, 91705, 91707, 91708, 91710, 91712, 91713, 91714, 91715, 91716, 91717, 91719, 91720, 91721, 91722, 91723, 91724, 91725, 91726, 91727, 91813, 91814, 91815, 91816, 91817, 91818, 91819, 91820, 91821, 91822, 91823, 91824, 91825, 91826, 91827, 91828, 91829, 91830, 91831, 91832, 91833, 91834, 91835, 91836, 91837, 91838, 91839, 91840, 91841, 91842, 91854, 91856, 91857, 91858, 91860, 91861, 91862, 91863, 91864, 91865, 91866, 91867, 91868, 91870, 91871, 91872, 91873, 91874, 91875, 91876, 91877, 91878, 91879, 91880, 91881, 91882, 91883, 91884, 91885, 91886, 91887, 91888, 91889, 91890, 91891, 91892, 91893, 91894, 91895, 91896, 91905, 91906, 91907, 91908, 91909, 91910, 91911, 91913, 91914, 91915, 91916, 91917, 91918, 91919, 91920, 91921, 91923, 91924, 91925, 91926, 91932, 91933, 91934, 91936, 91937, 91938, 91939, 91940, 91942, 91943, 91944, 91945, 91946, 91947, 91948, 91950, 91951, 91952, 91953, 91954, 91976, 91978, 91981, 91982, 91984, 91985, 91988, 91994, 92000, 92005, 92007, 92014, 92049, 92051, 92053, 92055, 92058, 92059, 92062, 92063, 92064, 92066, 92067, 92069, 92107, 92110, 92112, 92113, 92115, 92117, 92118, 92119, 92120, 92121, 92122, 92123, 92124, 92126, 92127, 92934, 92936, 92937, 92939, 92942, 92945, 92948, 92949, 92951, 92952, 93118]
2025-05-23 19:22:02.565 [http-nio-8081-exec-7] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 19:22:02.565 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 250 topics available for GA (input size was 250). MinReuseIntervalDays: null
2025-05-23 19:22:02.565 [http-nio-8081-exec-7] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 19:22:02.565 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 250 topics remain.
2025-05-23 19:22:02.565 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 250 candidate topics...
2025-05-23 19:22:02.566 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-23 19:22:02.566 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.35
2025-05-23 19:22:02.569 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - 各题型可用题目数量: {singleChoice=115, judgment=135}
2025-05-23 19:22:02.580 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:02.597 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:02.604 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:02.610 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:02.611 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 30. Overall Best Fitness: {:.4f}. Generations without improvement: 0.0.
2025-05-23 19:22:02.611 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 45ms. Best solution fitness: {:.4f}, Selected 0.0 topics with total score: 0.
2025-05-23 19:22:02.613 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 0 topics with type distribution: {}
2025-05-23 19:22:02.613 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 0 topics.
2025-05-23 19:22:15.003 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=195, questionCount=null, includeShortAnswer=true)], title=集合考点  专项练习, totalScore=53, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_IN_BLANKS=3, SHORT_ANSWER=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_IN_BLANKS=3, SHORT_ANSWER=2}, minReuseIntervalDays=null)
2025-05-23 19:22:15.033 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 250. Earmarked: 0, General Pool (unique): 250. Remaining global counts for GA: {SHORT_ANSWER=2, SINGLE_CHOICE=5, JUDGMENT=5, FILL_IN_BLANKS=3, MULTIPLE_CHOICE=3}
2025-05-23 19:22:15.046 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 250 topics. IDs: [90387, 90388, 90389, 90390, 90391, 90392, 90393, 90394, 90395, 90396, 90397, 90398, 90399, 90400, 90401, 90402, 90403, 90404, 90405, 90406, 90407, 90408, 90409, 90410, 90411, 90412, 90413, 90414, 90415, 90416, 90417, 90418, 90419, 90420, 90421, 90422, 90423, 90424, 90425, 90426, 90427, 90428, 90429, 90430, 90431, 90432, 90433, 90434, 90435, 90436, 90437, 90438, 90439, 90440, 90441, 90442, 90443, 90444, 90445, 90446, 91690, 91691, 91692, 91693, 91695, 91696, 91698, 91699, 91701, 91702, 91704, 91705, 91707, 91708, 91710, 91712, 91713, 91714, 91715, 91716, 91717, 91719, 91720, 91721, 91722, 91723, 91724, 91725, 91726, 91727, 91813, 91814, 91815, 91816, 91817, 91818, 91819, 91820, 91821, 91822, 91823, 91824, 91825, 91826, 91827, 91828, 91829, 91830, 91831, 91832, 91833, 91834, 91835, 91836, 91837, 91838, 91839, 91840, 91841, 91842, 91854, 91856, 91857, 91858, 91860, 91861, 91862, 91863, 91864, 91865, 91866, 91867, 91868, 91870, 91871, 91872, 91873, 91874, 91875, 91876, 91877, 91878, 91879, 91880, 91881, 91882, 91883, 91884, 91885, 91886, 91887, 91888, 91889, 91890, 91891, 91892, 91893, 91894, 91895, 91896, 91905, 91906, 91907, 91908, 91909, 91910, 91911, 91913, 91914, 91915, 91916, 91917, 91918, 91919, 91920, 91921, 91923, 91924, 91925, 91926, 91932, 91933, 91934, 91936, 91937, 91938, 91939, 91940, 91942, 91943, 91944, 91945, 91946, 91947, 91948, 91950, 91951, 91952, 91953, 91954, 91976, 91978, 91981, 91982, 91984, 91985, 91988, 91994, 92000, 92005, 92007, 92014, 92049, 92051, 92053, 92055, 92058, 92059, 92062, 92063, 92064, 92066, 92067, 92069, 92107, 92110, 92112, 92113, 92115, 92117, 92118, 92119, 92120, 92121, 92122, 92123, 92124, 92126, 92127, 92934, 92936, 92937, 92939, 92942, 92945, 92948, 92949, 92951, 92952, 93118]
2025-05-23 19:22:15.046 [http-nio-8081-exec-8] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 19:22:15.046 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 250 topics available for GA (input size was 250). MinReuseIntervalDays: null
2025-05-23 19:22:15.047 [http-nio-8081-exec-8] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 19:22:15.047 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 250 topics remain.
2025-05-23 19:22:15.047 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 250 candidate topics...
2025-05-23 19:22:15.047 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-23 19:22:15.047 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.35
2025-05-23 19:22:15.048 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - 各题型可用题目数量: {singleChoice=115, judgment=135}
2025-05-23 19:22:15.049 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:15.053 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:15.058 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:15.061 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Best Fitness = {:.4f}, Score = 0.0, Type Distribution = 0
2025-05-23 19:22:15.061 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 30. Overall Best Fitness: {:.4f}. Generations without improvement: 0.0.
2025-05-23 19:22:15.062 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 15ms. Best solution fitness: {:.4f}, Selected 0.0 topics with total score: 0.
2025-05-23 19:22:15.062 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 0 topics with type distribution: {}
2025-05-23 19:22:15.062 [http-nio-8081-exec-8] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 0 topics.
2025-05-23 19:30:58.687 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-23 19:34:59.895 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-23 19:59:03.337 [http-nio-8081-exec-7] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=20, questionCount=5, includeShortAnswer=true), KnowledgePointConfigRequest(knowledgeId=21, questionCount=5, includeShortAnswer=true)], title=识记类 + 表达应用类  组合练习, totalScore=null, typeScoreMap={singleChoice=3, multipleChoice=3, judgment=2, fillBlank=3, shortAnswer=5}, difficultyCriteria={easy=30.0, medium=50.0, hard=20.0}, topicTypeCounts={singleChoice=5, multipleChoice=3, judgment=5, fillBlank=3, shortAnswer=2}, minReuseIntervalDays=null)
2025-05-23 20:13:41.087 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=20, questionCount=6, includeShortAnswer=false), KnowledgePointConfigRequest(knowledgeId=21, questionCount=7, includeShortAnswer=false)], title=识记类 + 表达应用类  组合练习, totalScore=null, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_BLANK=3, SHORT_ANSWER=5}, difficultyCriteria={easy=0.3, medium=0.5, hard=0.2}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=3, JUDGMENT=5, FILL_BLANK=0, SHORT_ANSWER=0}, minReuseIntervalDays=null)
2025-05-23 20:48:34.083 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-23 20:58:42.489 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-23 21:00:41.696 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=25, questionCount=10, includeShortAnswer=false)], title=集合考点  专项练习, totalScore=25, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_BLANK=3, SHORT_ANSWER=5}, difficultyCriteria={easy=0.3, medium=0.5, hard=0.2}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=0, JUDGMENT=5, FILL_BLANK=0, SHORT_ANSWER=0}, minReuseIntervalDays=null)
2025-05-23 21:00:41.795 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 10. Earmarked: 10, General Pool (unique): 0. Remaining global counts for GA: {SHORT_ANSWER=0, SINGLE_CHOICE=5, JUDGMENT=5, FILL_BLANK=0, MULTIPLE_CHOICE=0}
2025-05-23 21:00:41.800 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 10 topics. IDs: [3473, 35759, 9528, 34496, 32691, 29120, 34910, 34759, 35014, 33306]
2025-05-23 21:00:41.800 [http-nio-8081-exec-4] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 21:00:41.800 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 10 topics available for GA (input size was 10). MinReuseIntervalDays: null
2025-05-23 21:00:41.800 [http-nio-8081-exec-4] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 21:00:41.800 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 10 topics remain.
2025-05-23 21:00:41.800 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 10 candidate topics...
2025-05-23 21:00:41.801 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-23 21:00:41.801 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-23 21:00:41.816 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Best Fitness = {:.4f}, Score = 1.1739010989010987, Type Distribution = 24
2025-05-23 21:00:41.830 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Best Fitness = {:.4f}, Score = 1.1739010989010987, Type Distribution = 24
2025-05-23 21:00:41.838 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Best Fitness = {:.4f}, Score = 1.1739010989010987, Type Distribution = 24
2025-05-23 21:00:41.846 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Best Fitness = {:.4f}, Score = 1.1739010989010987, Type Distribution = 24
2025-05-23 21:00:41.847 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 30. Overall Best Fitness: {:.4f}. Generations without improvement: 1.1739010989010987.
2025-05-23 21:00:41.847 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 47ms. Best solution fitness: {:.4f}, Selected 1.1739010989010987 topics with total score: 8.
2025-05-23 21:00:41.849 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 8 topics with type distribution: {singleChoice=7, judgment=1}
2025-05-23 21:00:41.850 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 25, 同时保持题型分布
2025-05-23 21:00:41.850 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {singleChoice=7, judgment=1}
2025-05-23 21:00:41.854 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 24, Target score: 25. Number of topics: 8
2025-05-23 21:00:41.855 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=7, judgment=1}, Target type counts: {SHORT_ANSWER=0, SINGLE_CHOICE=5, JUDGMENT=5, FILL_BLANK=0, MULTIPLE_CHOICE=0}
2025-05-23 21:00:41.856 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Starting strict type-preserving optimization
2025-05-23 21:00:41.856 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Initial selection with strict type constraints: 0 topics, score=0, type distribution={JUDGMENT=0, SINGLE_CHOICE=0}
2025-05-23 21:00:41.857 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Strict type-preserving adjustment failed, trying more flexible approach
2025-05-23 21:00:41.858 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: After type-preserving adjustment: score=0 (target=25), type distribution={}
2025-05-23 21:00:41.858 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=7, judgment=1}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=0, JUDGMENT=5, FILL_BLANK=0, SHORT_ANSWER=0}
2025-05-23 21:00:41.858 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-23 21:00:41.858 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-23 21:00:41.858 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-23 21:00:41.859 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 24 but target is 25, attempting to adjust scores...
2025-05-23 21:00:41.859 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 24, Target score: 25. Number of topics: 8
2025-05-23 21:00:41.859 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=24
2025-05-23 21:00:41.859 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=8
2025-05-23 21:00:41.859 [http-nio-8081-exec-4] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 8 topics.
2025-05-23 21:00:41.873 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 18, Target score: 25. Number of topics: 6
2025-05-23 21:23:28.919 [main] INFO  c.e.m.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components (excluding unused CacheManager).
2025-05-23 21:24:47.693 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=20, questionCount=10, includeShortAnswer=false)], title=识记类 专项练习, totalScore=25, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGMENT=2, FILL_BLANK=3, SHORT_ANSWER=5}, difficultyCriteria={easy=0.3, medium=0.5, hard=0.2}, topicTypeCounts={SINGLE_CHOICE=5, MULTIPLE_CHOICE=0, JUDGMENT=5, FILL_BLANK=0, SHORT_ANSWER=0}, minReuseIntervalDays=null)
2025-05-23 21:24:47.868 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Initial topic pool size after KP config processing: 10. Earmarked: 10, General Pool (unique): 0. Remaining global counts for GA: {SHORT_ANSWER=0, SINGLE_CHOICE=5, JUDGMENT=5, FILL_BLANK=0, MULTIPLE_CHOICE=0}
2025-05-23 21:24:47.874 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 10 topics. IDs: [20039, 19121, 8448, 39015, 14662, 9491, 18296, 11212, 10330, 16631]
2025-05-23 21:24:47.874 [http-nio-8081-exec-9] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 21:24:47.874 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 3: After Diversity Filter, 10 topics available for GA (input size was 10). MinReuseIntervalDays: null
2025-05-23 21:24:47.874 [http-nio-8081-exec-9] INFO  c.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.
2025-05-23 21:24:47.874 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - After diversity filtering: 10 topics remain.
2025-05-23 21:24:47.874 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Step 4: Executing genetic algorithm with 10 candidate topics...
2025-05-23 21:24:47.875 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver started. Population: 100, Max Generations: 50, Crossover: 0.8, Mutation: 0.1, Tournament: 5
2025-05-23 21:24:47.876 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Fitness Weights: Score=0.4, Quality=0.2, DifficultyDist=0.2, CognitiveDist=0.2, KPCoverage=0.2
2025-05-23 21:24:47.897 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 0: Best Fitness = {:.4f}, Score = 1.2073534798534797, Type Distribution = 24
2025-05-23 21:24:47.913 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 10: Best Fitness = {:.4f}, Score = 1.2073534798534797, Type Distribution = 24
2025-05-23 21:24:47.928 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 20: Best Fitness = {:.4f}, Score = 1.2073534798534797, Type Distribution = 24
2025-05-23 21:24:47.941 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generation 30: Best Fitness = {:.4f}, Score = 1.2073534798534797, Type Distribution = 24
2025-05-23 21:24:47.941 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination triggered at generation 30. Overall Best Fitness: {:.4f}. Generations without improvement: 1.2073534798534797.
2025-05-23 21:24:47.941 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Genetic algorithm completed in 66ms. Best solution fitness: {:.4f}, Selected 1.2073534798534797 topics with total score: 8.
2025-05-23 21:24:47.945 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Genetic algorithm selected 8 topics with type distribution: {singleChoice=5, judgment=1, multipleChoice=2}
2025-05-23 21:24:47.946 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 25, 同时保持题型分布
2025-05-23 21:24:47.946 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {singleChoice=5, judgment=1, multipleChoice=2}
2025-05-23 21:24:47.955 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 24, Target score: 25. Number of topics: 8
2025-05-23 21:24:47.956 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=5, judgment=1, multipleChoice=2}, Target type counts: {SHORT_ANSWER=0, SINGLE_CHOICE=5, JUDGMENT=5, FILL_BLANK=0, MULTIPLE_CHOICE=0}
2025-05-23 21:24:47.956 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Starting strict type-preserving optimization
2025-05-23 21:24:47.958 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Initial selection with strict type constraints: 0 topics, score=0, type distribution={JUDGMENT=0, SINGLE_CHOICE=0}
2025-05-23 21:24:47.959 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Strict type-preserving adjustment failed, trying more flexible approach
2025-05-23 21:24:47.960 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: After type-preserving adjustment: score=0 (target=25), type distribution={}
2025-05-23 21:24:47.961 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={singleChoice=5, judgment=1, multipleChoice=2}, 目标={SINGLE_CHOICE=5, MULTIPLE_CHOICE=0, JUDGMENT=5, FILL_BLANK=0, SHORT_ANSWER=0}
2025-05-23 21:24:47.961 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 发现题目数量不满足要求，开始动态调整...
2025-05-23 21:24:47.961 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type SINGLE_CHOICE: 5 topics needed
2025-05-23 21:24:47.961 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Shortfall detected for type JUDGMENT: 5 topics needed
2025-05-23 21:24:47.962 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - After adjustment, total score is 24 but target is 25, attempting to adjust scores...
2025-05-23 21:24:47.963 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 24, Target score: 25. Number of topics: 8
2025-05-23 21:24:47.964 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 分数调整完成，调整后总分=24
2025-05-23 21:24:47.964 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - 动态调整完成，调整后题目数量=8
2025-05-23 21:24:47.965 [http-nio-8081-exec-9] INFO  c.e.m.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 8 topics.
2025-05-23 21:24:47.981 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics. Current score: 18, Target score: 25. Number of topics: 6
