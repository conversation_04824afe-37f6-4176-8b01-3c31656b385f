2025-05-22 10:08:50.294 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-22 10:09:52.642 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:52.663 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:52.842 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:53.283 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:55.187 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-05-22 10:09:56.362 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.362 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.375 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:09:56.375 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.375 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:09:56.376 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.377 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.379 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.387 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:09:56.387 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:09:56.391 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.394 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:09:56.394 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.395 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:56.399 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:09:59.823 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:59.824 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:09:59.824 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:59.825 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:59.828 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:09:59.830 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:59.830 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:09:59.836 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:09:59.837 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:09:59.839 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:09:59.840 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:10:02.228 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:10:03.108 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:10:06.155 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:10:48.623 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:10:48.645 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:10:54.217 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:11:52.570 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:12:48.162 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:12:52.555 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:12:52.558 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:12:52.558 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:12:52.558 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:12:52.559 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:12:53.744 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.779 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.780 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.785 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:12:53.796 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:12:53.797 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:12:53.797 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:12:53.797 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.705 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:01.706 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.214 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.214 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.214 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.214 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.214 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:02.214 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.215 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:13:02.216 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:14:13.032 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:14:13.049 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:14:45.370 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:15:45.387 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:15:45.390 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:15:45.390 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:15:45.392 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:15:45.399 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:15:45.399 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:15:45.400 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:15:45.402 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:15:45.402 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:15:45.402 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:15:45.402 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:15:55.601 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:16:01.838 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:16:01.854 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:16:07.347 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:16:09.941 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:19:38.523 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:21:36.738 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:21:36.741 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:21:36.741 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:21:36.741 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:21:36.741 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:21:41.027 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:21:41.027 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:21:41.046 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.046 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.046 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.047 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'group'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'group': 'groupQuestion'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:41.048 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.049 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.048 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'group'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'group': 'groupQuestion'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.050 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.051 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:41.051 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:41.051 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.438 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'group'
2025-05-22 10:21:54.439 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'group': 'groupQuestion'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:54.440 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.209 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.209 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.209 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'group'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'group': 'groupQuestion'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 10:21:55.210 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 10:30:13.838 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-22 10:30:30.608 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:30:30.621 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:30:30.621 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:30:30.647 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:30:30.647 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:30:30.648 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:30:30.649 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:30:30.657 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:30:30.660 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:30:30.662 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:30:30.670 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:30:32.193 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:33:44.893 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:33:44.897 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:33:44.897 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:33:44.900 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:33:44.905 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:33:44.906 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:33:44.910 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:33:44.910 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:33:44.910 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:33:44.911 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:33:44.911 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:42:10.654 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:10.658 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:42:10.658 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:10.660 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:10.664 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:10.665 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:10.668 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:42:10.668 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:42:10.668 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:42:10.668 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:42:10.668 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:42:30.345 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-22 10:42:37.495 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:37.506 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:37.506 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:37.527 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:42:37.527 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:37.527 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:42:37.529 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:42:37.536 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:42:37.539 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:42:37.540 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:42:37.548 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:58:17.568 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-22 10:58:27.242 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:27.255 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:27.256 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:27.296 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 10:58:27.296 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:27.297 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:27.298 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 10:58:27.306 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 10:58:27.309 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 10:58:27.311 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 10:58:27.319 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 10:58:54.672 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:54.695 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:57.941 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:58.512 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:58:58.870 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:59:01.024 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:59:03.680 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 10:59:38.960 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:01:48.946 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:01:48.949 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 11:01:48.949 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:01:48.951 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 11:01:48.957 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:01:48.957 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:01:48.957 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 11:01:48.961 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 11:01:48.961 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 11:01:48.961 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 11:01:48.962 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 11:02:07.838 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:07.854 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:09.078 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:10.431 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:12.386 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:13.804 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:14.375 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:14.800 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:15.467 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:21.972 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:37.913 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:42.911 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:42.915 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 11:02:42.916 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 11:02:42.916 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 11:02:42.916 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 11:02:43.867 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:43.867 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.900 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:02:43.900 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:43.901 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:43.901 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.959 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:52.960 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:52.960 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:52.960 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:52.960 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.730 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:02:53.731 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.682 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:10.683 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:04:10.683 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:04:10.683 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:10.683 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.404 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:04:11.405 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:22:16.436 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-22 11:22:46.910 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:22:46.925 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:22:46.926 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:22:46.972 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 11:22:46.972 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:22:46.973 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 11:22:46.975 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 11:22:46.984 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 11:22:46.988 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 11:22:46.989 [http-nio-8081-exec-8] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 11:22:46.999 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 11:22:49.752 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:23:04.802 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:23:04.821 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:23:04.934 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:23:04.950 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:23:11.573 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:23:14.298 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:24:12.742 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:24:28.790 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:24:28.795 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 11:24:28.795 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 11:24:28.795 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 11:24:28.796 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 11:24:33.681 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:24:33.681 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:33.707 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:33.708 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:33.708 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:33.708 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.708 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.707 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:33.708 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:33.708 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:33.708 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:33.708 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:33.708 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.708 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.708 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-4] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:33.709 [http-nio-8081-exec-3] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.777 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:38.778 [http-nio-8081-exec-5] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 11:24:39.564 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 11:24:39.565 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:02:57.715 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-22 12:03:41.686 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-22 12:03:54.640 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.663 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 12:03:54.663 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.663 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.684 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 12:03:54.692 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.693 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 12:03:54.693 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.694 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.697 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 12:03:54.828 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.829 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat history for token: eyJhbGciOi...
2025-05-22 12:03:54.831 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.866 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat history response status: 200, size: not null
2025-05-22 12:03:54.952 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.954 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat detail: chatId=32
2025-05-22 12:03:54.954 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.969 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat detail response: ApiResponse(code=200, message=success, data=ChatSession(id=32, userId=1920280447393230850, title=思想政治, knowId=218, bookUrl=https://www.yuque.com/shi-xi-qiang/kb/ofwnlgwerdm6u49i?singleDoc# 《普通高中教科书·思想政治必修1 中国特色社会主义_1-24》, createdAt=2025-05-14T16:55:25, updatedAt=2025-05-17T20:10:47, deleted=false))
2025-05-22 12:03:54.973 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:54.974 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat messages for chatId=32 (using /messages/{chatId} endpoint)
2025-05-22 12:03:54.981 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat messages response status: 200, data null? no
2025-05-22 12:03:59.608 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:59.608 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...UANA
2025-05-22 12:03:59.609 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:59.609 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:59.613 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-22 12:03:59.620 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:59.620 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:03:59.626 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 12:03:59.626 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 12:03:59.629 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 12:03:59.630 [http-nio-8081-exec-2] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 12:04:21.761 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:04:21.782 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:04:22.697 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:04:23.185 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:04:26.056 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:04:30.509 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:04:32.249 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:05:09.877 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:05:14.039 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:05:14.043 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - MyBatis-Plus Page parameters: current=1, size=10
2025-05-22 12:05:14.044 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added isDeleted=false condition to query
2025-05-22 12:05:14.044 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Added ORDER BY create_time DESC
2025-05-22 12:05:14.044 [http-nio-8081-exec-7] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - Executing paginated query with wrapper: WHERE (is_deleted = #{ew.paramNameValuePairs.MPGENVAL1}) ORDER BY create_time DESC
2025-05-22 12:05:15.298 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:05:15.328 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:15.329 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.330 [http-nio-8081-exec-6] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.337 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:15.347 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:15.348 [http-nio-8081-exec-9] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:20.276 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.277 [http-nio-8081-exec-10] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.791 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.791 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.791 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:20.791 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:20.791 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.791 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.791 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'multiple'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'multiple': 'multipleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'short'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'short': 'shortAnswer'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'judge'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'judge': 'judgment'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType called with typeKey: 'choice'
2025-05-22 12:05:20.792 [http-nio-8081-exec-1] DEBUG c.e.m.service.impl.PaperGenerationServiceImpl - mapTopicType result for 'choice': 'singleChoice'
2025-05-22 12:07:43.394 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
