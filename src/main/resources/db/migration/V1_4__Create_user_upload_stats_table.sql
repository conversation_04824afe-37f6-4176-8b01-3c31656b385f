-- 创建用户上传统计表
CREATE TABLE IF NOT EXISTS `user_upload_stats` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `upload_date` date NOT NULL COMMENT '上传日期',
  `topic_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日上传题目数量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_date` (`user_id`, `upload_date`) USING BTREE COMMENT '用户日期唯一索引',
  KEY `idx_user_id` (`user_id`) USING BTREE COMMENT '用户ID索引',
  KEY `idx_upload_date` (`upload_date`) USING BTREE COMMENT '上传日期索引',
  KEY `idx_user_date_count` (`user_id`, `upload_date`, `topic_count`) USING BTREE COMMENT '复合查询索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户上传统计表';

-- 插入示例数据（可选）
INSERT INTO `user_upload_stats` (`user_id`, `upload_date`, `topic_count`) VALUES
(1, '2024-01-01', 150),
(1, '2024-01-02', 200),
(1, '2024-01-03', 180),
(2, '2024-01-01', 100),
(2, '2024-01-02', 120),
(3, '2024-01-01', 300),
(3, '2024-01-02', 250)
ON DUPLICATE KEY UPDATE 
  `topic_count` = VALUES(`topic_count`),
  `updated_at` = CURRENT_TIMESTAMP;
