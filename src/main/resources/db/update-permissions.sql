-- 更新权限配置脚本
-- 为所有角色添加缺失的权限

-- 为管理员添加题目统计权限
INSERT INTO `role_permissions` (`role_id`, `permission_code`, `permission_name`, `resource_type`, `resource_path`, `description`, `is_active`) 
VALUES (1, 'TOPIC_STATISTICS', '题目统计', 'API', '/api/topics/statistics', '允许管理员查看题目统计', 1)
ON DUPLICATE KEY UPDATE 
    `permission_name` = VALUES(`permission_name`),
    `resource_path` = VALUES(`resource_path`),
    `description` = VALUES(`description`),
    `is_active` = 1;

-- 为普通用户添加题目统计权限
INSERT INTO `role_permissions` (`role_id`, `permission_code`, `permission_name`, `resource_type`, `resource_path`, `description`, `is_active`) 
VALUES (2, 'TOPIC_STATISTICS', '题目统计', 'API', '/api/topics/statistics', '允许普通用户查看题目统计', 1)
ON DUPLICATE KEY UPDATE 
    `permission_name` = VALUES(`permission_name`),
    `resource_path` = VALUES(`resource_path`),
    `description` = VALUES(`description`),
    `is_active` = 1;

-- 为教师添加题目统计权限
INSERT INTO `role_permissions` (`role_id`, `permission_code`, `permission_name`, `resource_type`, `resource_path`, `description`, `is_active`) 
VALUES (3, 'TOPIC_STATISTICS', '题目统计', 'API', '/api/topics/statistics', '允许教师查看题目统计', 1)
ON DUPLICATE KEY UPDATE 
    `permission_name` = VALUES(`permission_name`),
    `resource_path` = VALUES(`resource_path`),
    `description` = VALUES(`description`),
    `is_active` = 1;

-- 为管理员添加更多常用API权限
INSERT INTO `role_permissions` (`role_id`, `permission_code`, `permission_name`, `resource_type`, `resource_path`, `description`, `is_active`) VALUES
(1, 'API_ALL', '所有API访问', 'API', '/api/*', '允许管理员访问所有API接口', 1),
(1, 'TOPIC_BANK', '题库管理', 'API', '/api/topics/bank', '允许管理员访问题库管理', 1),
(1, 'TOPIC_SEARCH', '题目搜索', 'API', '/api/topics/search', '允许管理员搜索题目', 1),
(1, 'USER_CURRENT', '当前用户信息', 'API', '/api/user/current', '允许管理员获取当前用户信息', 1),
(1, 'KNOWLEDGE_POINTS', '知识点管理', 'API', '/api/knowledge-points', '允许管理员管理知识点', 1)
ON DUPLICATE KEY UPDATE 
    `permission_name` = VALUES(`permission_name`),
    `resource_path` = VALUES(`resource_path`),
    `description` = VALUES(`description`),
    `is_active` = 1;

-- 为普通用户添加常用API权限
INSERT INTO `role_permissions` (`role_id`, `permission_code`, `permission_name`, `resource_type`, `resource_path`, `description`, `is_active`) VALUES
(2, 'TOPIC_BANK', '题库浏览', 'API', '/api/topics/bank', '允许普通用户浏览题库', 1),
(2, 'TOPIC_SEARCH', '题目搜索', 'API', '/api/topics/search', '允许普通用户搜索题目', 1),
(2, 'USER_CURRENT', '当前用户信息', 'API', '/api/user/current', '允许普通用户获取当前用户信息', 1),
(2, 'KNOWLEDGE_POINTS', '知识点查看', 'API', '/api/knowledge-points', '允许普通用户查看知识点', 1)
ON DUPLICATE KEY UPDATE 
    `permission_name` = VALUES(`permission_name`),
    `resource_path` = VALUES(`resource_path`),
    `description` = VALUES(`description`),
    `is_active` = 1;

-- 为教师添加常用API权限
INSERT INTO `role_permissions` (`role_id`, `permission_code`, `permission_name`, `resource_type`, `resource_path`, `description`, `is_active`) VALUES
(3, 'TOPIC_BANK', '题库浏览', 'API', '/api/topics/bank', '允许教师浏览题库', 1),
(3, 'TOPIC_SEARCH', '题目搜索', 'API', '/api/topics/search', '允许教师搜索题目', 1),
(3, 'USER_CURRENT', '当前用户信息', 'API', '/api/user/current', '允许教师获取当前用户信息', 1),
(3, 'KNOWLEDGE_POINTS', '知识点查看', 'API', '/api/knowledge-points', '允许教师查看知识点', 1)
ON DUPLICATE KEY UPDATE 
    `permission_name` = VALUES(`permission_name`),
    `resource_path` = VALUES(`resource_path`),
    `description` = VALUES(`description`),
    `is_active` = 1;

-- 显示更新结果
SELECT 
    rp.role_id,
    CASE rp.role_id 
        WHEN 1 THEN '管理员'
        WHEN 2 THEN '普通用户'
        WHEN 3 THEN '教师'
        ELSE '未知'
    END as role_name,
    COUNT(*) as permission_count
FROM role_permissions rp 
WHERE rp.is_active = 1 
GROUP BY rp.role_id 
ORDER BY rp.role_id;

-- 显示新增的权限
SELECT 
    rp.role_id,
    CASE rp.role_id 
        WHEN 1 THEN '管理员'
        WHEN 2 THEN '普通用户'
        WHEN 3 THEN '教师'
        ELSE '未知'
    END as role_name,
    rp.permission_code,
    rp.permission_name,
    rp.resource_path
FROM role_permissions rp 
WHERE rp.permission_code IN ('TOPIC_STATISTICS', 'API_ALL', 'TOPIC_BANK', 'TOPIC_SEARCH', 'USER_CURRENT', 'KNOWLEDGE_POINTS')
    AND rp.is_active = 1
ORDER BY rp.role_id, rp.permission_code;
