<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --apple-blue: #007AFF;
            --apple-gray: #F2F2F7;
            --apple-gray-2: #E5E5EA;
            --apple-gray-3: #D1D1D6;
            --apple-text: #1D1D1F;
            --apple-text-secondary: #86868B;
            --apple-green: #34C759;
            --apple-orange: #FF9500;
            --apple-red: #FF3B30;
        }
        
        body {
            background-color: var(--apple-gray);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--apple-text);
        }
        
        .bank-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin: 20px;
            overflow: hidden;
        }
        
        .bank-header {
            background: linear-gradient(135deg, var(--apple-blue), #5AC8FA);
            color: white;
            padding: 24px;
            border-radius: 16px 16px 0 0;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--apple-gray-2);
            transition: all 0.2s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
        }
        
        .stats-label {
            color: var(--apple-text-secondary);
            font-size: 0.9rem;
            margin-top: 4px;
        }
        
        .btn-apple {
            border-radius: 8px;
            font-weight: 500;
            padding: 8px 16px;
            border: none;
            transition: all 0.2s ease;
        }
        
        .btn-apple-primary {
            background-color: var(--apple-blue);
            color: white;
        }
        
        .btn-apple-primary:hover {
            background-color: #0056CC;
            transform: translateY(-1px);
        }
        
        .form-control-apple {
            border: 1px solid var(--apple-gray-3);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 0.95rem;
            transition: all 0.2s ease;
        }
        
        .form-control-apple:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        .table-apple {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .table-apple thead th {
            background-color: var(--apple-gray);
            border: none;
            font-weight: 600;
            color: var(--apple-text);
            padding: 16px;
        }
        
        .table-apple tbody td {
            border: none;
            padding: 16px;
            border-bottom: 1px solid var(--apple-gray-2);
        }
        
        .table-apple tbody tr:hover {
            background-color: var(--apple-gray);
        }
        
        .topic-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .difficulty-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .difficulty-easy { background-color: rgba(52, 199, 89, 0.1); color: var(--apple-green); }
        .difficulty-medium { background-color: rgba(255, 149, 0, 0.1); color: var(--apple-orange); }
        .difficulty-hard { background-color: rgba(255, 59, 48, 0.1); color: var(--apple-red); }
        
        .search-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <!-- 统一导航栏 -->
    <div th:replace="fragments/unified-navbar :: unified-navbar"></div>
    
    <div class="bank-container">
        <!-- 题库管理页面头部 -->
        <div class="bank-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">题库管理</h1>
                    <p class="mb-0 opacity-75">管理和维护系统题库，确保题目质量</p>
                </div>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-light btn-apple me-2" onclick="refreshTopicList()">
                        <i class="bi bi-arrow-clockwise me-1"></i> 刷新
                    </button>
                    <button type="button" class="btn btn-light btn-apple me-2" onclick="exportTopics()">
                        <i class="bi bi-download me-1"></i> 导出
                    </button>
                    <button type="button" class="btn btn-light btn-apple" onclick="importTopics()">
                        <i class="bi bi-upload me-1"></i> 导入
                    </button>
                </div>
            </div>
        </div>
        
        <div class="p-4">
            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-collection-fill" style="font-size: 2rem; color: var(--apple-blue);"></i>
                            </div>
                            <div>
                                <div class="stats-number" style="color: var(--apple-blue);" id="totalTopics">-</div>
                                <div class="stats-label">总题目数</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-check-circle-fill" style="font-size: 2rem; color: var(--apple-green);"></i>
                            </div>
                            <div>
                                <div class="stats-number" style="color: var(--apple-green);" id="choiceTopics">-</div>
                                <div class="stats-label">选择题</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-list-check" style="font-size: 2rem; color: var(--apple-orange);"></i>
                            </div>
                            <div>
                                <div class="stats-number" style="color: var(--apple-orange);" id="fillTopics">-</div>
                                <div class="stats-label">填空题</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-chat-text" style="font-size: 2rem; color: var(--apple-red);"></i>
                            </div>
                            <div>
                                <div class="stats-number" style="color: var(--apple-red);" id="shortTopics">-</div>
                                <div class="stats-label">简答题</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-container">
                <div class="row align-items-end">
                    <div class="col-md-2">
                        <label class="form-label">题目类型</label>
                        <select class="form-select form-control-apple" id="typeFilter">
                            <option value="">全部类型</option>
                            <option value="choice">单选题</option>
                            <option value="multiple">多选题</option>
                            <option value="judge">判断题</option>
                            <option value="fill">填空题</option>
                            <option value="short">简答题</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">知识点</label>
                        <select class="form-select form-control-apple" id="knowFilter">
                            <option value="">全部知识点</option>
                            <!-- 动态加载知识点 -->
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">难度</label>
                        <select class="form-select form-control-apple" id="difficultyFilter">
                            <option value="">全部难度</option>
                            <option value="easy">简单</option>
                            <option value="medium">中等</option>
                            <option value="hard">困难</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">搜索题目</label>
                        <input type="text" class="form-control form-control-apple" id="searchKeyword" placeholder="输入题目标题或内容进行搜索...">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-apple btn-apple-primary w-100" onclick="searchTopics()">
                            <i class="bi bi-search me-1"></i> 搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 题目列表 -->
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">题目列表</h5>
                    <div class="d-flex align-items-center">
                        <span class="text-muted me-3">共 <span id="totalRecords">0</span> 条记录</span>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-apple btn-apple-primary" onclick="batchEdit()" id="batchEditBtn" disabled>
                                <i class="bi bi-pencil me-1"></i> 批量编辑
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="batchDelete()" id="batchDeleteBtn" disabled>
                                <i class="bi bi-trash me-1"></i> 批量删除
                            </button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-apple">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th>ID</th>
                                <th>题目标题</th>
                                <th>题目类型</th>
                                <th>知识点</th>
                                <th>难度</th>
                                <th>分值</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="topicTableBody">
                            <!-- 动态加载内容 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页 -->
            <nav aria-label="题目列表分页" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 动态生成分页 -->
                </ul>
            </nav>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/common.js"></script>
    <script>
        let currentPage = 1;
        let selectedTopics = new Set();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTopicList();
            loadTopicStats();
            loadKnowledgePoints();
            initEventListeners();
        });

        function initEventListeners() {
            // 全选/取消全选
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('#topicTableBody input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                    if (this.checked) {
                        selectedTopics.add(checkbox.value);
                    } else {
                        selectedTopics.delete(checkbox.value);
                    }
                });
                updateBatchButtons();
            });
        }

        function loadTopicList(page = 1) {
            // 实现题目列表加载逻辑
            console.log('Loading topic list for page:', page);
        }

        function loadTopicStats() {
            // 实现统计数据加载逻辑
            console.log('Loading topic statistics');
        }

        function loadKnowledgePoints() {
            // 实现知识点加载逻辑
            console.log('Loading knowledge points');
        }

        function updateBatchButtons() {
            const hasSelected = selectedTopics.size > 0;
            document.getElementById('batchEditBtn').disabled = !hasSelected;
            document.getElementById('batchDeleteBtn').disabled = !hasSelected;
        }

        // 其他功能函数...
        function searchTopics() {
            console.log('Searching topics');
        }

        function refreshTopicList() {
            console.log('Refreshing topic list');
        }

        function exportTopics() {
            console.log('Exporting topics');
        }

        function importTopics() {
            console.log('Importing topics');
        }
    </script>
</body>
</html>
