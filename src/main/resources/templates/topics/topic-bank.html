<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理 - Maizi EDU</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/topic-bank.css">
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">
                        <i class="bi bi-collection"></i>
                        题库管理
                    </h1>
                    <p class="page-subtitle">管理和维护题目数据库</p>
                </div>
                <div class="header-actions">
                    <button type="button" class="btn btn-primary" onclick="addTopic()">
                        <i class="bi bi-plus-circle"></i>
                        新增题目
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="card">
                <div class="card-body">
                    <form id="filterForm" class="row g-3">
                        <div class="col-md-3">
                            <label for="knowledgePointSelect" class="form-label">知识点</label>
                            <select class="form-select" id="knowledgePointSelect" name="knowledgePointId">
                                <option value="">全部知识点</option>
                                <option th:each="kp : ${knowledgePoints}" 
                                        th:value="${kp.id}" 
                                        th:text="${kp.name}">知识点名称</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="typeSelect" class="form-label">题型</label>
                            <select class="form-select" id="typeSelect" name="type">
                                <option value="">全部题型</option>
                                <option value="single">单选题</option>
                                <option value="multiple">多选题</option>
                                <option value="judge">判断题</option>
                                <option value="fill">填空题</option>
                                <option value="short">简答题</option>
                                <option value="essay">主观题</option>
                                <option value="group">组合题</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="difficultySelect" class="form-label">难度</label>
                            <select class="form-select" id="difficultySelect" name="difficulty">
                                <option value="">全部难度</option>
                                <option value="0.1-0.4">简单</option>
                                <option value="0.4-0.7">中等</option>
                                <option value="0.7-1.0">困难</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="searchInput" class="form-label">搜索题目</label>
                            <input type="text" class="form-control" id="searchInput" name="keyword" placeholder="输入题目关键词">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                    搜索
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="toolbar-section">
            <div class="d-flex justify-content-between align-items-center">
                <div class="batch-actions">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">
                            全选
                        </label>
                    </div>
                    <button type="button" class="btn btn-outline-danger btn-sm ms-2" id="batchDeleteBtn" disabled>
                        <i class="bi bi-trash"></i>
                        批量删除
                    </button>
                </div>
                <div class="result-info">
                    <span id="resultCount" class="text-muted">共 0 条记录</span>
                </div>
            </div>
        </div>

        <!-- 题目列表 -->
        <div class="table-section">
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="topicTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="50">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="tableSelectAll">
                                        </div>
                                    </th>
                                    <th width="80">ID</th>
                                    <th width="120">知识点</th>
                                    <th width="100">题型</th>
                                    <th>题目内容</th>
                                    <th width="80">难度</th>
                                    <th width="80">分值</th>
                                    <th width="120">创建时间</th>
                                    <th width="150">操作</th>
                                </tr>
                            </thead>
                            <tbody id="topicTableBody">
                                <!-- 题目数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-section">
            <nav aria-label="题目分页">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 题目详情模态框 -->
    <div class="modal fade" id="topicDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">题目详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="topicDetailContent">
                    <!-- 题目详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="editTopicBtn">编辑题目</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除选中的题目吗？此操作不可撤销。</p>
                    <div id="deleteTopicInfo"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传统计模态框 -->
    <div class="modal fade" id="uploadStatsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">上传统计</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="uploadStatsContent">
                    <!-- 统计内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="loading-text">加载中...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/topic-bank.js"></script>
</body>
</html>
