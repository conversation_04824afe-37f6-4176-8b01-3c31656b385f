<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑题目 - <PERSON>zi EDU</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.snow.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.css">
    <link rel="stylesheet" href="/static/css/topic-edit.css">
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">
                        <i class="bi bi-pencil-square"></i>
                        <span th:text="${topic != null ? '编辑题目' : '新增题目'}">编辑题目</span>
                    </h1>
                    <p class="page-subtitle">
                        <span th:if="${topic != null}" th:text="'题目ID: ' + ${topic.id}">题目ID: 123</span>
                        <span th:unless="${topic != null}">创建新的题目</span>
                    </p>
                </div>
                <div class="header-actions">
                    <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="bi bi-arrow-left"></i>
                        返回
                    </button>
                    <button type="button" class="btn btn-success" id="saveTopicBtn">
                        <i class="bi bi-check-circle"></i>
                        保存题目
                    </button>
                </div>
            </div>
        </div>

        <!-- 题目编辑表单 -->
        <div class="edit-form-section">
            <form id="topicEditForm">
                <input type="hidden" id="topicId" th:value="${topic?.id}">
                
                <div class="row g-4">
                    <!-- 左侧：基本信息 -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-info-circle"></i>
                                    基本信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- 知识点选择 -->
                                <div class="mb-3">
                                    <label for="knowledgePointSelect" class="form-label">知识点 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="knowledgePointSelect" name="knowId" required>
                                        <option value="">请选择知识点</option>
                                        <option th:each="kp : ${knowledgePoints}" 
                                                th:value="${kp.id}" 
                                                th:text="${kp.name}"
                                                th:selected="${topic != null && topic.knowId == kp.id}">知识点名称</option>
                                    </select>
                                </div>

                                <!-- 题型选择 -->
                                <div class="mb-3">
                                    <label for="topicTypeSelect" class="form-label">题型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="topicTypeSelect" name="type" required>
                                        <option value="">请选择题型</option>
                                        <option value="single" th:selected="${topic != null && topic.type == 'single'}">单选题</option>
                                        <option value="multiple" th:selected="${topic != null && topic.type == 'multiple'}">多选题</option>
                                        <option value="judge" th:selected="${topic != null && topic.type == 'judge'}">判断题</option>
                                        <option value="fill" th:selected="${topic != null && topic.type == 'fill'}">填空题</option>
                                        <option value="short" th:selected="${topic != null && topic.type == 'short'}">简答题</option>
                                        <option value="essay" th:selected="${topic != null && topic.type == 'essay'}">主观题</option>
                                        <option value="group" th:selected="${topic != null && topic.type == 'group'}">组合题</option>
                                    </select>
                                </div>

                                <!-- 难度 -->
                                <div class="mb-3">
                                    <label for="difficultyRange" class="form-label">
                                        难度 <span class="text-danger">*</span>
                                        <span class="difficulty-value" id="difficultyValue">0.5</span>
                                    </label>
                                    <input type="range" class="form-range" id="difficultyRange" name="difficulty" 
                                           min="0.1" max="1.0" step="0.1" value="0.5" 
                                           th:value="${topic?.difficulty ?: 0.5}">
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">简单</small>
                                        <small class="text-muted">中等</small>
                                        <small class="text-muted">困难</small>
                                    </div>
                                </div>

                                <!-- 分值 -->
                                <div class="mb-3">
                                    <label for="scoreInput" class="form-label">分值 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="scoreInput" name="score" 
                                           min="1" max="100" value="3" required
                                           th:value="${topic?.score ?: 3}">
                                </div>

                                <!-- 排序 -->
                                <div class="mb-3">
                                    <label for="sortInput" class="form-label">排序值</label>
                                    <input type="number" class="form-control" id="sortInput" name="sort" 
                                           min="0" max="255" value="1"
                                           th:value="${topic?.sort ?: 1}">
                                </div>

                                <!-- 标签 -->
                                <div class="mb-3">
                                    <label for="tagsInput" class="form-label">标签</label>
                                    <input type="text" class="form-control" id="tagsInput" name="tags" 
                                           placeholder="用逗号分隔多个标签"
                                           th:value="${topic?.tags}">
                                    <div class="form-text">例如：重点,难点,常考</div>
                                </div>

                                <!-- 来源 -->
                                <div class="mb-3">
                                    <label for="sourceInput" class="form-label">来源</label>
                                    <input type="text" class="form-control" id="sourceInput" name="source" 
                                           placeholder="题目来源"
                                           th:value="${topic?.source}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：题目内容 -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-file-text"></i>
                                    题目内容
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- 题干 -->
                                <div class="mb-4">
                                    <label class="form-label">题干 <span class="text-danger">*</span></label>
                                    <div class="editor-container">
                                        <div id="titleEditor" class="quill-editor"></div>
                                        <div class="editor-toolbar">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertLatex('titleEditor')">
                                                <i class="bi bi-calculator"></i> LaTeX
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertImage('titleEditor')">
                                                <i class="bi bi-image"></i> 图片
                                            </button>
                                            <span class="char-count" id="titleCharCount">0 字符</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 选项区域（根据题型显示） -->
                                <div id="optionsSection" class="mb-4" style="display: none;">
                                    <label class="form-label">选项</label>
                                    <div id="optionsContainer">
                                        <!-- 选项将通过JavaScript动态生成 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="addOptionBtn">
                                        <i class="bi bi-plus-circle"></i> 添加选项
                                    </button>
                                </div>

                                <!-- 答案 -->
                                <div class="mb-4">
                                    <label class="form-label">答案</label>
                                    <div id="answerSection">
                                        <!-- 答案输入区域将根据题型动态生成 -->
                                    </div>
                                </div>

                                <!-- 解析 -->
                                <div class="mb-4">
                                    <label class="form-label">解析</label>
                                    <div class="editor-container">
                                        <div id="parseEditor" class="quill-editor"></div>
                                        <div class="editor-toolbar">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertLatex('parseEditor')">
                                                <i class="bi bi-calculator"></i> LaTeX
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertImage('parseEditor')">
                                                <i class="bi bi-image"></i> 图片
                                            </button>
                                            <span class="char-count" id="parseCharCount">0 字符</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 组合题子题目（仅组合题显示） -->
                                <div id="subTopicsSection" class="mb-4" style="display: none;">
                                    <label class="form-label">子题目</label>
                                    <div id="subTopicsContainer">
                                        <!-- 子题目将通过JavaScript动态生成 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="addSubTopicBtn">
                                        <i class="bi bi-plus-circle"></i> 添加子题目
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- LaTeX输入模态框 -->
    <div class="modal fade" id="latexModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">插入LaTeX公式</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="latexInput" class="form-label">LaTeX代码</label>
                        <textarea class="form-control" id="latexInput" rows="4" placeholder="输入LaTeX代码，例如：x = \frac{-b \pm \sqrt{b^2-4ac}}{2a}"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">预览</label>
                        <div id="latexPreview" class="latex-preview border p-3"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="insertLatexBtn">插入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片上传模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">插入图片</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="imageFile" class="form-label">选择图片</label>
                        <input type="file" class="form-control" id="imageFile" accept="image/*">
                    </div>
                    <div class="mb-3">
                        <label for="imageUrl" class="form-label">或输入图片URL</label>
                        <input type="url" class="form-control" id="imageUrl" placeholder="https://example.com/image.jpg">
                    </div>
                    <div class="mb-3">
                        <label for="imageAlt" class="form-label">图片描述</label>
                        <input type="text" class="form-control" id="imageAlt" placeholder="图片描述">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="insertImageBtn">插入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">保存中...</span>
            </div>
            <div class="loading-text">保存中...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    <script src="/static/js/topic-edit.js"></script>
</body>
</html>
