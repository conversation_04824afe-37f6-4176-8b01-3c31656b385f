<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问被拒绝 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --apple-blue: #007AFF;
            --apple-gray: #F2F2F7;
            --apple-red: #FF3B30;
            --apple-text: #1D1D1F;
        }
        
        body {
            background: linear-gradient(135deg, var(--apple-gray), #ffffff);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--apple-text);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .error-icon {
            font-size: 5rem;
            color: var(--apple-red);
            margin-bottom: 20px;
        }
        
        .error-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--apple-text);
            margin-bottom: 15px;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #86868B;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .btn-apple {
            background-color: var(--apple-blue);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }
        
        .btn-apple:hover {
            background-color: #0056CC;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
        }
        
        .security-info {
            background: rgba(255, 59, 48, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            border-left: 4px solid var(--apple-red);
        }
        
        .security-info h6 {
            color: var(--apple-red);
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .security-info p {
            color: #86868B;
            font-size: 0.9rem;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="bi bi-shield-exclamation"></i>
        </div>
        
        <h1 class="error-title">访问被拒绝</h1>
        
        <p class="error-message">
            抱歉，您没有权限访问管理员后台。<br>
            只有具备管理员权限的用户才能访问此区域。
        </p>
        
        <div class="d-flex justify-content-center gap-3">
            <a href="/" class="btn btn-apple">
                <i class="bi bi-house me-2"></i>返回首页
            </a>
            <a href="/auth/login" class="btn btn-outline-secondary" style="border-radius: 12px; padding: 12px 24px;">
                <i class="bi bi-box-arrow-in-right me-2"></i>重新登录
            </a>
        </div>
        
        <div class="security-info">
            <h6><i class="bi bi-info-circle me-2"></i>安全提示</h6>
            <p>
                管理员后台是系统的核心管理区域，只有经过授权的管理员才能访问。
                如果您认为这是一个错误，请联系系统管理员。
            </p>
        </div>
    </div>

    <script>
        // 记录访问尝试（可选）
        console.log('未授权访问管理后台尝试:', new Date().toISOString());
        
        // 如果是从管理后台重定向过来的，显示更详细的信息
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        if (error) {
            const messageElement = document.querySelector('.error-message');
            messageElement.innerHTML = error + '<br><br>请确认您的账号具有管理员权限。';
        }
    </script>
</body>
</html>
