<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --apple-blue: #007AFF;
            --apple-gray: #F2F2F7;
            --apple-gray-2: #E5E5EA;
            --apple-text: #1D1D1F;
            --apple-text-secondary: #86868B;
            --apple-red: #FF3B30;
            --apple-green: #34C759;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--apple-text);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            padding: 50px 40px;
            width: 100%;
            max-width: 420px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--apple-blue), #5AC8FA);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.3);
        }
        
        .login-icon i {
            font-size: 2.5rem;
            color: white;
        }
        
        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--apple-text);
            margin-bottom: 8px;
        }
        
        .login-subtitle {
            color: var(--apple-text-secondary);
            font-size: 0.95rem;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            font-weight: 600;
            color: var(--apple-text);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .form-control-apple {
            border: 2px solid var(--apple-gray-2);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }
        
        .form-control-apple:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
            background: white;
        }
        
        .input-group-apple {
            position: relative;
        }
        
        .input-group-apple .form-control-apple {
            padding-right: 50px;
        }
        
        .input-group-apple .toggle-password {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--apple-text-secondary);
            cursor: pointer;
            z-index: 10;
        }
        
        .btn-apple-primary {
            background: linear-gradient(135deg, var(--apple-blue), #5AC8FA);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
        }
        
        .btn-apple-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
            color: white;
        }
        
        .btn-apple-primary:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.2);
        }
        
        .alert-apple {
            border-radius: 12px;
            border: none;
            padding: 16px 20px;
            margin-bottom: 24px;
            font-size: 0.9rem;
        }
        
        .alert-danger {
            background: rgba(255, 59, 48, 0.1);
            color: var(--apple-red);
            border-left: 4px solid var(--apple-red);
        }
        
        .alert-success {
            background: rgba(52, 199, 89, 0.1);
            color: var(--apple-green);
            border-left: 4px solid var(--apple-green);
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }
        
        .form-check-apple {
            display: flex;
            align-items: center;
        }
        
        .form-check-apple input {
            margin-right: 8px;
            transform: scale(1.1);
        }
        
        .form-check-apple label {
            font-size: 0.9rem;
            color: var(--apple-text-secondary);
            margin: 0;
        }
        
        .forgot-password {
            color: var(--apple-blue);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .forgot-password:hover {
            color: #0056CC;
            text-decoration: underline;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--apple-gray-2);
        }
        
        .login-footer a {
            color: var(--apple-blue);
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-footer a:hover {
            color: #0056CC;
            text-decoration: underline;
        }
        
        .security-notice {
            background: rgba(255, 149, 0, 0.1);
            border-left: 4px solid #FF9500;
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 20px;
            font-size: 0.85rem;
            color: var(--apple-text-secondary);
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 验证码样式 */
        .captcha-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .captcha-input {
            flex: 1;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 600;
        }

        .captcha-image-container {
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid var(--apple-gray-2);
            border-radius: 8px;
            overflow: hidden;
        }

        .captcha-image {
            width: 120px;
            height: 40px;
            cursor: pointer;
            display: block;
            border: none;
        }

        .captcha-refresh {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .captcha-image-container:hover .captcha-refresh {
            opacity: 1;
        }

        .captcha-refresh:hover {
            background: rgba(0, 0, 0, 0.9);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="bi bi-shield-check"></i>
            </div>
            <h1 class="login-title">管理员登录</h1>
            <p class="login-subtitle">麦子教育系统 - 管理后台</p>
        </div>

        <!-- 错误提示 -->
        <div id="errorAlert" class="alert alert-danger alert-apple" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <span id="errorMessage"></span>
        </div>

        <!-- 成功提示 -->
        <div id="successAlert" class="alert alert-success alert-apple" style="display: none;">
            <i class="bi bi-check-circle me-2"></i>
            <span id="successMessage"></span>
        </div>

        <form id="adminLoginForm">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="bi bi-person me-1"></i>管理员账号
                </label>
                <input type="text" class="form-control form-control-apple" id="username" name="username" 
                       placeholder="请输入管理员用户名" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="bi bi-lock me-1"></i>登录密码
                </label>
                <div class="input-group-apple">
                    <input type="password" class="form-control form-control-apple" id="password" name="password"
                           placeholder="请输入登录密码" required autocomplete="current-password">
                    <button type="button" class="toggle-password" onclick="togglePassword()">
                        <i class="bi bi-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="captchaCode" class="form-label">
                    <i class="bi bi-shield-check me-1"></i>安全验证
                </label>
                <div class="captcha-container">
                    <input type="text" class="form-control form-control-apple captcha-input" id="captchaCode" name="captchaCode"
                           placeholder="请输入验证码" required maxlength="4" autocomplete="off">
                    <div class="captcha-image-container">
                        <img id="captchaImage" src="" alt="验证码" class="captcha-image" onclick="refreshCaptcha()">
                        <button type="button" class="captcha-refresh" onclick="refreshCaptcha()" title="刷新验证码">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
                <small class="form-text text-muted">点击验证码图片可刷新</small>
            </div>

            <div class="remember-me">
                <div class="form-check-apple">
                    <input type="checkbox" id="rememberMe" name="rememberMe">
                    <label for="rememberMe">记住登录状态</label>
                </div>
                <a href="#" class="forgot-password" onclick="showForgotPassword()">忘记密码？</a>
            </div>

            <button type="submit" class="btn btn-apple-primary" id="loginBtn">
                <span class="loading-spinner" id="loadingSpinner"></span>
                <span id="loginBtnText">
                    <i class="bi bi-box-arrow-in-right me-2"></i>登录管理后台
                </span>
            </button>
        </form>

        <div class="security-notice">
            <i class="bi bi-info-circle me-2"></i>
            <strong>安全提示：</strong>此页面仅供系统管理员使用，所有登录尝试都会被记录。
        </div>

        <div class="login-footer">
            <a href="/" onclick="goToMainSite()">
                <i class="bi bi-house me-1"></i>返回主站
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentSessionId = null;

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已经登录且为管理员
            checkAdminLogin();

            // 加载验证码
            loadCaptcha();

            // 绑定表单提交事件
            document.getElementById('adminLoginForm').addEventListener('submit', handleLogin);

            // 绑定回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleLogin(e);
                }
            });
        });

        // 检查管理员登录状态
        function checkAdminLogin() {
            const token = localStorage.getItem('authToken');
            if (token) {
                fetch('/api/user/current', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200 && data.data && data.data.role === 1) {
                        // 已经是管理员登录状态，直接跳转到后台
                        showSuccess('检测到管理员登录状态，正在跳转...');
                        setTimeout(() => {
                            window.location.href = '/admin/topics/audit';
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.log('检查登录状态失败:', error);
                });
            }
        }

        // 加载验证码
        function loadCaptcha() {
            fetch('/api/captcha/generate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('captchaImage').src = data.data.captchaImage;
                        currentSessionId = data.data.sessionId;
                    } else {
                        showError('验证码加载失败');
                    }
                })
                .catch(error => {
                    console.error('加载验证码失败:', error);
                    showError('验证码加载失败');
                });
        }

        // 刷新验证码
        function refreshCaptcha() {
            const refreshData = currentSessionId ? { sessionId: currentSessionId } : {};

            fetch('/api/captcha/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(refreshData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('captchaImage').src = data.data.captchaImage;
                    currentSessionId = data.data.sessionId;
                    // 清空验证码输入框
                    document.getElementById('captchaCode').value = '';
                } else {
                    showError('验证码刷新失败');
                }
            })
            .catch(error => {
                console.error('刷新验证码失败:', error);
                showError('验证码刷新失败');
            });
        }

        // 处理登录
        function handleLogin(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const captchaCode = document.getElementById('captchaCode').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }

            if (!captchaCode) {
                showError('请输入验证码');
                return;
            }

            if (!currentSessionId) {
                showError('验证码会话无效，请刷新页面');
                return;
            }

            setLoading(true);
            hideAlerts();
            
            fetch('/api/admin/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    captchaCode: captchaCode,
                    sessionId: currentSessionId,
                    rememberMe: rememberMe
                })
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);

                if (data.success && data.data) {
                    // 管理员登录API已经验证了管理员权限
                    // 保存token
                    localStorage.setItem('authToken', data.data.token);

                    showSuccess('管理员登录成功，正在跳转到管理后台...');

                    setTimeout(() => {
                        window.location.href = '/admin/topics/audit';
                    }, 1000);
                } else {
                    showError(data.message || '登录失败，请检查用户名和密码');
                    // 登录失败时刷新验证码
                    refreshCaptcha();
                }
            })
            .catch(error => {
                setLoading(false);
                console.error('登录请求失败:', error);
                showError('网络错误，请稍后重试');
                // 网络错误时也刷新验证码
                refreshCaptcha();
            });
        }

        // 切换密码显示
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // 设置加载状态
        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const loginBtnText = document.getElementById('loginBtnText');
            
            if (loading) {
                loginBtn.disabled = true;
                loadingSpinner.style.display = 'inline-block';
                loginBtnText.innerHTML = '登录中...';
            } else {
                loginBtn.disabled = false;
                loadingSpinner.style.display = 'none';
                loginBtnText.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>登录管理后台';
            }
        }

        // 显示错误信息
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorAlert.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(hideAlerts, 3000);
        }

        // 显示成功信息
        function showSuccess(message) {
            const successAlert = document.getElementById('successAlert');
            const successMessage = document.getElementById('successMessage');
            successMessage.textContent = message;
            successAlert.style.display = 'block';
        }

        // 隐藏所有提示
        function hideAlerts() {
            document.getElementById('errorAlert').style.display = 'none';
            document.getElementById('successAlert').style.display = 'none';
        }

        // 忘记密码
        function showForgotPassword() {
            alert('请联系系统管理员重置密码');
        }

        // 返回主站
        function goToMainSite() {
            window.location.href = '/';
        }
    </script>
</body>
</html>
