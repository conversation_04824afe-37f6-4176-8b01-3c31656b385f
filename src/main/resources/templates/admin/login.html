<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0066FF;
            --primary-dark: #0052CC;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #dee2e6;
            --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --admin-gradient-alt: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--admin-gradient);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: var(--dark-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: backgroundMove 20s linear infinite;
        }

        @keyframes backgroundMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(10px, 10px); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.15),
                0 16px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            padding: 60px 50px;
            width: 100%;
            max-width: 480px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .login-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--primary-color), #5AC8FA);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.3);
        }
        
        .login-icon i {
            font-size: 3rem;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .login-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 12px;
            background: linear-gradient(135deg, var(--primary-color), #5AC8FA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-subtitle {
            color: var(--secondary-color);
            font-size: 1rem;
            font-weight: 500;
        }

        .admin-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-top: 12px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .form-group {
            margin-bottom: 28px;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 10px;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control-admin {
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 18px 24px;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            width: 100%;
            font-weight: 500;
        }

        .form-control-admin:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 102, 255, 0.1);
            background: white;
            outline: none;
            transform: translateY(-1px);
        }

        .form-control-admin::placeholder {
            color: var(--secondary-color);
            font-weight: 400;
        }
        
        .input-group-admin {
            position: relative;
        }

        .input-group-admin .form-control-admin {
            padding-right: 60px;
        }

        .input-group-admin .toggle-password {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-color);
            cursor: pointer;
            z-index: 10;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .input-group-admin .toggle-password:hover {
            background: rgba(0, 102, 255, 0.1);
            color: var(--primary-color);
        }

        .btn-admin-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            border-radius: 16px;
            padding: 18px 32px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(0, 102, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-admin-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-admin-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 102, 255, 0.4);
            color: white;
        }

        .btn-admin-primary:hover::before {
            left: 100%;
        }

        .btn-admin-primary:active {
            transform: translateY(-1px);
        }

        .btn-admin-primary:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(0, 102, 255, 0.2);
        }

        .btn-admin-primary:disabled::before {
            display: none;
        }
        
        .alert-apple {
            border-radius: 12px;
            border: none;
            padding: 16px 20px;
            margin-bottom: 24px;
            font-size: 0.9rem;
        }
        
        .alert-danger {
            background: rgba(255, 59, 48, 0.1);
            color: var(--apple-red);
            border-left: 4px solid var(--apple-red);
        }
        
        .alert-success {
            background: rgba(52, 199, 89, 0.1);
            color: var(--apple-green);
            border-left: 4px solid var(--apple-green);
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }
        
        .form-check-apple {
            display: flex;
            align-items: center;
        }
        
        .form-check-apple input {
            margin-right: 8px;
            transform: scale(1.1);
        }
        
        .form-check-apple label {
            font-size: 0.9rem;
            color: var(--apple-text-secondary);
            margin: 0;
        }
        
        .forgot-password {
            color: var(--apple-blue);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .forgot-password:hover {
            color: #0056CC;
            text-decoration: underline;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--apple-gray-2);
        }
        
        .login-footer a {
            color: var(--apple-blue);
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-footer a:hover {
            color: #0056CC;
            text-decoration: underline;
        }
        
        .security-notice {
            background: rgba(255, 149, 0, 0.1);
            border-left: 4px solid #FF9500;
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 20px;
            font-size: 0.85rem;
            color: var(--apple-text-secondary);
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 验证码样式 */
        .captcha-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .captcha-input {
            flex: 1;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 600;
        }

        .captcha-image-container {
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid var(--apple-gray-2);
            border-radius: 8px;
            overflow: hidden;
        }

        .captcha-image {
            width: 120px;
            height: 40px;
            cursor: pointer;
            display: block;
            border: none;
        }

        .captcha-refresh {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .captcha-image-container:hover .captcha-refresh {
            opacity: 1;
        }

        .captcha-refresh:hover {
            background: rgba(0, 0, 0, 0.9);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="bi bi-shield-check"></i>
            </div>
            <h1 class="login-title">管理员登录</h1>
            <p class="login-subtitle">麦子教育系统 - 管理后台</p>
            <div class="admin-badge">
                <i class="bi bi-star-fill"></i>
                <span>管理员专用通道</span>
            </div>
        </div>

        <!-- 错误提示 -->
        <div id="errorAlert" class="alert alert-danger alert-apple" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <span id="errorMessage"></span>
        </div>

        <!-- 成功提示 -->
        <div id="successAlert" class="alert alert-success alert-apple" style="display: none;">
            <i class="bi bi-check-circle me-2"></i>
            <span id="successMessage"></span>
        </div>

        <form id="adminLoginForm">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="bi bi-person me-1"></i>管理员账号
                </label>
                <input type="text" class="form-control form-control-admin" id="username" name="username"
                       placeholder="请输入管理员用户名" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="bi bi-lock me-1"></i>登录密码
                </label>
                <div class="input-group-admin">
                    <input type="password" class="form-control form-control-admin" id="password" name="password"
                           placeholder="请输入登录密码" required autocomplete="current-password">
                    <button type="button" class="toggle-password" onclick="togglePassword()">
                        <i class="bi bi-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="captchaCode" class="form-label">
                    <i class="bi bi-shield-check me-1"></i>安全验证
                </label>
                <div class="captcha-container">
                    <input type="text" class="form-control form-control-admin captcha-input" id="captchaCode" name="captchaCode"
                           placeholder="请输入验证码" required maxlength="4" autocomplete="off">
                    <div class="captcha-image-container">
                        <img id="captchaImage" src="" alt="验证码" class="captcha-image" onclick="refreshCaptcha()">
                        <button type="button" class="captcha-refresh" onclick="refreshCaptcha()" title="刷新验证码">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
                <small class="form-text text-muted">点击验证码图片可刷新</small>
            </div>

            <div class="remember-me">
                <div class="form-check-apple">
                    <input type="checkbox" id="rememberMe" name="rememberMe">
                    <label for="rememberMe">记住登录状态</label>
                </div>
                <a href="#" class="forgot-password" onclick="showForgotPassword()">忘记密码？</a>
            </div>

            <button type="submit" class="btn btn-admin-primary" id="loginBtn">
                <span class="loading-spinner" id="loadingSpinner"></span>
                <span id="loginBtnText">
                    <i class="bi bi-shield-lock me-2"></i>登录管理后台
                </span>
            </button>
        </form>

        <div class="security-notice">
            <i class="bi bi-info-circle me-2"></i>
            <strong>安全提示：</strong>此页面仅供系统管理员使用，所有登录尝试都会被记录。
        </div>

        <div class="login-footer">
            <a href="/" onclick="goToMainSite()">
                <i class="bi bi-house me-1"></i>返回主站
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentSessionId = null;

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已经登录且为管理员
            checkAdminLogin();

            // 加载验证码
            loadCaptcha();

            // 绑定表单提交事件
            document.getElementById('adminLoginForm').addEventListener('submit', handleLogin);

            // 绑定回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleLogin(e);
                }
            });
        });

        // 检查管理员登录状态
        function checkAdminLogin() {
            const token = localStorage.getItem('authToken');
            if (token) {
                fetch('/api/user/current', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200 && data.data && data.data.role === 1) {
                        // 已经是管理员登录状态，直接跳转到后台
                        showSuccess('检测到管理员登录状态，正在跳转...');
                        setTimeout(() => {
                            window.location.href = '/admin/topics/audit';
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.log('检查登录状态失败:', error);
                });
            }
        }

        // 加载验证码
        function loadCaptcha() {
            fetch('/api/captcha/generate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('captchaImage').src = data.data.captchaImage;
                        currentSessionId = data.data.sessionId;
                    } else {
                        showError('验证码加载失败');
                    }
                })
                .catch(error => {
                    console.error('加载验证码失败:', error);
                    showError('验证码加载失败');
                });
        }

        // 刷新验证码
        function refreshCaptcha() {
            const refreshData = currentSessionId ? { sessionId: currentSessionId } : {};

            fetch('/api/captcha/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(refreshData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('captchaImage').src = data.data.captchaImage;
                    currentSessionId = data.data.sessionId;
                    // 清空验证码输入框
                    document.getElementById('captchaCode').value = '';
                } else {
                    showError('验证码刷新失败');
                }
            })
            .catch(error => {
                console.error('刷新验证码失败:', error);
                showError('验证码刷新失败');
            });
        }

        // 处理登录
        function handleLogin(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const captchaCode = document.getElementById('captchaCode').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }

            if (!captchaCode) {
                showError('请输入验证码');
                return;
            }

            if (!currentSessionId) {
                showError('验证码会话无效，请刷新页面');
                return;
            }

            setLoading(true);
            hideAlerts();
            
            fetch('/api/admin/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    captchaCode: captchaCode,
                    sessionId: currentSessionId,
                    rememberMe: rememberMe
                })
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);

                if (data.success && data.data) {
                    // 管理员登录API已经验证了管理员权限
                    // 保存token
                    localStorage.setItem('authToken', data.data.token);

                    showSuccess('管理员登录成功，正在跳转到管理后台...');

                    setTimeout(() => {
                        window.location.href = '/admin/topics/audit';
                    }, 1000);
                } else {
                    showError(data.message || '登录失败，请检查用户名和密码');
                    // 登录失败时刷新验证码
                    refreshCaptcha();
                }
            })
            .catch(error => {
                setLoading(false);
                console.error('登录请求失败:', error);
                showError('网络错误，请稍后重试');
                // 网络错误时也刷新验证码
                refreshCaptcha();
            });
        }

        // 切换密码显示
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // 设置加载状态
        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const loginBtnText = document.getElementById('loginBtnText');
            
            if (loading) {
                loginBtn.disabled = true;
                loadingSpinner.style.display = 'inline-block';
                loginBtnText.innerHTML = '登录中...';
            } else {
                loginBtn.disabled = false;
                loadingSpinner.style.display = 'none';
                loginBtnText.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>登录管理后台';
            }
        }

        // 显示错误信息
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorAlert.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(hideAlerts, 3000);
        }

        // 显示成功信息
        function showSuccess(message) {
            const successAlert = document.getElementById('successAlert');
            const successMessage = document.getElementById('successMessage');
            successMessage.textContent = message;
            successAlert.style.display = 'block';
        }

        // 隐藏所有提示
        function hideAlerts() {
            document.getElementById('errorAlert').style.display = 'none';
            document.getElementById('successAlert').style.display = 'none';
        }

        // 忘记密码
        function showForgotPassword() {
            alert('请联系系统管理员重置密码');
        }

        // 返回主站
        function goToMainSite() {
            window.location.href = '/';
        }
    </script>
</body>
</html>
