2025-06-25 09:45:15.940 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 4846 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 09:45:15.942 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 09:45:15.944 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 09:45:17.034 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 09:45:17.034 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 09:45:17.034 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 09:45:17.108 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 09:45:17.452 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 09:45:17.695 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 09:45:18.181 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 09:45:18.181 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 09:45:18.181 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 09:45:18.402 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 09:45:18.408 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 09:45:18.490 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 09:45:18.501 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 09:45:18.502 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 09:45:18.895 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-25 09:45:19.144 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-06-25 09:45:19.304 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-25 09:45:19.563 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-25 09:45:19.587 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.918 seconds (JVM running for 4.321)
2025-06-25 09:45:19.597 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-06-25 09:45:19.599 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-06-25 09:45:19.599 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - user_id: bigint
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty_distribution: json
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - paper_type: varchar
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - file_format: varchar
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - download_count: int
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - last_download_time: datetime
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-06-25 09:45:19.600 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: tinyint
2025-06-25 09:45:19.603 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.5, difficulty_distribution=[{"easy": 0.2, "hard": 0.1, "medium": 0.7}], content=103813,103825,103826,103831,103837,103839,103856,103863,103868,103872,103881,103887,103893,103905,103940,103946,103955,103958,103965,103979,104003,104021,104029,104036,104057,104066,104073,104090,104115,104128,104152,104156,104159,104182,104186,104197,104203,104207,104212,104218,104234,104252,104258,104262,104890,104897,104902,104918,104921,104935,104939,104949,104957,104962,104970,104976,104986,104990,104993,105003,105017,105021,105025,105048,105056,105071,105155,105164,105195,105200,105229,105234,105255,105257,105275,105279,105288,105292,105296,105299,105306,105317,106864,106875,107046,107050,107102,107110,107173,107178,107234,107384,107540,107542,107550,107583,107592,107607,107611,107615,107640,107652,107657,107680,107682,107688,107690,107691,107692,107693,107696,107700,107701,107703,107709,107713,107719,107722,107729,107739,107741,107744,107749,107755,107768,107769,107777,107781,107782,107784,107787,107788,107789,107790,107791,107793,107795,107798,107804,107808,107812,107820,107822,107824,107827,107829,107831,107836,107838,107847,107850,107852,107856,107864,107876,107881,107888,107942,107945,108015,108077,108121,108128,108181,108205,108364,108384,108385,108387,108389,108417,108445,108475,108478,108481,108496,108497,108528,108534,108582,108590,108599,108782,108786,108819,108837,108845,108879,109020,109034,109036,109050,109058,109078,109086,109220,109226,109227,109230,109234,109238,109251,109257,109276,109300,109314,109343,109346,109348,109419,109541,109543,109545,109576,109584,109592,109649,109650,109657,109697,109703,109705,109714,109718,109721,109771,109959,110000,110009,110175,110178,110189,110292,110299,110489,110494,110513,110738,110743,110751,110754,110770,110777,110784,110786,110788,110791,110806,110930,110977,111018,111025,111032,111053,111242,111335,111354,111445,111463,111475,111499,111504,111561,111563,111564,111566,111567,111571,111598,111601,111603,111604,111605,111628,111632,111634,111642,111648,111649,111655,111658,111659,111662,111691,111693,111694,111695,111702,111717,111731,111735,111743,111751,111782,111798,111804,111845,112003,112005,112009,112029,112035,112044,112047,112065,112184,112192,112194,112199,112271,112306,112317,112329,112333,112334,112336,112360,112420,112428,112438,112440,112517,112524,112526,112529,112530,112532,112533,112548,112574,112613,112616,112618,112621,112623,112625,112627,112630,112635,112639,112672,112830,112916,112943,113353,113374,113390,113413,113419,113426,113430,113435,113452,113454,113482,113506,113523,113526,113555,113560,113563,113565,113566,113572,113573,113653,113735,113796,113883,113925,113943,113951,113959,113964,113977,113981,113984,113992,114002,114010,114024,114030,114037,114119,114124,114135,114156,114173,115066,115177,115184,115191,115195,115209,115215,115219,115240,115245,115248,115257,115265,115303,115309,115336,115366,115442,115451,115567,115586,115617,115619,115620,115650,115660,115679,115684,115693,115707,115721,115734,115740,115745,115759,115806,115814,115820,115927,115947,115950,115955,116076,116127,116131,116268,116283,116340,116436,116622,116629,116704,116705,116708,116722,116723,116991,117004,117011,117014,117022,117230,117354,117445,117476,117486,117491,117503,117510,117672,117685,117694,117777,117845,117977,117978,118080,118119,118120,118195,118196,118198,118202,118242,118255,119854,119855,119856,119861,119862,119864,119865,123492,123783,123875,123986,131453,131455,131456,131457,131494,131498,131507,131509,131512,131513,131527,131531,131533,131535,131537,131539,131540,131541,131543,131544,131547,131553,131557,131558,131565,131567,131571,131576,131578,131579,131580,131594,131600,131602,131606,131610,131612,131616,131621,131628,131630,131634,131639,131643,131648,131649,131652,131717,131719,131725,131727,131734,131736,131739,131744,131746,131749,131750,131760,131762,131769,131770,131774,131775,131778,131779,131785,131787,131788,131790,131791,131792,131793,131794,131796,131797,131798,131800,131802,131805,131806,131807,131808,131812,131814,131816,131817,131818,131819,131820,131821,131822,131823,131825,131829,160715, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:40:57, update_time=2025-05-20T11:29:46, is_deleted=false}, {id=2, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.6999999999999966, difficulty_distribution=[{"easy": 0.2, "hard": 0.1, "medium": 0.7}], content=103797,103802,103813,103825,103826,103831,103834,103839,103856,103868,103893,103902,103905,103915,103921,103929,103936,103958,103965,103972,103984,103989,103997,104003,104016,104021,104029,104045,104050,104106,104115,104138,104156,104159,104188,104203,104207,104212,104215,104218,104224,104233,104234,104252,104262,104286,104890,104893,104897,104918,104925,104931,104943,104957,104962,104990,104998,105003,105021,105048,105051,105061,105071,105088,105102,105112,105116,105155,105161,105167,105171,105181,105190,105195,105207,105210,105216,105229,105234,105244,105248,105255,105257,105279,105285,105288,105296,105299,105306,105315,105317,106862,106864,106874,107004,107015,107050,107163,107165,107168,107173,107186,107222,107234,107264,107268,107401,107540,107542,107583,107587,107592,107611,107615,107618,107621,107622,107636,107643,107652,107657,107682,107684,107686,107690,107691,107693,107696,107700,107701,107709,107717,107725,107739,107744,107749,107753,107758,107761,107764,107777,107779,107781,107782,107786,107788,107790,107791,107792,107793,107794,107795,107801,107802,107804,107805,107808,107809,107810,107812,107814,107815,107816,107817,107824,107825,107829,107832,107834,107836,107850,107851,107852,107870,107872,107882,107888,107929,107942,107993,108012,108082,108131,108200,108364,108382,108384,108385,108389,108411,108417,108421,108440,108442,108445,108475,108509,108534,108569,108585,108590,108819,108837,108845,108879,108940,109022,109030,109034,109036,109050,109074,109082,109152,109220,109226,109227,109234,109314,109343,109417,109419,109543,109545,109579,109592,109649,109650,109697,109703,109704,109705,109708,109714,109718,109721,109731,109767,109769,109771,110009,110016,110168,110172,110175,110268,110292,110471,110489,110494,110502,110509,110525,110562,110576,110724,110731,110734,110738,110751,110756,110762,110777,110784,110791,110977,111018,111025,111032,111053,111061,111230,111249,111335,111350,111357,111449,111463,111465,111475,111485,111488,111489,111504,111514,111561,111563,111564,111566,111567,111571,111572,111603,111604,111619,111627,111628,111632,111633,111635,111638,111642,111648,111649,111655,111657,111663,111692,111694,111695,111697,111720,111731,111751,111757,111798,111822,111858,112003,112009,112029,112035,112044,112047,112192,112212,112303,112310,112321,112325,112329,112336,112349,112360,112420,112517,112524,112529,112530,112532,112554,112574,112595,112606,112613,112616,112621,112625,112627,112628,112635,112830,112831,113331,113347,113353,113368,113374,113381,113401,113419,113422,113430,113433,113439,113483,113523,113524,113544,113552,113554,113560,113563,113568,113572,113655,113747,113796,113802,113883,113925,113929,113935,113943,113951,113959,113975,113977,113981,113985,114010,114024,114030,114037,114119,114124,114135,114173,115006,115033,115056,115062,115064,115066,115177,115191,115202,115209,115219,115240,115248,115257,115303,115309,115336,115352,115366,115447,115567,115570,115620,115650,115668,115679,115688,115693,115707,115723,115730,115740,115744,115745,115753,115772,115806,115814,115820,115923,115927,115938,115947,115955,115968,116073,116076,116127,116131,116335,116340,116345,116442,116721,116722,116991,116996,116999,117004,117007,117014,117019,117027,117033,117199,117230,117239,117484,117491,117503,117507,117510,117672,117685,117693,117694,117777,117842,117845,117856,117977,117981,118120,118129,118185,118193,118195,118196,118197,118200,118201,118202,118242,118251,118254,119853,119856,119858,119862,119864,119865,123783,123802,123875,123986,131454,131455,131456,131492,131498,131501,131505,131507,131510,131513,131527,131531,131532,131533,131534,131535,131540,131541,131543,131547,131552,131555,131560,131562,131569,131573,131578,131584,131590,131592,131598,131602,131604,131606,131616,131617,131624,131628,131630,131637,131643,131645,131646,131647,131648,131652,131727,131728,131734,131737,131739,131749,131750,131754,131760,131762,131763,131768,131769,131785,131791,131795,131798,131806,131809,131814,131817,131818,131823,131824,131825,131827,131828,131830,131831,133520,160715, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:44:07, update_time=2025-05-20T11:29:46, is_deleted=false}, {id=3, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.5, difficulty_distribution=[{"easy": 0.2, "hard": 0.1, "medium": 0.7}], content=126658,126668,126680,126686,126714,126718,126873,126899,126949,126953,126957,126960,126971,126975,126976,126981,126985,126989,126996,126998,127009,127094,127124,127132,127139,127178,127193,127197,127310,127316,127467,127479,127534, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:53:09, update_time=2025-05-20T11:29:46, is_deleted=false}, {id=4, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=105, actual_total_score=null, difficulty=0.7, difficulty_distribution=null, content=126658,126663,126723,126818,126853,126882,126890,126950,126952,126953,126963,126964,126968,126975,126980,126981,126986,127001,127007,127011,127049,127069,127083,127138,127142,127151,127153,127155,127161,127170,127182,127313,127457,127473,127505, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:57:20, update_time=2025-05-16T21:57:20, is_deleted=true}, {id=5, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.6928571428571428, difficulty_distribution=[{"easy": 0.2, "hard": 0.1, "medium": 0.7}], content=126641,126680,126718,126728,126762,126840,126845,126873,126890,126907,126914,126934,126959,126968,126975,126976,126984,126985,126991,127001,127011,127083,127111,127139,127148,127153,127157,127166,127174,127178,127501,127513,127532, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:59:54, update_time=2025-05-20T11:29:46, is_deleted=false}]
2025-06-25 09:45:19.639 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 86
2025-06-25 09:45:48.508 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 32.70745560653298%, pool size: 50
2025-06-25 09:46:18.503 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:46:48.503 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:47:18.504 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:47:48.503 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:48:18.503 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:48:48.503 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:49:18.503 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:49:48.503 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:50:18.505 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.095800306925874%, pool size: 50
2025-06-25 09:50:48.266 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 09:50:48.269 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 09:50:48.272 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 10:24:41.091 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 7183 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 10:24:41.093 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 10:24:41.093 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 10:24:42.066 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 10:24:42.066 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 10:24:42.066 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 10:24:42.135 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 10:24:42.431 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 10:24:42.644 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 10:24:43.054 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 10:24:43.054 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 10:24:43.054 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 10:24:43.197 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 10:24:43.201 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 10:24:43.202 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-25 10:25:08.974 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 7205 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 10:25:08.975 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 10:25:08.976 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 10:25:10.008 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 10:25:10.009 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 10:25:10.009 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 10:25:10.077 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 10:25:10.396 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 10:25:10.610 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 10:25:11.064 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 10:25:11.065 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 10:25:11.065 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 10:25:11.067 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 10:25:11.076 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 10:25:11.146 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 10:25:11.158 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 10:25:11.160 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 10:25:11.617 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 10:25:11.618 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 10:25:11.622 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 10:25:11.624 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-25 10:26:08.268 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 7253 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 10:26:08.269 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 10:26:08.270 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 10:26:09.251 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 10:26:09.251 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 10:26:09.251 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 10:26:09.318 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 10:26:09.639 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 10:26:09.853 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 10:26:10.279 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 10:26:10.280 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 10:26:10.280 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 10:26:10.282 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 10:26:10.290 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 10:26:10.349 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 10:26:10.360 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 10:26:10.362 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 10:26:10.761 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 10:26:10.762 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 10:26:10.765 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 10:26:10.767 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-25 10:29:19.853 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 7350 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 10:29:19.855 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 10:29:19.855 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 10:29:20.867 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 10:29:20.867 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 10:29:20.867 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 10:29:20.939 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 10:29:21.247 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 10:29:21.463 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 10:29:21.889 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 10:29:21.889 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 10:29:21.889 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 10:29:21.891 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 10:29:21.899 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 10:29:21.957 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 10:29:21.968 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 10:29:21.971 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 10:29:22.376 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-25 10:29:22.377 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-25 10:29:22.377 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-25 10:29:22.377 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-25 10:29:22.530 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-25 10:29:22.811 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-06-25 10:29:22.958 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-25 10:29:23.198 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-25 10:29:23.221 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.628 seconds (JVM running for 4.012)
2025-06-25 10:29:29.396 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 10:29:29.414 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-25 10:29:30.088 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/info, Token: exists
2025-06-25 10:29:30.090 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:30.103 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/page, Token: null
2025-06-25 10:29:30.103 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/page
2025-06-25 10:29:30.147 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-06-19T03:19:13Z. Current time: 2025-06-25T02:29:30Z, a difference of 515417146 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc1MDIxNjc1MywiZXhwIjoxNzUwMzAzMTUzfQ.PQPdqjKhp0OIjRlDy53KiNI8LUplvqheUXKma28V62GMp9rFw6_S_Cf3LuIj_3_2rLvN7zyiDNd2KU-9Ndojlw]
2025-06-25 10:29:30.147 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-06-25 10:29:30.858 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/statistics, Token: null
2025-06-25 10:29:30.859 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/statistics
2025-06-25 10:29:31.455 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-25 10:29:33.366 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 10:29:33.366 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:33.367 [http-nio-8081-exec-10] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-06-19T03:19:13Z. Current time: 2025-06-25T02:29:33Z, a difference of 515420367 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc1MDIxNjc1MywiZXhwIjoxNzUwMzAzMTUzfQ.PQPdqjKhp0OIjRlDy53KiNI8LUplvqheUXKma28V62GMp9rFw6_S_Cf3LuIj_3_2rLvN7zyiDNd2KU-9Ndojlw]
2025-06-25 10:29:33.368 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/current
2025-06-25 10:29:35.976 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-06-25 10:29:35.976 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Login attempt from IP: 0:0:0:0:0:0:0:1, username: admin
2025-06-25 10:29:36.097 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: admin with secret (first 5): 'F9A8C...'
2025-06-25 10:29:37.127 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-25 10:29:37.204 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/info, Token: exists
2025-06-25 10:29:37.204 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:37.208 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/page, Token: exists
2025-06-25 10:29:37.209 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:37.224 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/info called (redirecting to /current)
2025-06-25 10:29:37.225 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 10:29:37.225 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 10:29:37.225 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:37.227 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:37.228 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 10:29:37.235 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 10:29:37.235 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 10:29:37.276 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:29:37.278 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.AvatarsController - 请求头像文件: 20250508090939_f9f90b54.jpg
2025-06-25 10:29:37.278 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.controller.AvatarsController - 头像文件不存在: 20250508090939_f9f90b54.jpg，返回默认头像
2025-06-25 10:29:37.320 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/statistics, Token: exists
2025-06-25 10:29:37.320 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:37.483 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-25 10:29:40.460 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 10:29:40.460 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:40.464 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 10:29:40.464 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 10:29:40.464 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:40.467 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:29:40.469 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 10:29:40.475 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 10:29:40.476 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 10:29:40.531 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:29:40.543 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:29:51.977 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 14.133211186057643%, pool size: 50
2025-06-25 10:30:12.086 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 10:30:12.086 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:30:12.087 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 10:30:12.087 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 10:30:12.087 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:30:12.088 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:30:12.089 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 10:30:12.092 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 10:30:12.092 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 10:30:12.196 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:30:12.595 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-25 10:30:12.595 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 10:30:12.595 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:30:12.595 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:30:12.598 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-25 10:30:12.611 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 10:30:21.973 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:30:51.975 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:31:21.976 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:31:51.974 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:32:21.978 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:32:51.978 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:33:21.975 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:33:51.976 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:34:21.975 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.782256176597194%, pool size: 50
2025-06-25 10:34:51.976 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 17.082759581114114%, pool size: 50
2025-06-25 10:35:21.977 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 17.082759581114114%, pool size: 50
2025-06-25 10:35:51.979 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 17.082759581114114%, pool size: 50
2025-06-25 10:36:21.976 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 17.082759581114114%, pool size: 50
2025-06-25 10:36:38.215 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 10:36:38.215 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:38.218 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 10:36:38.219 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 10:36:38.219 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:38.221 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:38.223 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 10:36:38.228 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 10:36:38.228 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 10:36:38.408 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:36:38.729 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-25 10:36:38.729 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:38.729 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 10:36:38.729 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:38.732 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-25 10:36:38.732 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 10:36:42.358 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 10:36:42.358 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:42.359 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 10:36:42.359 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 10:36:42.359 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:42.359 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:42.360 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 10:36:42.363 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 10:36:42.363 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 10:36:42.433 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:36:45.126 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 10:36:45.126 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:45.127 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 10:36:45.127 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 10:36:45.127 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:45.128 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:45.128 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 10:36:45.131 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 10:36:45.131 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 10:36:45.207 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:36:45.649 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-25 10:36:45.649 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 10:36:45.649 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:45.649 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:45.651 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-25 10:36:45.651 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 10:36:51.866 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/download/84, Token: exists
2025-06-25 10:36:51.979 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 22.631466388702393%, pool size: 50
2025-06-25 10:36:52.029 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-25 10:36:52.029 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:52.030 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:36:52.032 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-25 10:36:52.058 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - 成功记录试卷下载: paperId=84, userId=1920280447393230850, format=pdf, ip=0:0:0:0:0:0:0:1
2025-06-25 10:37:21.981 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 23.97006310914692%, pool size: 50
2025-06-25 10:37:51.981 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 23.97006310914692%, pool size: 50
2025-06-25 10:38:21.980 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:38:51.983 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:39:21.982 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:39:51.982 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:40:21.979 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:40:51.979 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:41:21.980 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:41:51.982 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:42:21.985 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.27055835723877%, pool size: 50
2025-06-25 10:42:27.386 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 10:42:27.386 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:27.389 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 10:42:27.389 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 10:42:27.389 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:27.391 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:27.391 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 10:42:27.397 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 10:42:27.397 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 10:42:27.657 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 10:42:27.897 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 10:42:27.897 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-25 10:42:27.897 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:27.897 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:27.900 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-25 10:42:27.900 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 10:42:37.443 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/search, Token: exists
2025-06-25 10:42:37.443 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:37.456 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 搜索知识点: keyword=信息, groupName=null, isFree=null
2025-06-25 10:42:37.478 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 搜索到 1 个知识点
2025-06-25 10:42:45.446 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:42:45.449 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:45.474 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60}
2025-06-25 10:42:45.540 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.541 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.541 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.541 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.541 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.541 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.542 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.542 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.542 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.542 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.542 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.543 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.543 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.543 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.543 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.543 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.544 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.544 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:45.544 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.648 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:42:47.648 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:47.653 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60}
2025-06-25 10:42:47.711 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.711 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.711 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.711 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.711 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.711 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.712 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.712 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.712 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.712 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.712 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.713 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.713 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.713 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.713 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.713 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.713 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.713 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:47.714 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.430 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:42:49.431 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:49.437 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=29}
2025-06-25 10:42:49.494 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.494 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.494 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.494 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.494 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.494 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.494 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.495 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.495 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.495 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.495 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.495 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.496 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.496 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.496 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.496 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.496 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.496 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:49.497 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.427 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:42:51.428 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:51.433 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20}
2025-06-25 10:42:51.483 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.483 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.483 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.483 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.483 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.484 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.484 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.484 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.484 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.484 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.484 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.485 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.485 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.485 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.485 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.485 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.485 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.486 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.486 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:51.983 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 30.43589717463443%, pool size: 50
2025-06-25 10:42:55.747 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:42:55.747 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:55.753 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-25 10:42:55.802 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.802 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.802 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.803 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.804 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.804 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.804 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.804 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.804 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.804 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.805 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:55.805 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.385 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:42:57.386 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:57.391 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-25 10:42:57.442 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.443 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.443 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.443 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.443 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.443 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.443 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.444 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.444 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.444 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.444 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.444 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.445 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.445 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.445 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.445 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.445 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.445 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:57.445 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.838 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:42:58.838 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:42:58.843 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-25 10:42:58.892 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.892 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.892 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.892 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.892 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.893 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.893 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.893 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.893 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.893 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.894 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.894 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.894 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.894 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.894 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.894 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.895 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.895 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:42:58.895 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.927 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:43:06.928 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:06.933 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-25 10:43:06.991 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.991 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.991 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.991 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.992 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.992 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.992 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.992 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.992 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.992 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.992 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:06.993 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.172 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:43:08.172 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:08.176 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-25 10:43:08.219 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.219 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.219 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.219 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.220 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.221 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.221 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.221 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.221 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.221 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:08.221 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.326 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-06-25 10:43:14.326 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:14.329 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-25 10:43:14.371 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.371 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.372 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:14.373 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:21.986 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 37.35065021012959%, pool size: 50
2025-06-25 10:43:27.180 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/generate-batch, Token: exists
2025-06-25 10:43:27.180 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:27.196 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate 2 papers with title: 信息技术2测试试卷
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.242 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.243 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.243 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.243 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.243 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.243 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.245 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.246 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.246 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.246 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:27.819 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 10:43:27.820 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:27.822 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 10:43:28.659 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm completed successfully in 1384ms with fitness {:.3f}
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.948 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:28.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-06-25 10:43:30.140 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm completed successfully in 1183ms with fitness {:.3f}
2025-06-25 10:43:31.704 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 10:43:31.704 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:31.709 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 10:43:35.992 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/download/88, Token: exists
2025-06-25 10:43:36.039 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '单选题', 返回默认单选题
2025-06-25 10:43:36.129 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '多选题', 返回默认单选题
2025-06-25 10:43:36.143 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '判断题', 返回默认单选题
2025-06-25 10:43:36.211 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-25 10:43:36.211 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:36.211 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:36.214 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-25 10:43:36.216 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - 成功记录试卷下载: paperId=88, userId=1920280447393230850, format=pdf, ip=0:0:0:0:0:0:0:1
2025-06-25 10:43:38.670 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/download/87, Token: exists
2025-06-25 10:43:38.727 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '单选题', 返回默认单选题
2025-06-25 10:43:38.779 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '多选题', 返回默认单选题
2025-06-25 10:43:38.789 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '判断题', 返回默认单选题
2025-06-25 10:43:38.817 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-25 10:43:38.817 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:38.818 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:43:38.820 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-25 10:43:38.823 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - 成功记录试卷下载: paperId=87, userId=1920280447393230850, format=pdf, ip=0:0:0:0:0:0:0:1
2025-06-25 10:43:51.986 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 18.086533894202912%, pool size: 50
2025-06-25 10:44:21.986 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 18.442072732781597%, pool size: 50
2025-06-25 10:44:51.986 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.199983009743896%, pool size: 50
2025-06-25 10:45:21.987 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.236671626936225%, pool size: 50
2025-06-25 10:45:51.987 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.273360244128554%, pool size: 50
2025-06-25 10:46:21.988 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.34673747851321%, pool size: 50
2025-06-25 10:46:51.988 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.383426095705538%, pool size: 50
2025-06-25 10:47:21.988 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.420114712897867%, pool size: 50
2025-06-25 10:47:51.985 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.456803330090196%, pool size: 50
2025-06-25 10:48:21.989 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.493491947282525%, pool size: 50
2025-06-25 10:48:51.989 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.566869181667183%, pool size: 50
2025-06-25 10:49:21.989 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 19.603557798859512%, pool size: 50
2025-06-25 10:49:46.546 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /papers/download/87, Token: exists
2025-06-25 10:49:46.592 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '单选题', 返回默认单选题
2025-06-25 10:49:46.606 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '多选题', 返回默认单选题
2025-06-25 10:49:46.610 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: '判断题', 返回默认单选题
2025-06-25 10:49:46.631 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-25 10:49:46.631 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 10:49:46.632 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 10:49:46.635 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-25 10:49:46.639 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - 成功记录试卷下载: paperId=87, userId=1920280447393230850, format=pdf, ip=0:0:0:0:0:0:0:1
2025-06-25 10:49:51.987 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.720217182550797%, pool size: 50
2025-06-25 10:50:21.986 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.756905799743123%, pool size: 50
2025-06-25 10:50:51.989 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.01312708530497%, pool size: 50
2025-06-25 10:51:21.990 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.0498157024973%, pool size: 50
2025-06-25 10:51:51.990 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.086504319689624%, pool size: 50
2025-06-25 10:52:21.991 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.123192936881956%, pool size: 50
2025-06-25 10:52:51.991 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.159881554074282%, pool size: 50
2025-06-25 10:53:21.991 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.23325878845894%, pool size: 50
2025-06-25 10:53:51.993 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.269947405651266%, pool size: 50
2025-06-25 10:54:21.994 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.3066360228436%, pool size: 50
2025-06-25 10:54:51.992 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.343324640035924%, pool size: 50
2025-06-25 10:55:21.993 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.416701874420582%, pool size: 50
2025-06-25 10:55:51.990 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.45339049161291%, pool size: 50
2025-06-25 10:56:21.992 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.490079108805237%, pool size: 50
2025-06-25 10:56:51.995 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.52676772599757%, pool size: 50
2025-06-25 10:57:21.992 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.60014496038222%, pool size: 50
2025-06-25 10:57:51.993 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.636833577574553%, pool size: 50
2025-06-25 10:58:21.987 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.67352219476688%, pool size: 50
2025-06-25 10:58:51.986 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 26.40828249333668%, pool size: 50
2025-06-25 10:59:21.986 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 26.965884667244477%, pool size: 50
2025-06-25 10:59:51.989 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.429911723095643%, pool size: 50
2025-06-25 11:00:21.987 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.46660034028797%, pool size: 50
2025-06-25 11:00:51.988 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.5032889574803%, pool size: 50
2025-06-25 11:01:05.827 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 11:01:05.828 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 11:01:05.833 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 11:01:53.843 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 8828 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 11:01:53.844 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 11:01:53.845 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 11:01:54.880 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 11:01:54.881 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 11:01:54.881 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 11:01:54.952 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 11:01:55.309 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 11:01:55.558 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 11:01:56.009 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 11:01:56.009 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 11:01:56.009 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 11:01:56.011 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 11:01:56.020 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 11:01:56.082 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 11:01:56.094 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 11:01:56.096 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 11:01:56.518 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 11:01:56.519 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 11:01:56.522 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 11:01:56.524 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-25 11:02:36.644 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 8860 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 11:02:36.645 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 11:02:36.646 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 11:02:37.609 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 11:02:37.609 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 11:02:37.609 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 11:02:37.680 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 11:02:37.984 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 11:02:38.208 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 11:02:38.617 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 11:02:38.617 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 11:02:38.617 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 11:02:38.619 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 11:02:38.627 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 11:02:38.686 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 11:02:38.696 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 11:02:38.698 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 11:02:39.131 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 11:02:39.133 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 11:02:39.136 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 11:02:39.138 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-25 11:02:50.977 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 8868 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 11:02:50.979 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 11:02:50.979 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 11:02:52.070 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 11:02:52.070 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 11:02:52.070 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 11:02:52.153 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 11:02:52.503 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 11:02:52.762 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 11:02:53.237 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 11:02:53.238 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 11:02:53.238 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 11:02:53.240 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 11:02:53.249 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 11:02:53.323 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 11:02:53.334 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 11:02:53.336 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 11:02:53.811 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 11:02:53.813 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 11:02:53.816 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 11:02:53.819 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-25 11:04:05.871 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 8916 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 11:04:05.872 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 11:04:05.873 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 11:04:06.893 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 11:04:06.893 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 11:04:06.893 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 11:04:06.966 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 11:04:07.286 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 11:04:07.500 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 11:04:07.905 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 11:04:07.906 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 11:04:07.906 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 11:04:07.908 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 11:04:07.915 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 11:04:07.974 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 11:04:07.985 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 11:04:07.987 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 11:04:08.534 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-25 11:04:08.796 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-06-25 11:04:08.954 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-25 11:04:09.206 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-25 11:04:09.231 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.625 seconds (JVM running for 4.007)
2025-06-25 11:04:22.554 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 11:04:22.902 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 11:04:22.902 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:04:23.054 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 11:04:23.055 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 11:04:23.055 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:04:23.055 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 11:04:23.056 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 11:04:23.125 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 11:04:23.125 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 11:04:23.192 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 11:04:23.412 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 11:04:23.412 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-25 11:04:23.412 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:04:23.412 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:04:23.416 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-25 11:04:23.426 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 11:04:37.993 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 13.354665670773787%, pool size: 50
2025-06-25 11:05:07.992 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 13.715824310999247%, pool size: 50
2025-06-25 11:05:37.992 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 14.004753286921162%, pool size: 50
2025-06-25 11:06:07.992 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 14.004753286921162%, pool size: 50
2025-06-25 11:06:37.989 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 14.004753286921162%, pool size: 50
2025-06-25 11:06:59.634 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-25 11:06:59.636 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-25 11:06:59.639 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-25 11:07:01.011 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9039 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-25 11:07:01.012 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-25 11:07:01.013 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-25 11:07:01.991 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-25 11:07:01.991 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 11:07:01.991 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 11:07:02.059 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 11:07:02.376 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 11:07:02.582 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 11:07:02.995 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-25 11:07:02.995 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-25 11:07:02.995 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-25 11:07:02.997 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-25 11:07:03.006 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-06-25 11:07:03.067 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-25 11:07:03.078 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-25 11:07:03.080 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-25 11:07:03.618 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-25 11:07:03.898 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-06-25 11:07:04.032 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-25 11:07:04.265 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-25 11:07:04.288 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.544 seconds (JVM running for 3.931)
2025-06-25 11:07:33.086 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.159357816545697%, pool size: 50
2025-06-25 11:08:03.086 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.55116101832787%, pool size: 50
2025-06-25 11:08:33.082 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.55116101832787%, pool size: 50
2025-06-25 11:09:03.082 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.55116101832787%, pool size: 50
2025-06-25 11:09:33.086 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.55116101832787%, pool size: 50
2025-06-25 11:10:03.083 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.55116101832787%, pool size: 50
2025-06-25 11:10:33.085 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.55116101832787%, pool size: 50
2025-06-25 11:11:03.083 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.55116101832787%, pool size: 50
2025-06-25 11:11:29.937 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 11:11:33.088 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 33.43374320408908%, pool size: 50
2025-06-25 11:11:37.076 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-25 11:11:37.077 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:11:37.111 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-25 11:11:37.112 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-25 11:11:37.112 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:11:37.112 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-25 11:11:37.113 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-25 11:11:37.179 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-25 11:11:37.179 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-25 11:11:37.280 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-25 11:11:37.602 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-25 11:11:37.603 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:11:37.604 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-06-25 11:11:37.605 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-25 11:11:37.607 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-25 11:11:37.612 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-25 11:12:03.084 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 13.765678103890632%, pool size: 50
2025-06-25 11:12:33.085 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 14.728379954600623%, pool size: 50
2025-06-25 11:13:03.088 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 15.320804823144979%, pool size: 50
2025-06-25 11:13:33.089 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 15.320804823144979%, pool size: 50
2025-06-25 11:14:03.089 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 15.320804823144979%, pool size: 50
2025-06-25 11:14:33.086 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 15.320804823144979%, pool size: 50
2025-06-25 11:15:03.088 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 15.320804823144979%, pool size: 50
2025-06-25 11:15:33.091 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 15.765146353204068%, pool size: 50
2025-06-25 11:16:03.091 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.061358151958167%, pool size: 50
2025-06-25 11:16:33.087 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.061358151958167%, pool size: 50
2025-06-25 11:17:03.091 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.061358151958167%, pool size: 50
2025-06-25 11:17:33.087 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.061358151958167%, pool size: 50
2025-06-25 11:18:03.088 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 16.5056837940653%, pool size: 50
2025-06-25 11:18:33.092 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 17.431482380998478%, pool size: 50
2025-06-25 11:19:03.093 [pool-2-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 17.431482380998478%, pool size: 50
