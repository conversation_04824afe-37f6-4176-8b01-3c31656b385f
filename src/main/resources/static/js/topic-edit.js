// 题目编辑JavaScript
class TopicEditor {
    constructor() {
        this.titleEditor = null;
        this.parseEditor = null;
        this.currentTopicType = '';
        this.options = [];
        this.subTopics = [];
        this.currentEditorTarget = '';
        this.init();
    }

    init() {
        this.initEditors();
        this.bindEvents();
        this.loadTopicData();
    }

    // 初始化富文本编辑器
    initEditors() {
        // 题干编辑器
        this.titleEditor = new Quill('#titleEditor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    ['bold', 'italic', 'underline', 'strike'],
                    ['blockquote', 'code-block'],
                    [{ 'header': 1 }, { 'header': 2 }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'script': 'sub'}, { 'script': 'super' }],
                    [{ 'indent': '-1'}, { 'indent': '+1' }],
                    ['link', 'formula'],
                    ['clean']
                ]
            },
            placeholder: '请输入题目内容...'
        });

        // 解析编辑器
        this.parseEditor = new Quill('#parseEditor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    ['bold', 'italic', 'underline'],
                    ['blockquote', 'code-block'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['link', 'formula'],
                    ['clean']
                ]
            },
            placeholder: '请输入答案解析...'
        });

        // 监听编辑器内容变化，更新字符计数
        this.titleEditor.on('text-change', () => {
            this.updateCharCount('titleCharCount', this.titleEditor.getText().length);
        });

        this.parseEditor.on('text-change', () => {
            this.updateCharCount('parseCharCount', this.parseEditor.getText().length);
        });
    }

    // 绑定事件
    bindEvents() {
        // 题型变化
        document.getElementById('topicTypeSelect').addEventListener('change', (e) => {
            this.handleTopicTypeChange(e.target.value);
        });

        // 难度滑块
        document.getElementById('difficultyRange').addEventListener('input', (e) => {
            this.updateDifficultyDisplay(e.target.value);
        });

        // 添加选项
        document.getElementById('addOptionBtn').addEventListener('click', () => {
            this.addOption();
        });

        // 添加子题目
        document.getElementById('addSubTopicBtn').addEventListener('click', () => {
            this.addSubTopic();
        });

        // 保存题目
        document.getElementById('saveTopicBtn').addEventListener('click', () => {
            this.saveTopic();
        });

        // LaTeX相关
        document.getElementById('latexInput').addEventListener('input', (e) => {
            this.previewLatex(e.target.value);
        });

        document.getElementById('insertLatexBtn').addEventListener('click', () => {
            this.insertLatexToEditor();
        });

        // 图片相关
        document.getElementById('insertImageBtn').addEventListener('click', () => {
            this.insertImageToEditor();
        });

        document.getElementById('imageFile').addEventListener('change', (e) => {
            this.handleImageUpload(e.target.files[0]);
        });
    }

    // 加载题目数据
    loadTopicData() {
        const topicId = document.getElementById('topicId').value;
        if (!topicId) return;

        // 如果是编辑模式，加载现有题目数据
        fetch(`/topics/api/${topicId}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    this.populateForm(result.data);
                } else {
                    this.showError('加载题目数据失败：' + result.message);
                }
            })
            .catch(error => {
                console.error('加载题目数据失败:', error);
                this.showError('网络错误，请稍后重试');
            });
    }

    // 填充表单数据
    populateForm(topic) {
        // 设置基本信息
        document.getElementById('knowledgePointSelect').value = topic.knowId;
        document.getElementById('topicTypeSelect').value = topic.type;
        document.getElementById('difficultyRange').value = topic.difficulty;
        document.getElementById('scoreInput').value = topic.score;
        document.getElementById('sortInput').value = topic.sort;
        document.getElementById('tagsInput').value = topic.tags || '';
        document.getElementById('sourceInput').value = topic.source || '';

        // 设置编辑器内容
        this.titleEditor.root.innerHTML = topic.title || '';
        this.parseEditor.root.innerHTML = topic.parse || '';

        // 更新难度显示
        this.updateDifficultyDisplay(topic.difficulty);

        // 处理题型相关内容
        this.handleTopicTypeChange(topic.type);

        // 加载选项
        if (topic.options && topic.options !== '[]') {
            try {
                this.options = JSON.parse(topic.options);
                this.renderOptions();
            } catch (e) {
                console.error('解析选项失败:', e);
            }
        }

        // 设置答案
        if (topic.answer) {
            this.setAnswer(topic.answer);
        }

        // 加载子题目（组合题）
        if (topic.type === 'group' && topic.subs && topic.subs !== '[]') {
            try {
                this.subTopics = JSON.parse(topic.subs);
                this.renderSubTopics();
            } catch (e) {
                console.error('解析子题目失败:', e);
            }
        }
    }

    // 处理题型变化
    handleTopicTypeChange(type) {
        this.currentTopicType = type;
        
        const optionsSection = document.getElementById('optionsSection');
        const subTopicsSection = document.getElementById('subTopicsSection');
        
        // 隐藏所有特殊区域
        optionsSection.style.display = 'none';
        subTopicsSection.style.display = 'none';
        
        // 根据题型显示相应区域
        switch (type) {
            case 'single':
            case 'multiple':
                optionsSection.style.display = 'block';
                this.initOptionsForType(type);
                break;
            case 'judge':
                this.initJudgeAnswer();
                break;
            case 'fill':
            case 'short':
            case 'essay':
                this.initTextAnswer();
                break;
            case 'group':
                subTopicsSection.style.display = 'block';
                this.initGroupTopic();
                break;
        }
        
        this.renderAnswerSection();
    }

    // 初始化选择题选项
    initOptionsForType(type) {
        if (this.options.length === 0) {
            // 默认添加4个选项
            for (let i = 0; i < 4; i++) {
                this.options.push({
                    text: '',
                    correct: false
                });
            }
        }
        this.renderOptions();
    }

    // 渲染选项
    renderOptions() {
        const container = document.getElementById('optionsContainer');
        container.innerHTML = '';

        this.options.forEach((option, index) => {
            const optionDiv = this.createOptionElement(option, index);
            container.appendChild(optionDiv);
        });
    }

    // 创建选项元素
    createOptionElement(option, index) {
        const div = document.createElement('div');
        div.className = 'option-item sortable-item';
        div.innerHTML = `
            <div class="drag-handle">
                <i class="bi bi-grip-vertical"></i>
            </div>
            <div class="form-check">
                <input class="form-check-input option-checkbox" type="${this.currentTopicType === 'multiple' ? 'checkbox' : 'radio'}" 
                       name="optionCorrect" value="${index}" ${option.correct ? 'checked' : ''}>
            </div>
            <input type="text" class="form-control option-input" placeholder="选项 ${String.fromCharCode(65 + index)}" 
                   value="${this.escapeHtml(option.text)}" onchange="topicEditor.updateOption(${index}, this.value)">
            <div class="option-actions">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="topicEditor.moveOption(${index}, -1)" 
                        ${index === 0 ? 'disabled' : ''}>
                    <i class="bi bi-arrow-up"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="topicEditor.moveOption(${index}, 1)" 
                        ${index === this.options.length - 1 ? 'disabled' : ''}>
                    <i class="bi bi-arrow-down"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="topicEditor.removeOption(${index})" 
                        ${this.options.length <= 2 ? 'disabled' : ''}>
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

        // 绑定正确答案选择事件
        const checkbox = div.querySelector('.option-checkbox');
        checkbox.addEventListener('change', (e) => {
            this.updateOptionCorrect(index, e.target.checked);
        });

        return div;
    }

    // 渲染答案区域
    renderAnswerSection() {
        const answerSection = document.getElementById('answerSection');
        
        switch (this.currentTopicType) {
            case 'single':
            case 'multiple':
                answerSection.innerHTML = this.createChoiceAnswerHtml();
                break;
            case 'judge':
                answerSection.innerHTML = this.createJudgeAnswerHtml();
                break;
            case 'fill':
            case 'short':
            case 'essay':
                answerSection.innerHTML = this.createTextAnswerHtml();
                break;
            case 'group':
                answerSection.innerHTML = '<p class="text-muted">组合题的答案由各子题目单独设置</p>';
                break;
            default:
                answerSection.innerHTML = '<p class="text-muted">请先选择题型</p>';
        }
    }

    // 创建选择题答案HTML
    createChoiceAnswerHtml() {
        const options = this.options.map((option, index) => {
            return `
                <div class="answer-checkbox-item ${option.correct ? 'selected' : ''}" 
                     onclick="topicEditor.toggleAnswerOption(${index})">
                    <input type="${this.currentTopicType === 'multiple' ? 'checkbox' : 'radio'}" 
                           name="answer" value="${index}" ${option.correct ? 'checked' : ''}>
                    <span>${String.fromCharCode(65 + index)}. ${this.escapeHtml(option.text) || '选项' + (index + 1)}</span>
                </div>
            `;
        }).join('');

        return `
            <div class="answer-checkbox-group">
                ${options}
            </div>
            <small class="form-text text-muted mt-2">
                ${this.currentTopicType === 'multiple' ? '可选择多个正确答案' : '请选择一个正确答案'}
            </small>
        `;
    }

    // 创建判断题答案HTML
    createJudgeAnswerHtml() {
        return `
            <div class="type-judge">
                <div class="answer-options">
                    <div class="answer-option" onclick="topicEditor.setJudgeAnswer(true)">
                        <i class="bi bi-check-circle fs-2 text-success"></i>
                        <div class="mt-2">正确</div>
                    </div>
                    <div class="answer-option" onclick="topicEditor.setJudgeAnswer(false)">
                        <i class="bi bi-x-circle fs-2 text-danger"></i>
                        <div class="mt-2">错误</div>
                    </div>
                </div>
            </div>
        `;
    }

    // 创建文本答案HTML
    createTextAnswerHtml() {
        const placeholder = this.currentTopicType === 'fill' ? '请输入标准答案，多个答案用分号分隔' :
                           this.currentTopicType === 'short' ? '请输入参考答案' : '主观题无标准答案';
        
        return `
            <textarea class="form-control" id="answerText" rows="4" 
                      placeholder="${placeholder}"></textarea>
            <small class="form-text text-muted mt-2">
                ${this.currentTopicType === 'fill' ? '填空题支持多个答案，用分号(;)分隔' : 
                  this.currentTopicType === 'essay' ? '主观题通常不设置标准答案' : ''}
            </small>
        `;
    }

    // 添加选项
    addOption() {
        this.options.push({
            text: '',
            correct: false
        });
        this.renderOptions();
        this.renderAnswerSection();
    }

    // 移除选项
    removeOption(index) {
        if (this.options.length <= 2) return;
        
        this.options.splice(index, 1);
        this.renderOptions();
        this.renderAnswerSection();
    }

    // 移动选项
    moveOption(index, direction) {
        const newIndex = index + direction;
        if (newIndex < 0 || newIndex >= this.options.length) return;
        
        [this.options[index], this.options[newIndex]] = [this.options[newIndex], this.options[index]];
        this.renderOptions();
        this.renderAnswerSection();
    }

    // 更新选项文本
    updateOption(index, text) {
        this.options[index].text = text;
        this.renderAnswerSection();
    }

    // 更新选项正确性
    updateOptionCorrect(index, correct) {
        if (this.currentTopicType === 'single' && correct) {
            // 单选题：取消其他选项的正确标记
            this.options.forEach((option, i) => {
                option.correct = i === index;
            });
        } else {
            this.options[index].correct = correct;
        }
        this.renderOptions();
        this.renderAnswerSection();
    }

    // 切换答案选项
    toggleAnswerOption(index) {
        this.updateOptionCorrect(index, !this.options[index].correct);
    }

    // 设置判断题答案
    setJudgeAnswer(isTrue) {
        document.querySelectorAll('.answer-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        const selectedOption = document.querySelectorAll('.answer-option')[isTrue ? 0 : 1];
        selectedOption.classList.add('selected');
        
        this.judgeAnswer = isTrue;
    }

    // 更新难度显示
    updateDifficultyDisplay(value) {
        const difficultyValue = document.getElementById('difficultyValue');
        const difficulty = parseFloat(value);
        
        let text = difficulty.toFixed(1);
        if (difficulty <= 0.4) {
            text += ' (简单)';
        } else if (difficulty <= 0.7) {
            text += ' (中等)';
        } else {
            text += ' (困难)';
        }
        
        difficultyValue.textContent = text;
    }

    // 更新字符计数
    updateCharCount(elementId, count) {
        document.getElementById(elementId).textContent = `${count} 字符`;
    }

    // 插入LaTeX
    insertLatex(editorType) {
        this.currentEditorTarget = editorType;
        const modal = new bootstrap.Modal(document.getElementById('latexModal'));
        modal.show();
    }

    // 预览LaTeX
    previewLatex(latex) {
        const preview = document.getElementById('latexPreview');
        if (!latex.trim()) {
            preview.innerHTML = '';
            return;
        }
        
        try {
            katex.render(latex, preview, {
                throwOnError: false,
                displayMode: true
            });
        } catch (e) {
            preview.innerHTML = '<span class="text-danger">LaTeX语法错误</span>';
        }
    }

    // 插入LaTeX到编辑器
    insertLatexToEditor() {
        const latex = document.getElementById('latexInput').value;
        if (!latex.trim()) return;
        
        const editor = this.currentEditorTarget === 'titleEditor' ? this.titleEditor : this.parseEditor;
        const range = editor.getSelection() || { index: editor.getLength(), length: 0 };
        
        editor.insertText(range.index, `$$${latex}$$`);
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('latexModal'));
        modal.hide();
        
        document.getElementById('latexInput').value = '';
        document.getElementById('latexPreview').innerHTML = '';
    }

    // 插入图片
    insertImage(editorType) {
        this.currentEditorTarget = editorType;
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        modal.show();
    }

    // 插入图片到编辑器
    insertImageToEditor() {
        const imageUrl = document.getElementById('imageUrl').value;
        const imageAlt = document.getElementById('imageAlt').value;
        
        if (!imageUrl.trim()) {
            this.showError('请输入图片URL');
            return;
        }
        
        const editor = this.currentEditorTarget === 'titleEditor' ? this.titleEditor : this.parseEditor;
        const range = editor.getSelection() || { index: editor.getLength(), length: 0 };
        
        editor.insertEmbed(range.index, 'image', imageUrl);
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('imageModal'));
        modal.hide();
        
        document.getElementById('imageUrl').value = '';
        document.getElementById('imageAlt').value = '';
    }

    // 处理图片上传
    handleImageUpload(file) {
        if (!file) return;
        
        // 这里应该实现图片上传到服务器的逻辑
        // 暂时使用本地预览
        const reader = new FileReader();
        reader.onload = (e) => {
            document.getElementById('imageUrl').value = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    // 保存题目
    async saveTopic() {
        if (!this.validateForm()) return;
        
        this.showLoading(true);
        
        try {
            const topicData = this.collectFormData();
            const topicId = document.getElementById('topicId').value;
            
            const response = await fetch('/topics/api/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + localStorage.getItem('authToken')
                },
                body: JSON.stringify(topicData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(result.message || '保存成功');
                setTimeout(() => {
                    window.location.href = '/topics/bank';
                }, 1500);
            } else {
                this.showError(result.message || '保存失败');
            }
        } catch (error) {
            console.error('保存题目失败:', error);
            this.showError('网络错误，请稍后重试');
        } finally {
            this.showLoading(false);
        }
    }

    // 验证表单
    validateForm() {
        const knowId = document.getElementById('knowledgePointSelect').value;
        const type = document.getElementById('topicTypeSelect').value;
        const title = this.titleEditor.getText().trim();
        
        if (!knowId) {
            this.showError('请选择知识点');
            return false;
        }
        
        if (!type) {
            this.showError('请选择题型');
            return false;
        }
        
        if (!title) {
            this.showError('请输入题目内容');
            return false;
        }
        
        // 验证选择题选项
        if ((type === 'single' || type === 'multiple') && this.options.length < 2) {
            this.showError('选择题至少需要2个选项');
            return false;
        }
        
        // 验证选择题答案
        if (type === 'single' || type === 'multiple') {
            const hasCorrect = this.options.some(option => option.correct);
            if (!hasCorrect) {
                this.showError('请设置正确答案');
                return false;
            }
        }
        
        return true;
    }

    // 收集表单数据
    collectFormData() {
        const topicId = document.getElementById('topicId').value;
        
        const data = {
            knowId: parseInt(document.getElementById('knowledgePointSelect').value),
            type: document.getElementById('topicTypeSelect').value,
            title: this.titleEditor.root.innerHTML,
            difficulty: parseFloat(document.getElementById('difficultyRange').value),
            score: parseInt(document.getElementById('scoreInput').value),
            sort: parseInt(document.getElementById('sortInput').value),
            tags: document.getElementById('tagsInput').value,
            source: document.getElementById('sourceInput').value,
            parse: this.parseEditor.root.innerHTML
        };
        
        if (topicId) {
            data.id = parseInt(topicId);
        }
        
        // 设置选项
        if (this.currentTopicType === 'single' || this.currentTopicType === 'multiple') {
            data.options = this.options;
        }
        
        // 设置答案
        data.answer = this.getAnswer();
        
        // 设置子题目（组合题）
        if (this.currentTopicType === 'group') {
            data.subs = JSON.stringify(this.subTopics);
        }
        
        return data;
    }

    // 获取答案
    getAnswer() {
        switch (this.currentTopicType) {
            case 'single':
                const singleCorrect = this.options.findIndex(option => option.correct);
                return singleCorrect >= 0 ? String.fromCharCode(65 + singleCorrect) : '';
            
            case 'multiple':
                const multipleCorrect = this.options
                    .map((option, index) => option.correct ? String.fromCharCode(65 + index) : null)
                    .filter(item => item !== null);
                return multipleCorrect.join(',');
            
            case 'judge':
                return this.judgeAnswer !== undefined ? (this.judgeAnswer ? '正确' : '错误') : '';
            
            case 'fill':
            case 'short':
            case 'essay':
                const answerText = document.getElementById('answerText');
                return answerText ? answerText.value : '';
            
            default:
                return '';
        }
    }

    // 设置答案
    setAnswer(answer) {
        switch (this.currentTopicType) {
            case 'single':
                const singleIndex = answer.charCodeAt(0) - 65;
                if (singleIndex >= 0 && singleIndex < this.options.length) {
                    this.options.forEach((option, index) => {
                        option.correct = index === singleIndex;
                    });
                }
                break;
            
            case 'multiple':
                const multipleIndices = answer.split(',').map(char => char.charCodeAt(0) - 65);
                this.options.forEach((option, index) => {
                    option.correct = multipleIndices.includes(index);
                });
                break;
            
            case 'judge':
                this.judgeAnswer = answer === '正确';
                setTimeout(() => {
                    this.setJudgeAnswer(this.judgeAnswer);
                }, 100);
                break;
            
            case 'fill':
            case 'short':
            case 'essay':
                setTimeout(() => {
                    const answerText = document.getElementById('answerText');
                    if (answerText) {
                        answerText.value = answer;
                    }
                }, 100);
                break;
        }
    }

    // 工具方法
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    showError(message) {
        // 这里可以使用更好的错误提示组件
        alert(message);
    }

    showSuccess(message) {
        // 这里可以使用更好的成功提示组件
        alert(message);
    }
}

// 全局变量和方法
let topicEditor;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    topicEditor = new TopicEditor();
});

// 全局方法供HTML调用
function insertLatex(editorType) {
    topicEditor.insertLatex(editorType);
}

function insertImage(editorType) {
    topicEditor.insertImage(editorType);
}
