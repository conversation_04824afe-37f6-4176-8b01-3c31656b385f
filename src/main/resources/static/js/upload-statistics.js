document.addEventListener('DOMContentLoaded', function () {
    // 初始化用户上传统计
    initUserUploadStats();

    // 初始化图表（如果存在）
    const chartDom = document.getElementById('uploadStatsChart');
    if (chartDom) {
        initUploadChart();
    }
});

// 用户上传统计管理类
class UserUploadStatsManager {
    constructor() {
        this.isAdmin = false;
        this.init();
    }

    async init() {
        await this.checkUserRole();
        await this.loadUserStats();
        this.bindEvents();
    }

    async checkUserRole() {
        try {
            // 检查用户权限，这里可以通过API调用或从页面数据获取
            const response = await fetch('/api/upload-stats/overview');
            if (response.ok) {
                const result = await response.json();
                this.isAdmin = result.data?.isAdmin || false;
            }
        } catch (error) {
            console.error('检查用户角色失败:', error);
        }
    }

    async loadUserStats() {
        try {
            const response = await fetch('/api/upload-stats/overview');
            if (!response.ok) {
                throw new Error('获取统计数据失败');
            }

            const result = await response.json();
            if (result.success) {
                this.renderUserStats(result.data);
                await this.loadUserTrend();
            } else {
                console.error('获取统计数据失败:', result.message);
            }
        } catch (error) {
            console.error('加载用户统计失败:', error);
        }
    }

    renderUserStats(stats) {
        // 更新统计卡片
        this.updateStatCard('todayCount', stats.todayCount || 0);
        this.updateStatCard('weekCount', stats.weekCount || 0);
        this.updateStatCard('monthCount', stats.monthCount || 0);
        this.updateStatCard('totalCount', stats.totalCount || 0);

        // 更新进度条
        const dailyLimit = stats.dailyLimit || 5000;
        const todayCount = stats.todayCount || 0;
        const percentage = Math.min((todayCount / dailyLimit) * 100, 100);

        this.updateProgressBar('dailyProgress', percentage, `${todayCount}/${dailyLimit}`);

        // 更新剩余量
        const remaining = stats.remainingToday || 0;
        this.updateStatCard('remainingToday', remaining);

        // 更新连续天数
        if (stats.consecutiveDays !== undefined) {
            this.updateStatCard('consecutiveDays', stats.consecutiveDays);
        }
    }

    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value.toLocaleString();
        }
    }

    updateProgressBar(elementId, percentage, text) {
        const progressBar = document.getElementById(elementId);
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);

            const progressText = progressBar.querySelector('.progress-text');
            if (progressText) {
                progressText.textContent = text;
            }
        }
    }

    async loadUserTrend(days = 7) {
        try {
            const response = await fetch(`/api/upload-stats/trend?days=${days}`);
            if (!response.ok) {
                throw new Error('获取趋势数据失败');
            }

            const result = await response.json();
            if (result.success) {
                this.renderTrendChart(result.data);
            }
        } catch (error) {
            console.error('加载用户趋势失败:', error);
        }
    }

    renderTrendChart(trendData) {
        const chartElement = document.getElementById('userTrendChart');
        if (!chartElement || !window.echarts) {
            return;
        }

        const chart = echarts.init(chartElement);

        const dates = trendData.map(item => item.upload_date);
        const counts = trendData.map(item => item.topic_count || 0);

        const option = {
            title: {
                text: '最近7天上传趋势',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const data = params[0];
                    return `${data.axisValue}<br/>上传数量: ${data.value}`;
                }
            },
            xAxis: {
                type: 'category',
                data: dates,
                axisLabel: {
                    formatter: function(value) {
                        return new Date(value).toLocaleDateString('zh-CN', {
                            month: 'short',
                            day: 'numeric'
                        });
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '上传数量'
            },
            series: [{
                data: counts,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    color: '#0066FF',
                    width: 2
                },
                itemStyle: {
                    color: '#0066FF'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(0, 102, 255, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(0, 102, 255, 0.1)'
                        }]
                    }
                }
            }],
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            }
        };

        chart.setOption(option);

        // 响应式处理
        window.addEventListener('resize', () => {
            chart.resize();
        });
    }

    bindEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshStatsBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadUserStats();
            });
        }

        // 趋势天数选择
        const trendDaysSelect = document.getElementById('trendDaysSelect');
        if (trendDaysSelect) {
            trendDaysSelect.addEventListener('change', (e) => {
                this.loadUserTrend(parseInt(e.target.value));
            });
        }

        // 如果是管理员，加载管理员统计
        if (this.isAdmin) {
            this.loadAdminStats();
        }
    }

    async loadAdminStats() {
        try {
            const response = await fetch('/api/upload-stats/admin/overview');
            if (!response.ok) {
                return; // 非管理员或权限不足
            }

            const result = await response.json();
            if (result.success) {
                this.renderAdminStats(result.data);
                await this.loadAdminTrend();
            }
        } catch (error) {
            console.error('加载管理员统计失败:', error);
        }
    }

    renderAdminStats(stats) {
        // 渲染管理员统计数据
        this.updateStatCard('adminTodayTotal', stats.todayTotal || 0);
        this.updateStatCard('adminTodayActiveUsers', stats.todayActiveUsers || 0);
        this.updateStatCard('adminWeekTotal', stats.weekTotal || 0);
        this.updateStatCard('adminMonthTotal', stats.monthTotal || 0);

        // 渲染用户排行榜
        if (stats.weeklyRanking) {
            this.renderUserRanking(stats.weeklyRanking);
        }
    }

    renderUserRanking(ranking) {
        const rankingElement = document.getElementById('userRanking');
        if (!rankingElement) return;

        const html = ranking.map((user, index) => `
            <div class="ranking-item">
                <span class="rank">${index + 1}</span>
                <span class="username">${user.username}</span>
                <span class="count">${user.total_count}</span>
            </div>
        `).join('');

        rankingElement.innerHTML = html;
    }

    async loadAdminTrend(days = 30) {
        try {
            const response = await fetch(`/api/upload-stats/admin/trend?days=${days}`);
            if (!response.ok) {
                return;
            }

            const result = await response.json();
            if (result.success) {
                this.renderAdminTrendChart(result.data);
            }
        } catch (error) {
            console.error('加载管理员趋势失败:', error);
        }
    }

    renderAdminTrendChart(trendData) {
        const chartElement = document.getElementById('adminTrendChart');
        if (!chartElement || !window.echarts) {
            return;
        }

        const chart = echarts.init(chartElement);

        const dates = trendData.map(item => item.upload_date);
        const counts = trendData.map(item => item.topic_count || 0);

        const option = {
            title: {
                text: '全站上传趋势',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: dates
            },
            yAxis: {
                type: 'value',
                name: '上传数量'
            },
            series: [{
                data: counts,
                type: 'bar',
                itemStyle: {
                    color: '#28a745'
                }
            }]
        };

        chart.setOption(option);
    }
}

// 初始化用户上传统计
function initUserUploadStats() {
    new UserUploadStatsManager();
}

// 原有的图表初始化函数
function initUploadChart() {
    const chartDom = document.getElementById('uploadStatsChart');
    const statType = document.getElementById('statType');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const searchBtn = document.getElementById('searchBtn');
    const totalCountSpan = document.getElementById('totalCount');
    let chart = echarts.init(chartDom);

    function getRecentDaysRange(days = 7) {
        const localEnd = new Date();
        const localStart = new Date();
        localStart.setDate(localEnd.getDate() - (days - 1));

        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        };
        
        return [formatDate(localStart), formatDate(localEnd)];
    }

    function processChartData(type, apiData, startStr, endStr) {
        let total = 0;
        let chartData = [];
        
        apiData.forEach(item => {
            total += (item.count || 0);
        });

        if (type === 'day' && startStr && endStr) {
            const dataMap = new Map(apiData.map(item => [item.day, item.count]));
            
            let currentDate = new Date(startStr + 'T00:00:00.000Z');
            const finalDate = new Date(endStr + 'T00:00:00.000Z');

            while (currentDate <= finalDate) {
                const dateString = currentDate.toISOString().slice(0, 10);
                const count = dataMap.get(dateString) || 0;
                chartData.push([dateString, count]);
                
                currentDate.setUTCDate(currentDate.getUTCDate() + 1);
            }
        } else {
            chartData = apiData.map(item => {
                const key = item.week || item.month;
                return [key, item.count];
            }).sort((a, b) => a[0].localeCompare(b[0]));
        }

        return { chartData, total };
    }

    function renderChart(type, data, total) {
        totalCountSpan.textContent = total;
        
        const xAxisType = type === 'day' ? 'time' : 'category';
        const xAxisName = type === 'day' ? '日期' : (type === 'week' ? '周' : '月份');
        
        chart.setOption({
            title: { text: '题目上传趋势', left: 'center', textStyle: { fontSize: 18, fontWeight: 'bold' } },
            tooltip: {
                trigger: 'axis',
                formatter: (params) => {
                    const param = params[0];
                    let label = param.axisValueLabel;
                    return `${label}<br/>${param.marker} ${param.seriesName}: <strong>${param.value[1]}</strong>`;
                }
            },
            legend: { data: ['上传量'], top: 40, right: 20 },
            grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
            xAxis: {
                type: xAxisType,
                name: xAxisName,
                boundaryGap: type !== 'day',
                axisLabel: { hideOverlap: true }
            },
            yAxis: { type: 'value', name: '上传题目数', min: 0 },
            series: [{
                name: '上传量',
                type: 'line',
                smooth: true,
                showSymbol: false,
                emphasis: { focus: 'series', itemStyle: { borderWidth: 2 } },
                data: data,
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(58, 132, 255, 0.5)' },
                        { offset: 1, color: 'rgba(58, 132, 255, 0.1)' }
                    ])
                },
                itemStyle: { color: '#3A84FF' },
                lineStyle: { width: 2.5 }
            }]
        }, true);
    }

    function fetchAndRenderStats() {
        const type = statType.value;
        const start = startDate.value;
        const end = endDate.value;
        
        let url = `/api/topics/statistics?type=${type}`;
        if (type === 'day' && start && end) {
            url += `&startDate=${start}&endDate=${end}`;
        }
        
        chart.showLoading();
        fetch(url)
            .then(res => {
                if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
                return res.json();
            })
            .then(apiResponse => {
                chart.hideLoading();
                if (apiResponse.code !== 200) {
                    showToast(apiResponse.message || '获取统计数据失败', 'error');
                    renderChart(type, [], 0); // Show empty chart
                    return;
                }
                const dataFromApi = apiResponse.data || [];
                const { chartData, total } = processChartData(type, dataFromApi, start, end);
                renderChart(type, chartData, total);
            })
            .catch(e => {
                chart.hideLoading();
                showToast('网络错误或服务器异常: ' + e.message, 'error');
                renderChart(type, [], 0); // Show empty chart on error
            });
    }

    statType.addEventListener('change', function () {
        if (this.value !== 'day') {
            startDate.disabled = true;
            endDate.disabled = true;
            startDate.value = '';
            endDate.value = '';
        } else {
            startDate.disabled = false;
            endDate.disabled = false;
            const [start, end] = getRecentDaysRange(7);
            startDate.value = start;
            endDate.value = end;
        }
        fetchAndRenderStats();
    });

    searchBtn.addEventListener('click', fetchAndRenderStats);

    window.addEventListener('resize', () => chart.resize());

    function initialize() {
        statType.value = 'day';
        const [start, end] = getRecentDaysRange(7);
        startDate.value = start;
        endDate.value = end;
        startDate.disabled = false;
        endDate.disabled = false;
        fetchAndRenderStats();
    }
    
    initialize();
    
    window.refreshStatsChart = fetchAndRenderStats;
});
