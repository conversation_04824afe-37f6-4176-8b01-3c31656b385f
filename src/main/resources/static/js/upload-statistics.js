document.addEventListener('DOMContentLoaded', function () {
    const chartDom = document.getElementById('uploadStatsChart');
    if (!chartDom) {
        return; // Exit if chart element is not on the page
    }

    const statType = document.getElementById('statType');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const searchBtn = document.getElementById('searchBtn');
    const totalCountSpan = document.getElementById('totalCount');
    let chart = echarts.init(chartDom);

    function getRecentDaysRange(days = 7) {
        const localEnd = new Date();
        const localStart = new Date();
        localStart.setDate(localEnd.getDate() - (days - 1));

        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        };
        
        return [formatDate(localStart), formatDate(localEnd)];
    }

    function processChartData(type, apiData, startStr, endStr) {
        let total = 0;
        let chartData = [];
        
        apiData.forEach(item => {
            total += (item.count || 0);
        });

        if (type === 'day' && startStr && endStr) {
            const dataMap = new Map(apiData.map(item => [item.day, item.count]));
            
            let currentDate = new Date(startStr + 'T00:00:00.000Z');
            const finalDate = new Date(endStr + 'T00:00:00.000Z');

            while (currentDate <= finalDate) {
                const dateString = currentDate.toISOString().slice(0, 10);
                const count = dataMap.get(dateString) || 0;
                chartData.push([dateString, count]);
                
                currentDate.setUTCDate(currentDate.getUTCDate() + 1);
            }
        } else {
            chartData = apiData.map(item => {
                const key = item.week || item.month;
                return [key, item.count];
            }).sort((a, b) => a[0].localeCompare(b[0]));
        }

        return { chartData, total };
    }

    function renderChart(type, data, total) {
        totalCountSpan.textContent = total;
        
        const xAxisType = type === 'day' ? 'time' : 'category';
        const xAxisName = type === 'day' ? '日期' : (type === 'week' ? '周' : '月份');
        
        chart.setOption({
            title: { text: '题目上传趋势', left: 'center', textStyle: { fontSize: 18, fontWeight: 'bold' } },
            tooltip: {
                trigger: 'axis',
                formatter: (params) => {
                    const param = params[0];
                    let label = param.axisValueLabel;
                    return `${label}<br/>${param.marker} ${param.seriesName}: <strong>${param.value[1]}</strong>`;
                }
            },
            legend: { data: ['上传量'], top: 40, right: 20 },
            grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
            xAxis: {
                type: xAxisType,
                name: xAxisName,
                boundaryGap: type !== 'day',
                axisLabel: { hideOverlap: true }
            },
            yAxis: { type: 'value', name: '上传题目数', min: 0 },
            series: [{
                name: '上传量',
                type: 'line',
                smooth: true,
                showSymbol: false,
                emphasis: { focus: 'series', itemStyle: { borderWidth: 2 } },
                data: data,
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(58, 132, 255, 0.5)' },
                        { offset: 1, color: 'rgba(58, 132, 255, 0.1)' }
                    ])
                },
                itemStyle: { color: '#3A84FF' },
                lineStyle: { width: 2.5 }
            }]
        }, true);
    }

    function fetchAndRenderStats() {
        const type = statType.value;
        const start = startDate.value;
        const end = endDate.value;
        
        let url = `/api/topics/statistics?type=${type}`;
        if (type === 'day' && start && end) {
            url += `&startDate=${start}&endDate=${end}`;
        }
        
        chart.showLoading();
        fetch(url)
            .then(res => {
                if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
                return res.json();
            })
            .then(apiResponse => {
                chart.hideLoading();
                if (apiResponse.code !== 200) {
                    showToast(apiResponse.message || '获取统计数据失败', 'error');
                    renderChart(type, [], 0); // Show empty chart
                    return;
                }
                const dataFromApi = apiResponse.data || [];
                const { chartData, total } = processChartData(type, dataFromApi, start, end);
                renderChart(type, chartData, total);
            })
            .catch(e => {
                chart.hideLoading();
                showToast('网络错误或服务器异常: ' + e.message, 'error');
                renderChart(type, [], 0); // Show empty chart on error
            });
    }

    statType.addEventListener('change', function () {
        if (this.value !== 'day') {
            startDate.disabled = true;
            endDate.disabled = true;
            startDate.value = '';
            endDate.value = '';
        } else {
            startDate.disabled = false;
            endDate.disabled = false;
            const [start, end] = getRecentDaysRange(7);
            startDate.value = start;
            endDate.value = end;
        }
        fetchAndRenderStats();
    });

    searchBtn.addEventListener('click', fetchAndRenderStats);

    window.addEventListener('resize', () => chart.resize());

    function initialize() {
        statType.value = 'day';
        const [start, end] = getRecentDaysRange(7);
        startDate.value = start;
        endDate.value = end;
        startDate.disabled = false;
        endDate.disabled = false;
        fetchAndRenderStats();
    }
    
    initialize();
    
    window.refreshStatsChart = fetchAndRenderStats;
});
