// 全局变量
let currentSessionId = null;

document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const showRegister = document.getElementById('showRegister');
    const showLogin = document.getElementById('showLogin');
    const formTitle = document.getElementById('formTitle');

    // 初始化验证码
    loadCaptcha();

    // 加载验证码
    function loadCaptcha() {
        fetch('/api/captcha/generate')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('captchaImage').src = data.data.captchaImage;
                    currentSessionId = data.data.sessionId;
                } else {
                    showToast('验证码加载失败', 'error');
                }
            })
            .catch(error => {
                console.error('加载验证码失败:', error);
                showToast('验证码加载失败', 'error');
            });
    }

    // 刷新验证码
    window.refreshCaptcha = function() {
        const refreshData = currentSessionId ? { sessionId: currentSessionId } : {};

        fetch('/api/captcha/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(refreshData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('captchaImage').src = data.data.captchaImage;
                currentSessionId = data.data.sessionId;
                // 清空验证码输入框
                document.getElementById('captchaCode').value = '';
            } else {
                showToast('验证码刷新失败', 'error');
            }
        })
        .catch(error => {
            console.error('刷新验证码失败:', error);
            showToast('验证码刷新失败', 'error');
        });
    }

    // 切换表单显示
    function toggleForms(showRegisterForm) {
        loginForm.style.display = showRegisterForm ? 'none' : 'block';
        registerForm.style.display = showRegisterForm ? 'block' : 'none';
        formTitle.textContent = showRegisterForm ? '创建账号' : '登录账号';
        document.querySelector('.subtitle').textContent = showRegisterForm ?
            '填写以下信息创建您的账号' : '欢迎回来，请输入您的账号信息';
    }

    showRegister.addEventListener('click', (e) => {
        e.preventDefault();
        toggleForms(true);
    });

    showLogin.addEventListener('click', (e) => {
        e.preventDefault();
        toggleForms(false);
    });

    // 登录表单提交
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const submitBtn = loginForm.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = '登录中...';

        try {
            const response = await fetch('/api/user/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: document.getElementById('username').value,
                    password: document.getElementById('password').value,
                    captchaCode: document.getElementById('captchaCode').value,
                    sessionId: currentSessionId,
                    ip: await getClientIP()
                })
            });

            const data = await response.json();
            if (data.code === 200) {
                localStorage.setItem('token', data.data.token);
                
                // 设置JWT_TOKEN到cookie中，有效期1天
                document.cookie = `JWT_TOKEN=${data.data.token}; path=/; max-age=86400; SameSite=Strict;`;
                // 不在Cookie中设置Authorization头，避免格式问题
                // Authorization头应该在HTTP请求头中设置，而不是Cookie中
                
                showToast('登录成功', 'success');
                setTimeout(() => window.location.href = '/index.html', 1000);
            } else {
                showToast(data.message, 'error');
                // 登录失败时刷新验证码
                refreshCaptcha();
            }
        } catch (error) {
            console.error('登录失败:', error);
            showToast('登录失败，请重试', 'error');
            // 网络错误时也刷新验证码
            refreshCaptcha();
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '登录';
        }
    });

    // 注册表单提交
    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const submitBtn = registerForm.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = '注册中...';

        try {
            const response = await fetch('/api/user/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: document.getElementById('reg-username').value,
                    password: document.getElementById('reg-password').value,
                    email: document.getElementById('reg-email').value
                })
            });

            const data = await response.json();
            if (data.code === 200) {
                showToast('注册成功', 'success');
                setTimeout(() => toggleForms(false), 1000);
            } else {
                showToast(data.message, 'error');
            }
        } catch (error) {
            console.error('注册失败:', error);
            showToast('注册失败，请重试', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '注册';
        }
    });

    // 获取客户端IP
    async function getClientIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            console.error('获取IP失败:', error);
            return '';
        }
    }

    // 提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }, 100);
    }
}); 