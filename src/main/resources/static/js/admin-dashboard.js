// 管理员后台JavaScript
class AdminDashboard {
    constructor() {
        this.charts = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDashboardData();
        this.initCharts();
        this.loadRecentActivities();
    }

    bindEvents() {
        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const sidebar = document.getElementById('sidebar');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                this.saveLayoutPreference();
            });
        }

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }

        // 用户菜单切换
        const userMenuToggle = document.querySelector('.user-menu-toggle');
        if (userMenuToggle) {
            userMenuToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleUserMenu();
            });
        }

        // 点击外部关闭用户菜单
        document.addEventListener('click', () => {
            this.closeUserMenu();
        });

        // 趋势周期选择
        const trendPeriod = document.getElementById('trendPeriod');
        if (trendPeriod) {
            trendPeriod.addEventListener('change', (e) => {
                this.updateTrendChart(parseInt(e.target.value));
            });
        }

        // 窗口大小变化时重新调整图表
        window.addEventListener('resize', () => {
            this.resizeCharts();
        });

        // 恢复布局偏好
        this.loadLayoutPreference();
    }

    async loadDashboardData() {
        try {
            this.showLoading(true);

            // 并行加载所有统计数据
            const [statsResponse, uploadStatsResponse] = await Promise.all([
                fetch('/api/admin/stats/overview'),
                fetch('/api/upload-stats/admin/overview')
            ]);

            if (statsResponse.ok) {
                const statsResult = await statsResponse.json();
                if (statsResult.success) {
                    this.updateStatCards(statsResult.data);
                }
            }

            if (uploadStatsResponse.ok) {
                const uploadResult = await uploadStatsResponse.json();
                if (uploadResult.success) {
                    this.updateUploadStats(uploadResult.data);
                }
            }

        } catch (error) {
            console.error('加载仪表盘数据失败:', error);
            this.showError('加载数据失败，请刷新页面重试');
        } finally {
            this.showLoading(false);
        }
    }

    updateStatCards(data) {
        // 更新统计卡片
        this.updateStatCard('totalTopics', data.totalTopics || 0);
        this.updateStatCard('totalUsers', data.totalUsers || 0);
        this.updateStatCard('pendingAudits', data.pendingAudits || 0);
    }

    updateUploadStats(data) {
        // 更新上传统计
        this.updateStatCard('todayUploads', data.todayTotal || 0);
    }

    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            // 添加数字动画效果
            this.animateNumber(element, parseInt(element.textContent) || 0, value);
        }
    }

    animateNumber(element, start, end) {
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.round(start + (end - start) * easeOutQuart);
            
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    initCharts() {
        this.initUploadTrendChart();
        this.initTopicTypeChart();
    }

    async initUploadTrendChart() {
        const ctx = document.getElementById('uploadTrendChart');
        if (!ctx) return;

        try {
            const response = await fetch('/api/upload-stats/admin/trend?days=7');
            if (!response.ok) return;

            const result = await response.json();
            if (!result.success) return;

            const data = result.data;
            const labels = data.map(item => {
                const date = new Date(item.upload_date);
                return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
            });
            const values = data.map(item => item.topic_count || 0);

            this.charts.uploadTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '上传数量',
                        data: values,
                        borderColor: '#0066FF',
                        backgroundColor: 'rgba(0, 102, 255, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#0066FF',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#0066FF',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#6c757d'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                color: '#6c757d'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

        } catch (error) {
            console.error('初始化上传趋势图表失败:', error);
        }
    }

    async initTopicTypeChart() {
        const ctx = document.getElementById('topicTypeChart');
        if (!ctx) return;

        try {
            const response = await fetch('/api/admin/stats/topic-types');
            if (!response.ok) {
                // 如果API不存在，使用模拟数据
                this.createMockTopicTypeChart(ctx);
                return;
            }

            const result = await response.json();
            if (!result.success) {
                this.createMockTopicTypeChart(ctx);
                return;
            }

            this.createTopicTypeChart(ctx, result.data);

        } catch (error) {
            console.error('初始化题型分布图表失败:', error);
            this.createMockTopicTypeChart(ctx);
        }
    }

    createMockTopicTypeChart(ctx) {
        // 使用模拟数据创建题型分布图表
        const mockData = [
            { type: '单选题', count: 450 },
            { type: '多选题', count: 280 },
            { type: '判断题', count: 320 },
            { type: '填空题', count: 180 },
            { type: '简答题', count: 120 },
            { type: '主观题', count: 80 }
        ];

        this.createTopicTypeChart(ctx, mockData);
    }

    createTopicTypeChart(ctx, data) {
        const labels = data.map(item => item.type);
        const values = data.map(item => item.count);
        const colors = [
            '#0066FF', '#28a745', '#ffc107', 
            '#17a2b8', '#dc3545', '#6c757d'
        ];

        this.charts.topicType = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 0,
                    hoverBorderWidth: 2,
                    hoverBorderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#0066FF',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    async updateTrendChart(days) {
        if (!this.charts.uploadTrend) return;

        try {
            const response = await fetch(`/api/upload-stats/admin/trend?days=${days}`);
            if (!response.ok) return;

            const result = await response.json();
            if (!result.success) return;

            const data = result.data;
            const labels = data.map(item => {
                const date = new Date(item.upload_date);
                return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
            });
            const values = data.map(item => item.topic_count || 0);

            this.charts.uploadTrend.data.labels = labels;
            this.charts.uploadTrend.data.datasets[0].data = values;
            this.charts.uploadTrend.update('active');

        } catch (error) {
            console.error('更新趋势图表失败:', error);
        }
    }

    async loadRecentActivities() {
        const container = document.getElementById('recentActivities');
        if (!container) return;

        try {
            const response = await fetch('/api/admin/activities/recent?limit=5');
            if (!response.ok) {
                // 如果API不存在，使用模拟数据
                this.renderMockActivities(container);
                return;
            }

            const result = await response.json();
            if (result.success) {
                this.renderActivities(container, result.data);
            } else {
                this.renderMockActivities(container);
            }

        } catch (error) {
            console.error('加载最新动态失败:', error);
            this.renderMockActivities(container);
        }
    }

    renderMockActivities(container) {
        const mockActivities = [
            {
                type: 'topic_upload',
                title: '用户张三上传了新题目',
                desc: '上传了5道单选题到"数学基础"知识点',
                time: '2分钟前',
                icon: 'bi-plus-circle',
                iconColor: '#28a745'
            },
            {
                type: 'topic_audit',
                title: '题目审核完成',
                desc: '管理员审核通过了10道题目',
                time: '15分钟前',
                icon: 'bi-check-circle',
                iconColor: '#0066FF'
            },
            {
                type: 'user_register',
                title: '新用户注册',
                desc: '用户"李四"完成注册',
                time: '1小时前',
                icon: 'bi-person-plus',
                iconColor: '#17a2b8'
            },
            {
                type: 'system_backup',
                title: '系统备份完成',
                desc: '数据库自动备份已完成',
                time: '2小时前',
                icon: 'bi-shield-check',
                iconColor: '#ffc107'
            },
            {
                type: 'topic_delete',
                title: '题目删除',
                desc: '管理员删除了3道重复题目',
                time: '3小时前',
                icon: 'bi-trash',
                iconColor: '#dc3545'
            }
        ];

        this.renderActivities(container, mockActivities);
    }

    renderActivities(container, activities) {
        const html = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon" style="background-color: ${activity.iconColor}">
                    <i class="bi ${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-desc">${activity.desc}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    toggleUserMenu() {
        const dropdown = document.getElementById('userMenuDropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    }

    closeUserMenu() {
        const dropdown = document.getElementById('userMenuDropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }

    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    }

    saveLayoutPreference() {
        const sidebar = document.getElementById('sidebar');
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('admin_sidebar_collapsed', isCollapsed);
    }

    loadLayoutPreference() {
        const isCollapsed = localStorage.getItem('admin_sidebar_collapsed') === 'true';
        const sidebar = document.getElementById('sidebar');
        if (isCollapsed && sidebar) {
            sidebar.classList.add('collapsed');
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = show ? 'flex' : 'none';
        }
    }

    showError(message) {
        // 这里可以使用更好的错误提示组件
        console.error(message);
        // 可以添加toast通知
    }

    showSuccess(message) {
        // 这里可以使用更好的成功提示组件
        console.log(message);
        // 可以添加toast通知
    }
}

// 全局函数
function refreshData() {
    if (window.adminDashboard) {
        window.adminDashboard.loadDashboardData();
        window.adminDashboard.loadRecentActivities();
    }
}

function showNotifications() {
    // 显示通知面板
    alert('通知功能开发中...');
}

function toggleUserMenu() {
    if (window.adminDashboard) {
        window.adminDashboard.toggleUserMenu();
    }
}

function logout() {
    if (confirm('确定要退出登录吗？')) {
        // 清除本地存储的认证信息
        localStorage.removeItem('authToken');
        localStorage.removeItem('admin_sidebar_collapsed');
        
        // 跳转到登录页面
        window.location.href = '/admin/login';
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
});
