// Hoist utility functions to the top to ensure they are defined before use.
function renderMarkdown(text) {
    if (!text) return '';
    if (typeof marked === 'undefined') {
        console.error('marked.js library is not loaded.');
        return text; // Fallback to plain text
    }
    const unescapedText = String(text).replace(/\\/g, '\\');
    return marked.parse(unescapedText, { breaks: true, gfm: true });
}

// Global variables
let editor;
let previewTopics = [];

document.addEventListener('DOMContentLoaded', function() {
    // Initialize CodeMirror editor
    editor = CodeMirror.fromTextArea(document.getElementById('jsonEditor'), {
        mode: {name: "javascript", json: true},
        theme: "material-darker",
        lineNumbers: true,
        matchBrackets: true,
        autoCloseBrackets: true,
        foldGutter: true,
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
        lineWrapping: true,
        extraKeys: {"Ctrl-Space": "autocomplete"}
    });

    // Get DOM elements
    const insertExampleBtn = document.getElementById('insertExampleBtn');
    const submitTopicsBtn = document.getElementById('submitTopicsBtn');
    const defaultSubmitBtnHtml = '<i class="bi bi-cloud-upload"></i> 提交题目';
    submitTopicsBtn.innerHTML = defaultSubmitBtnHtml;
    const validateAndPreviewBtn = document.getElementById('validateAndPreviewBtn');
    const previewContainer = document.getElementById('previewContainer');
    const errorMessageDiv = document.getElementById('errorMessage');
    const successMessageDiv = document.getElementById('successMessage');
    const previewTopicCountSpan = document.getElementById('previewTopicCount');

    // Event Listeners
    insertExampleBtn.addEventListener('click', function () {
        editor.setValue(JSON.stringify(exampleTopicData, null, 2));
        showToast('示例JSON已插入编辑器', 'info');
        validateAndPreview();
    });

    validateAndPreviewBtn.addEventListener('click', validateAndPreview);

    submitTopicsBtn.addEventListener('click', async function () {
        if (previewTopics.length === 0) {
            showError('没有可提交的题目。');
            return;
        }
        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 提交中...';
        
        let submissionSuccess = false;

        try {
            const token = getAuthHeader();
            if (!token) {
                showError('用户未登录或会话已过期，请重新登录。');
                return; // Finally will handle button reset
            }

            const response = await fetch('/api/topics/upload', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(previewTopics)
            });

            const result = await response.json();

            if (response.ok && result.code === 200) {
                showSuccess(result.message || `成功提交 ${previewTopics.length} 道题目！`);
                editor.setValue('');
                previewTopics = [];
                renderPreview();
                submissionSuccess = true;

                if (typeof window.refreshStatsChart === 'function') {
                    window.refreshStatsChart();
                }
            } else {
                throw new Error(result.message || `服务器错误: ${response.status}`);
            }
        } catch (error) {
            showError('提交失败: ' + error.message);
        } finally {
            // 提交结束后总是恢复按钮为可用，并显示默认文字
            this.disabled = false;
            this.innerHTML = defaultSubmitBtnHtml;
        }
    });

    // Core Functions
    function validateAndPreview() {
        const jsonString = editor.getValue();
        
        previewTopics = [];
        previewTopicCountSpan.textContent = '0';
        submitTopicsBtn.disabled = true;
        // 确保按钮文本复位，避免上一轮提交中的 loading 状态残留
        submitTopicsBtn.innerHTML = defaultSubmitBtnHtml;
        previewContainer.classList.add('preview-container-empty');
        previewContainer.innerHTML = '<p class="text-muted text-center p-5"><i class="bi bi-card-text fs-1"></i><br>请在编辑器中输入或粘贴题目的JSON数据。</p>';

        if (!jsonString.trim()) {
            showError('JSON数据不能为空。');
            return;
        }

        try {
            const topics = JSON.parse(jsonString);
            if (!Array.isArray(topics)) throw new Error('JSON数据必须是一个数组。');

            for (const topic of topics) {
                if (!topic.know_id || !topic.type || !topic.title || !topic.difficulty) {
                    throw new Error('有题目缺少必需字段 (know_id, type, title, difficulty)。');
                }
            }

            if (topics.length === 0) {
                showSuccess('JSON数组为空，没有可预览的题目。');
                previewContainer.innerHTML = '<p class="text-muted text-center p-5"><i class="bi bi-card-text fs-1"></i><br>JSON数组为空。</p>';
                return;
            }

            previewTopics = topics;
            showSuccess(`JSON格式正确，共 ${previewTopics.length} 道题目，预览已生成。`);
            errorMessageDiv.classList.add('d-none');
            submitTopicsBtn.disabled = false;
            renderPreview();

        } catch (e) {
            showError('JSON解析或验证失败: ' + e.message);
            previewContainer.innerHTML = `<p class="text-danger text-center p-5"><i class="bi bi-x-octagon-fill fs-1"></i><br>${e.message}</p>`;
        }
    }

    function renderPreview() {
        const previewContainer = document.getElementById('previewContainer');
        const previewTopicCountSpan = document.getElementById('previewTopicCount');

        previewTopicCountSpan.textContent = previewTopics.length;

        if (previewTopics.length === 0) {
            previewContainer.innerHTML = '<p class="text-muted">没有预览内容。</p>';
            previewContainer.classList.add('preview-container-empty');
            return;
        }

        previewContainer.classList.remove('preview-container-empty');

        let allTopicsHtml = '';
        previewTopics.forEach((topic, index) => {
            let optionsHtml = '';
            if (topic.options && Array.isArray(topic.options)) {
                optionsHtml = topic.options.map(opt => {
                    const isCorrect = topic.answer && topic.answer.includes(opt.key);
                    const correctClass = isCorrect ? 'correct-answer' : '';
                    return `<li class="list-group-item ${correctClass}"><strong>${opt.key}:</strong> ${renderMarkdown(opt.name)}</li>`;
                }).join('');
            }

            let tagsHtml = '';
            if (topic.tags && typeof topic.tags === 'string' && topic.tags.trim() !== '') {
                tagsHtml = topic.tags.split(',').map(tag => `<span class="badge bg-secondary me-1">${tag.trim()}</span>`).join('');
            }

            allTopicsHtml += `
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>题目 ${index + 1}</span>
                        <div>${tagsHtml}</div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${renderMarkdown(topic.title)}</h5>
                        <ul class="list-group list-group-flush">
                        ${optionsHtml}
                        </ul>
                        <div class="mt-3">
                            <p><strong>答案:</strong> <span class="text-success fw-bold">${topic.answer}</span></p>
                            <p><strong>解析:</strong> ${renderMarkdown(topic.parse || '暂无解析')}</p>
                            <p class="small text-muted"><strong>来源:</strong> ${topic.source || '暂无来源'}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        previewContainer.innerHTML = allTopicsHtml;

        if (window.renderMathInElement) {
            renderMathInElement(previewContainer, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ]
            });
        }
    }

    function showError(message) {
        errorMessageDiv.textContent = message;
        errorMessageDiv.classList.remove('d-none');
        successMessageDiv.classList.add('d-none');
        showToast(message, 'error');
    }

    function showSuccess(message) {
        successMessageDiv.textContent = message;
        successMessageDiv.classList.remove('d-none');
        errorMessageDiv.classList.add('d-none');
        showToast(message, 'success');
    }


});
const exampleTopicData = [
    {
        "know_id": 252,
        "type": "choice",
        "title": "党的十八大以来，党中央对地方党委工作条例进行了首次修订，其主要目的是（    ）",
        "tags": "社会主义发展简史-第8章-中国特色社会主义进入新时代",
        "options": [
            {"key": "A", "name": "扩大地方党委的自主权"},
            {"key": "B", "name": "加强党中央的集中统一领导"},
            {"key": "C", "name": "减轻地方党委的工作负担"},
            {"key": "D", "name": "简化地方党委的议事程序"}
        ],
        "answer": "B",
        "source": "《社会主义发展简史》第302页",
        "parse": "新时代加强党内法规制度建设，一个核心目标就是确保政令畅通，维护党中央权威和集中统一领导。",
        "difficulty": 0.6
    },
    {
        "know_id": 247,
        "type": "multiple",
        "title": "20世纪20年代，在中国的城市里，居民区和商业区的分布开始出现一种新的趋势，即沿着（    ）发展。",
        "tags": "历史选择性必修2-经济与社会生活-现代交通运输的新变化",
        "options": [
            { "key": "A", "name": "城墙" },
            { "key": "B", "name": "河流" },
            { "key": "C", "name": "铁道和电车道" },
            { "key": "D", "name": "山脉" }
        ],
        "answer": "C",
        "source": "《历史选择性必修2-经济与社会生活》第13课",
        "parse": "课文引用的史料描述了天津市的发展趋势：“复次则沿铁道线，自有电气事业，则沿电车道而发展。”",
        "difficulty": 0.4
    }
];

function insertMathExample() {
    const mathExample = [
        {
            "know_id": 1,
            "type": "choice",
            "title": "以下哪个是正确的平方和公式？$\\sum_{i=1}^{n} i^2 = ?$",
            "tags": "数学,公式",
            "options": [
                {"key": "A", "name": "$\\frac{n(n+1)}{2}$"},
                {"key": "B", "name": "$\\frac{n(n+1)(2n+1)}{6}$"},
                {"key": "C", "name": "$\\frac{n^2(n+1)^2}{4}$"},
                {"key": "D", "name": "$n^3$"}
            ],
            "answer": "B",
            "source": "数学公式例题",
            "parse": "正确的平方和公式是：$$\\sum_{i=1}^{n} i^2 = \\frac{n(n+1)(2n+1)}{6}$$",
            "difficulty": 0.6
        }
    ];
    editor.setValue(JSON.stringify(mathExample, null, 2));
    showToast('已插入数学公式示例', 'info');
    validateAndPreview();
}