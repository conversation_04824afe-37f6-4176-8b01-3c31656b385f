/* 题目编辑页面样式 */
:root {
    --primary-color: #0066FF;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --hover-color: #f5f5f5;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 页面标题区域 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.header-actions .btn {
    margin-left: 0.5rem;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

/* 编辑表单区域 */
.edit-form-section {
    margin-bottom: 2rem;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
}

.card-title {
    font-weight: 600;
    color: var(--dark-color);
}

.card-body {
    padding: 1.5rem;
}

/* 表单控件样式 */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 255, 0.25);
}

/* 难度滑块 */
.form-range {
    margin: 1rem 0;
}

.difficulty-value {
    font-weight: 600;
    color: var(--primary-color);
    background-color: rgba(0, 102, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

/* 编辑器容器 */
.editor-container {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.quill-editor {
    min-height: 150px;
    background-color: white;
}

.editor-toolbar {
    background-color: var(--light-color);
    padding: 0.5rem 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-toolbar .btn {
    margin-right: 0.5rem;
    border-radius: 6px;
}

.char-count {
    font-size: 0.75rem;
    color: var(--secondary-color);
    font-weight: 500;
}

/* 选项区域 */
.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.option-checkbox {
    margin-right: 1rem;
    transform: scale(1.2);
}

.option-input {
    flex: 1;
    margin-right: 1rem;
}

.option-actions {
    display: flex;
    gap: 0.5rem;
}

.option-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 6px;
}

/* 答案区域 */
#answerSection {
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.answer-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.answer-checkbox-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: white;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-checkbox-item:hover {
    background-color: rgba(0, 102, 255, 0.1);
    border-color: var(--primary-color);
}

.answer-checkbox-item.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.answer-checkbox-item input {
    margin-right: 0.5rem;
}

/* 子题目区域 */
.sub-topic-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.sub-topic-header {
    background-color: var(--light-color);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content-between;
    align-items: center;
}

.sub-topic-title {
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.sub-topic-body {
    padding: 1rem;
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
}

.modal-title {
    font-weight: 600;
    color: var(--dark-color);
}

/* LaTeX预览 */
.latex-preview {
    min-height: 60px;
    background-color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.latex-preview:empty::before {
    content: "LaTeX预览将在这里显示";
    color: var(--secondary-color);
    font-style: italic;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.loading-text {
    margin-top: 1rem;
    color: var(--primary-color);
    font-weight: 500;
}

/* 拖拽排序 */
.sortable-item {
    cursor: move;
    transition: all 0.3s ease;
}

.sortable-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.sortable-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.drag-handle {
    cursor: grab;
    color: var(--secondary-color);
    margin-right: 0.5rem;
}

.drag-handle:active {
    cursor: grabbing;
}

/* 题型特定样式 */
.type-single .option-checkbox {
    border-radius: 50%;
}

.type-multiple .option-checkbox {
    border-radius: 4px;
}

.type-judge .answer-options {
    display: flex;
    gap: 1rem;
}

.type-judge .answer-option {
    flex: 1;
    text-align: center;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.type-judge .answer-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(0, 102, 255, 0.1);
}

.type-judge .answer-option.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        padding: 1rem 0;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .header-actions {
        margin-top: 1rem;
    }
    
    .col-lg-4,
    .col-lg-8 {
        margin-bottom: 1rem;
    }
    
    .option-item {
        flex-direction: column;
        align-items: stretch;
    }
    
    .option-checkbox {
        margin-bottom: 0.5rem;
    }
    
    .option-input {
        margin-bottom: 0.5rem;
    }
    
    .answer-checkbox-group {
        flex-direction: column;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Quill编辑器自定义样式 */
.ql-toolbar {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid var(--border-color) !important;
}

.ql-container {
    border: none !important;
    font-size: 14px;
}

.ql-editor {
    min-height: 120px;
    padding: 1rem;
}

.ql-editor.ql-blank::before {
    color: var(--secondary-color);
    font-style: italic;
}

/* 自定义滚动条 */
.card-body::-webkit-scrollbar {
    width: 8px;
}

.card-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.card-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
