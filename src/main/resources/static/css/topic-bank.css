/* 题库管理页面样式 */
:root {
    --primary-color: #0066FF;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --hover-color: #f5f5f5;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 页面标题区域 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.header-actions .btn {
    margin-left: 0.5rem;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

/* 筛选区域 */
.filter-section {
    margin-bottom: 1.5rem;
}

.filter-section .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.filter-section .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.filter-section .form-select,
.filter-section .form-control {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.filter-section .form-select:focus,
.filter-section .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 255, 0.25);
}

/* 工具栏区域 */
.toolbar-section {
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.batch-actions {
    display: flex;
    align-items: center;
}

.batch-actions .form-check-label {
    font-weight: 500;
    color: var(--dark-color);
}

.result-info {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* 表格区域 */
.table-section .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
}

.table tbody tr:hover {
    background-color: var(--hover-color);
}

/* 题目内容显示 */
.topic-title {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    color: var(--primary-color);
    text-decoration: none;
}

.topic-title:hover {
    text-decoration: underline;
}

/* 题型标签 */
.topic-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.type-single { background-color: #e3f2fd; color: #1976d2; }
.type-multiple { background-color: #f3e5f5; color: #7b1fa2; }
.type-judge { background-color: #e8f5e8; color: #388e3c; }
.type-fill { background-color: #fff3e0; color: #f57c00; }
.type-short { background-color: #fce4ec; color: #c2185b; }
.type-essay { background-color: #e0f2f1; color: #00695c; }
.type-group { background-color: #f1f8e9; color: #558b2f; }

/* 难度显示 */
.difficulty-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.difficulty-easy { background-color: #e8f5e8; color: #388e3c; }
.difficulty-medium { background-color: #fff3e0; color: #f57c00; }
.difficulty-hard { background-color: #ffebee; color: #d32f2f; }

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 6px;
    border: none;
}

.btn-view {
    background-color: var(--info-color);
    color: white;
}

.btn-edit {
    background-color: var(--warning-color);
    color: white;
}

.btn-delete {
    background-color: var(--danger-color);
    color: white;
}

.action-buttons .btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* 分页区域 */
.pagination-section {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid var(--border-color);
    color: var(--primary-color);
    padding: 0.5rem 0.75rem;
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
}

.modal-title {
    font-weight: 600;
    color: var(--dark-color);
}

/* 题目详情显示 */
.topic-detail-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.topic-detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.topic-detail-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.topic-detail-content {
    color: var(--secondary-color);
    line-height: 1.6;
}

/* 选项显示 */
.topic-options {
    list-style: none;
    padding: 0;
}

.topic-options li {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: var(--light-color);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.topic-options li.correct {
    background-color: #e8f5e8;
    border-left-color: var(--success-color);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.loading-text {
    margin-top: 1rem;
    color: var(--primary-color);
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        padding: 1rem 0;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .header-actions {
        margin-top: 1rem;
    }
    
    .filter-section .row > div {
        margin-bottom: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 自定义滚动条 */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
