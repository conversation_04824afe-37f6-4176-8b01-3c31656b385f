package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户权限实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_permissions")
public class UserPermission {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 权限代码
     */
    @TableField("permission_code")
    private String permissionCode;

    /**
     * 权限名称
     */
    @TableField("permission_name")
    private String permissionName;

    /**
     * 资源类型
     */
    @TableField("resource_type")
    private String resourceType;

    /**
     * 资源路径
     */
    @TableField("resource_path")
    private String resourcePath;

    /**
     * 授权人ID
     */
    @TableField("granted_by")
    private Long grantedBy;

    /**
     * 授权时间
     */
    @TableField("granted_time")
    private LocalDateTime grantedTime;

    /**
     * 过期时间（NULL表示永不过期）
     */
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    /**
     * 是否激活：0-禁用，1-启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 资源类型枚举
     */
    public enum ResourceType {
        MENU("MENU", "菜单"),
        API("API", "接口"),
        BUTTON("BUTTON", "按钮");

        private final String code;
        private final String description;

        ResourceType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
