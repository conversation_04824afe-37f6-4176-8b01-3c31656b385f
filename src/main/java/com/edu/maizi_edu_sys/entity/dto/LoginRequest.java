package com.edu.maizi_edu_sys.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class LoginRequest {
    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "密码不能为空")
    private String password;

    @NotBlank(message = "验证码不能为空")
    private String captchaCode;

    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    private String ip;

    private Boolean rememberMe = false;
}