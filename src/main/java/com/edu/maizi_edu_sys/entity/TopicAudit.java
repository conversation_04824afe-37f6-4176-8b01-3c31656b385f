package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 题目审核实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("topic_audit")
public class TopicAudit {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 提交用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 知识点ID
     */
    @TableField("know_id")
    private Integer knowId;

    /**
     * 题目类型
     */
    @TableField("type")
    private String type;

    /**
     * 题目标题
     */
    @TableField("title")
    private String title;

    /**
     * 选项数据(json)
     */
    @TableField("options")
    private String options;

    /**
     * 组合题数据(json)
     */
    @TableField("subs")
    private String subs;

    /**
     * 答案
     */
    @TableField("answer")
    private String answer;

    /**
     * 答案解析
     */
    @TableField("parse")
    private String parse;

    /**
     * 分值
     */
    @TableField("score")
    private Integer score;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 题目难度
     */
    @TableField("difficulty")
    private Double difficulty;

    /**
     * 细分知识点标签
     */
    @TableField("tags")
    private String tags;

    /**
     * 审核状态：0-待审核，1-审核通过，2-审核拒绝
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 审核员ID
     */
    @TableField("auditor_id")
    private Long auditorId;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核意见
     */
    @TableField("audit_comment")
    private String auditComment;

    /**
     * 是否自动审核通过：0-否，1-是
     */
    @TableField("auto_approved")
    private Boolean autoApproved;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 审核状态枚举
     */
    public enum AuditStatus {
        PENDING(0, "待审核"),
        APPROVED(1, "审核通过"),
        REJECTED(2, "审核拒绝");

        private final Integer code;
        private final String description;

        AuditStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static AuditStatus fromCode(Integer code) {
            for (AuditStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return PENDING;
        }
    }
}
