package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 被拒绝题目实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("topic_rejected")
public class TopicRejected {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 审核记录ID
     */
    @TableField("audit_id")
    private Long auditId;

    /**
     * 提交用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 知识点ID
     */
    @TableField("know_id")
    private Integer knowId;

    /**
     * 题目类型
     */
    @TableField("type")
    private String type;

    /**
     * 题目标题
     */
    @TableField("title")
    private String title;

    /**
     * 选项数据(json)
     */
    @TableField("options")
    private String options;

    /**
     * 组合题数据(json)
     */
    @TableField("subs")
    private String subs;

    /**
     * 答案
     */
    @TableField("answer")
    private String answer;

    /**
     * 答案解析
     */
    @TableField("parse")
    private String parse;

    /**
     * 分值
     */
    @TableField("score")
    private Integer score;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 题目难度
     */
    @TableField("difficulty")
    private Double difficulty;

    /**
     * 细分知识点标签
     */
    @TableField("tags")
    private String tags;

    /**
     * 审核员ID
     */
    @TableField("auditor_id")
    private Long auditorId;

    /**
     * 拒绝原因
     */
    @TableField("reject_reason")
    private String rejectReason;

    /**
     * 拒绝时间
     */
    @TableField("reject_time")
    private LocalDateTime rejectTime;

    /**
     * 原始提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
