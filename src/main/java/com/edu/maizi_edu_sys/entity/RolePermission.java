package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 角色权限实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("role_permissions")
public class RolePermission {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色ID：1-普通用户，2-管理员，3-超级管理员
     */
    @TableField("role_id")
    private Integer roleId;

    /**
     * 权限代码
     */
    @TableField("permission_code")
    private String permissionCode;

    /**
     * 权限名称
     */
    @TableField("permission_name")
    private String permissionName;

    /**
     * 资源类型
     */
    @TableField("resource_type")
    private String resourceType;

    /**
     * 资源路径
     */
    @TableField("resource_path")
    private String resourcePath;

    /**
     * 权限描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否激活：0-禁用，1-启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 用户角色枚举
     */
    public enum UserRole {
        ADMIN(1, "管理员"),
        NORMAL_USER(2, "普通用户"),
        TEACHER(3, "教师");

        private final Integer code;
        private final String description;

        UserRole(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static UserRole fromCode(Integer code) {
            for (UserRole role : values()) {
                if (role.code.equals(code)) {
                    return role;
                }
            }
            return ADMIN;
        }
    }

    /**
     * 资源类型枚举
     */
    public enum ResourceType {
        MENU("MENU", "菜单"),
        API("API", "接口"),
        BUTTON("BUTTON", "按钮");

        private final String code;
        private final String description;

        ResourceType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
