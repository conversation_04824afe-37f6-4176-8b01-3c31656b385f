package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统消息实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("system_messages")
public class SystemMessage {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 接收用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 发送者ID（系统消息为NULL）
     */
    @TableField("sender_id")
    private Long senderId;

    /**
     * 消息类型
     */
    @TableField("message_type")
    private String messageType;

    /**
     * 消息标题
     */
    @TableField("title")
    private String title;

    /**
     * 消息内容
     */
    @TableField("content")
    private String content;

    /**
     * 关联ID（如审核记录ID）
     */
    @TableField("related_id")
    private Long relatedId;

    /**
     * 关联类型
     */
    @TableField("related_type")
    private String relatedType;

    /**
     * 是否已读：0-未读，1-已读
     */
    @TableField("is_read")
    private Boolean isRead;

    /**
     * 阅读时间
     */
    @TableField("read_time")
    private LocalDateTime readTime;

    /**
     * 优先级：1-普通，2-重要，3-紧急
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        AUDIT_APPROVED("AUDIT_APPROVED", "审核通过"),
        AUDIT_REJECTED("AUDIT_REJECTED", "审核拒绝"),
        SYSTEM_NOTICE("SYSTEM_NOTICE", "系统通知");

        private final String code;
        private final String description;

        MessageType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 关联类型枚举
     */
    public enum RelatedType {
        TOPIC_AUDIT("TOPIC_AUDIT", "题目审核");

        private final String code;
        private final String description;

        RelatedType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 优先级枚举
     */
    public enum Priority {
        NORMAL(1, "普通"),
        IMPORTANT(2, "重要"),
        URGENT(3, "紧急");

        private final Integer code;
        private final String description;

        Priority(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
