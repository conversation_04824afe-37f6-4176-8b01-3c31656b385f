package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.TopicRejected;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 被拒绝题目Mapper接口
 */
@Mapper
public interface TopicRejectedMapper extends BaseMapper<TopicRejected> {

    /**
     * 分页查询用户被拒绝的题目
     */
    @Select("SELECT tr.*, u.username as submitter_username, au.username as auditor_username " +
            "FROM topic_rejected tr " +
            "LEFT JOIN user u ON tr.user_id = u.id " +
            "LEFT JOIN user au ON tr.auditor_id = au.id " +
            "WHERE tr.user_id = #{userId} " +
            "ORDER BY tr.reject_time DESC")
    IPage<TopicRejected> selectUserRejectedTopics(Page<TopicRejected> page, @Param("userId") Long userId);

    /**
     * 查询用户被拒绝题目数量
     */
    @Select("SELECT COUNT(*) FROM topic_rejected WHERE user_id = #{userId}")
    Long countUserRejectedTopics(@Param("userId") Long userId);
}
