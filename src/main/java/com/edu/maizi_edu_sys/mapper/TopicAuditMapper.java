package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 题目审核Mapper接口
 */
@Mapper
public interface TopicAuditMapper extends BaseMapper<TopicAudit> {

    /**
     * 分页查询审核列表
     */
    @Select("SELECT ta.*, u.username as submitter_username, au.username as auditor_username " +
            "FROM topic_audit ta " +
            "LEFT JOIN user u ON ta.user_id = u.id " +
            "LEFT JOIN user au ON ta.auditor_id = au.id " +
            "WHERE 1=1 " +
            "${ew.customSqlSegment}")
    IPage<TopicAudit> selectAuditPageWithUsernames(Page<TopicAudit> page, @Param("ew") com.baomidou.mybatisplus.core.conditions.Wrapper<TopicAudit> queryWrapper);

    /**
     * 查询待审核的题目数量
     */
    @Select("SELECT COUNT(*) FROM topic_audit WHERE audit_status = 0")
    Long countPendingAudits();

    /**
     * 查询用户提交的题目统计
     */
    @Select("SELECT COUNT(*) FROM topic_audit WHERE user_id = #{userId} AND DATE(submit_time) = CURDATE()")
    Long countUserTodaySubmissions(@Param("userId") Long userId);

    /**
     * 查询超过一周未审核的题目
     */
    @Select("SELECT * FROM topic_audit WHERE audit_status = 0 AND submit_time < #{weekAgo}")
    List<TopicAudit> selectOverdueAudits(@Param("weekAgo") LocalDateTime weekAgo);

    /**
     * 批量更新超时题目为自动通过
     */
    @Select("UPDATE topic_audit SET audit_status = 1, auto_approved = 1, audit_time = NOW(), audit_comment = '系统自动审核通过（超过7天未处理）' WHERE audit_status = 0 AND submit_time < #{weekAgo}")
    int batchAutoApproveOverdue(@Param("weekAgo") LocalDateTime weekAgo);

    /**
     * 查询用户的审核统计
     */
    @Select("SELECT " +
            "COUNT(*) as total, " +
            "SUM(CASE WHEN audit_status = 0 THEN 1 ELSE 0 END) as pending, " +
            "SUM(CASE WHEN audit_status = 1 THEN 1 ELSE 0 END) as approved, " +
            "SUM(CASE WHEN audit_status = 2 THEN 1 ELSE 0 END) as rejected " +
            "FROM topic_audit WHERE user_id = #{userId}")
    TopicAuditStats getUserAuditStats(@Param("userId") Long userId);

    /**
     * 审核统计内部类
     */
    class TopicAuditStats {
        private Long total;
        private Long pending;
        private Long approved;
        private Long rejected;

        // getters and setters
        public Long getTotal() { return total; }
        public void setTotal(Long total) { this.total = total; }
        public Long getPending() { return pending; }
        public void setPending(Long pending) { this.pending = pending; }
        public Long getApproved() { return approved; }
        public void setApproved(Long approved) { this.approved = approved; }
        public Long getRejected() { return rejected; }
        public void setRejected(Long rejected) { this.rejected = rejected; }
    }
}
