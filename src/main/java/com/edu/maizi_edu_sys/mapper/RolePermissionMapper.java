package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.RolePermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色权限Mapper接口
 */
@Mapper
public interface RolePermissionMapper extends BaseMapper<RolePermission> {

    /**
     * 查询角色的所有权限
     */
    @Select("SELECT * FROM role_permissions WHERE role_id = #{roleId} AND is_active = 1")
    List<RolePermission> selectRolePermissions(@Param("roleId") Integer roleId);

    /**
     * 检查角色是否有特定权限
     */
    @Select("SELECT COUNT(*) > 0 FROM role_permissions WHERE role_id = #{roleId} AND permission_code = #{permissionCode} AND is_active = 1")
    boolean hasRolePermission(@Param("roleId") Integer roleId, @Param("permissionCode") String permissionCode);

    /**
     * 查询角色的API权限路径
     */
    @Select("SELECT resource_path FROM role_permissions WHERE role_id = #{roleId} AND resource_type = 'API' AND is_active = 1")
    List<String> selectRoleApiPaths(@Param("roleId") Integer roleId);

    /**
     * 查询所有权限代码
     */
    @Select("SELECT DISTINCT permission_code FROM role_permissions WHERE is_active = 1")
    List<String> selectAllPermissionCodes();
}
