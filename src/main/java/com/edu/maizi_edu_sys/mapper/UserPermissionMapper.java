package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.UserPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户权限Mapper接口
 */
@Mapper
public interface UserPermissionMapper extends BaseMapper<UserPermission> {

    /**
     * 查询用户的所有权限
     */
    @Select("SELECT * FROM user_permissions WHERE user_id = #{userId} AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())")
    List<UserPermission> selectUserPermissions(@Param("userId") Long userId);

    /**
     * 检查用户是否有特定权限
     */
    @Select("SELECT COUNT(*) > 0 FROM user_permissions WHERE user_id = #{userId} AND permission_code = #{permissionCode} AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())")
    boolean hasPermission(@Param("userId") Long userId, @Param("permissionCode") String permissionCode);

    /**
     * 查询用户的API权限路径
     */
    @Select("SELECT resource_path FROM user_permissions WHERE user_id = #{userId} AND resource_type = 'API' AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())")
    List<String> selectUserApiPaths(@Param("userId") Long userId);
}
