package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.SystemMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 系统消息Mapper接口
 */
@Mapper
public interface SystemMessageMapper extends BaseMapper<SystemMessage> {

    /**
     * 分页查询用户消息
     */
    @Select("SELECT sm.*, u.username as sender_username " +
            "FROM system_messages sm " +
            "LEFT JOIN user u ON sm.sender_id = u.id " +
            "WHERE sm.user_id = #{userId} " +
            "ORDER BY sm.priority DESC, sm.created_at DESC")
    IPage<SystemMessage> selectUserMessages(Page<SystemMessage> page, @Param("userId") Long userId);

    /**
     * 查询用户未读消息数量
     */
    @Select("SELECT COUNT(*) FROM system_messages WHERE user_id = #{userId} AND is_read = 0")
    Long countUnreadMessages(@Param("userId") Long userId);

    /**
     * 标记消息为已读
     */
    @Update("UPDATE system_messages SET is_read = 1, read_time = NOW() WHERE id = #{messageId} AND user_id = #{userId}")
    int markAsRead(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 批量标记消息为已读
     */
    @Update("UPDATE system_messages SET is_read = 1, read_time = NOW() WHERE user_id = #{userId} AND is_read = 0")
    int markAllAsRead(@Param("userId") Long userId);

    /**
     * 查询最近的系统通知
     */
    @Select("SELECT * FROM system_messages WHERE message_type = 'SYSTEM_NOTICE' AND user_id = #{userId} ORDER BY created_at DESC LIMIT #{limit}")
    java.util.List<SystemMessage> selectRecentNotices(@Param("userId") Long userId, @Param("limit") int limit);
}
