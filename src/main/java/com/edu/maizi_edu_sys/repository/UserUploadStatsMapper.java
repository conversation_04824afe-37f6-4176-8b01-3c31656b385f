package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.UserUploadStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户上传统计Mapper
 */
@Mapper
public interface UserUploadStatsMapper extends BaseMapper<UserUploadStats> {

    /**
     * 增加用户当日上传数量
     * @param userId 用户ID
     * @param uploadDate 上传日期
     * @param count 增加数量
     */
    @Update("INSERT INTO user_upload_stats (user_id, upload_date, topic_count, created_at, updated_at) " +
            "VALUES (#{userId}, #{uploadDate}, #{count}, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "topic_count = topic_count + #{count}, updated_at = NOW()")
    void incrementUserUploadCount(@Param("userId") Long userId, 
                                 @Param("uploadDate") LocalDate uploadDate, 
                                 @Param("count") Integer count);

    /**
     * 获取用户指定日期的上传数量
     * @param userId 用户ID
     * @param uploadDate 上传日期
     * @return 上传数量
     */
    @Select("SELECT COALESCE(topic_count, 0) FROM user_upload_stats " +
            "WHERE user_id = #{userId} AND upload_date = #{uploadDate}")
    Integer getUserUploadCountByDate(@Param("userId") Long userId, 
                                   @Param("uploadDate") LocalDate uploadDate);

    /**
     * 获取用户指定日期范围内的上传统计
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    @Select("SELECT upload_date, topic_count FROM user_upload_stats " +
            "WHERE user_id = #{userId} AND upload_date BETWEEN #{startDate} AND #{endDate} " +
            "ORDER BY upload_date DESC")
    List<Map<String, Object>> getUserUploadStatsByDateRange(@Param("userId") Long userId,
                                                           @Param("startDate") LocalDate startDate,
                                                           @Param("endDate") LocalDate endDate);

    /**
     * 获取用户总上传数量
     * @param userId 用户ID
     * @return 总上传数量
     */
    @Select("SELECT COALESCE(SUM(topic_count), 0) FROM user_upload_stats WHERE user_id = #{userId}")
    Integer getUserTotalUploadCount(@Param("userId") Long userId);

    /**
     * 获取所有用户指定日期的上传统计（管理员用）
     * @param uploadDate 上传日期
     * @return 统计数据列表
     */
    @Select("SELECT u.username, s.topic_count, s.upload_date " +
            "FROM user_upload_stats s " +
            "LEFT JOIN user u ON s.user_id = u.id " +
            "WHERE s.upload_date = #{uploadDate} " +
            "ORDER BY s.topic_count DESC")
    List<Map<String, Object>> getAllUsersUploadStatsByDate(@Param("uploadDate") LocalDate uploadDate);

    /**
     * 获取指定日期范围内的总上传统计（管理员用）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    @Select("SELECT " +
            "DATE(upload_date) as date, " +
            "SUM(topic_count) as total_count, " +
            "COUNT(DISTINCT user_id) as user_count " +
            "FROM user_upload_stats " +
            "WHERE upload_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(upload_date) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> getTotalUploadStatsByDateRange(@Param("startDate") LocalDate startDate,
                                                            @Param("endDate") LocalDate endDate);

    /**
     * 获取用户排行榜（指定日期范围）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行榜数据
     */
    @Select("SELECT " +
            "u.username, " +
            "u.id as user_id, " +
            "SUM(s.topic_count) as total_count " +
            "FROM user_upload_stats s " +
            "LEFT JOIN user u ON s.user_id = u.id " +
            "WHERE s.upload_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY s.user_id, u.username " +
            "ORDER BY total_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getUserUploadRanking(@Param("startDate") LocalDate startDate,
                                                  @Param("endDate") LocalDate endDate,
                                                  @Param("limit") Integer limit);

    /**
     * 获取最近N天的上传趋势
     * @param userId 用户ID（可选，为null时获取全站数据）
     * @param days 天数
     * @return 趋势数据
     */
    @Select("<script>" +
            "SELECT " +
            "upload_date, " +
            "<if test='userId != null'>" +
            "topic_count " +
            "</if>" +
            "<if test='userId == null'>" +
            "SUM(topic_count) as topic_count " +
            "</if>" +
            "FROM user_upload_stats " +
            "WHERE upload_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "<if test='userId != null'>" +
            "AND user_id = #{userId} " +
            "</if>" +
            "<if test='userId == null'>" +
            "GROUP BY upload_date " +
            "</if>" +
            "ORDER BY upload_date ASC" +
            "</script>")
    List<Map<String, Object>> getUploadTrend(@Param("userId") Long userId, @Param("days") Integer days);
}
