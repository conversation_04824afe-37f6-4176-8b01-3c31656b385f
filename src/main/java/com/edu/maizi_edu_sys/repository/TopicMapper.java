package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.Topic;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 题目数据访问层
 * <AUTHOR>
 */
@Mapper
public interface TopicMapper extends BaseMapper<Topic> {
    @Select("SELECT id FROM topic_bak WHERE know_id = #{knowId} AND type = #{type} " +
           "AND difficulty BETWEEN #{difficulty} - 0.1 AND #{difficulty} + 0.1 " +
           "ORDER BY id")
    List<Integer> findIdsByKnowledgeAndTypeAndDifficulty(
        @Param("knowId") Integer knowId,
        @Param("type") String type,
        @Param("difficulty") double difficulty);

    /**
     * 查询题目总数
     */
    @Select("SELECT COUNT(*) FROM topic_bak")
    int countAllTopics();

    /**
     * 获取任意一道题目
     */
    @Select("SELECT * FROM topic_bak LIMIT 1")
    Topic findAnyTopic();

    /**
     * 根据知识点ID查询题目列表
     */
    @Select("SELECT * FROM topic_bak WHERE know_id = #{knowId}")
    List<Topic> selectFromBakByKnowId(@Param("knowId") Integer knowId);

    /**
     * 根据题型查询题目列表（全局查询，不限知识点）
     */
    @Select("SELECT * FROM topic_bak WHERE type = #{type}")
    List<Topic> selectByType(@Param("type") String type);

    /**
     * 根据知识点ID查询题目ID列表
     */
    @Select("SELECT id FROM topic_bak WHERE know_id = #{knowId}")
    List<Integer> findAnyTopicsByKnowledgeId(@Param("knowId") Integer knowId);

    @Select("SELECT id FROM topic_bak WHERE know_id = #{knowId} AND type = #{type} " +
            "AND difficulty BETWEEN #{difficulty} - 0.2 AND #{difficulty} + 0.2 " +
            "ORDER BY id")
    List<Integer> findIdsByKnowledgeAndTypeWithWiderRange(
        @Param("knowId") Integer knowId,
        @Param("type") String type,
        @Param("difficulty") double difficulty);

    @Select("SELECT COUNT(*) FROM topic_bak WHERE know_id = #{knowId}")
    long countFromBakByKnowId(Integer knowId);

    /**
     * 根据知识点ID统计各题型的数量
     *
     * @param knowledgeIds 知识点ID列表
     * @return 各题型的数量统计
     */
    List<Map<String, Object>> countTopicsByTypeForKnowledgePoints(@Param("knowIds") List<Integer> knowledgeIds);

    /**
     * 统计一个知识点内各个题型的题目数量
     * @param knowId 知识点ID
     * @return 各题型的数量统计，以List<Map>形式返回
     */
    List<Map<String, Object>> countTopicsByTypeForKnowledgePoint(@Param("knowId") Integer knowId);

    /**
     * 获取题目上传统计数据
     * @param type      统计类型 ('day', 'week', 'month')
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计数据列表
     */
    List<Map<String, Object>> getTopicStatistics(
            @Param("type") String type,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

}