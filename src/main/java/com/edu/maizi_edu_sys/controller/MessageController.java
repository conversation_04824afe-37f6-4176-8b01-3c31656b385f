package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.SystemMessage;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.SystemMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/messages")
@RequiredArgsConstructor
public class MessageController {

    private final SystemMessageService systemMessageService;
    private final AuthService authService;

    /**
     * 获取用户消息列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<IPage<SystemMessage>>> getUserMessages(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) Boolean isRead) {
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            IPage<SystemMessage> messages = systemMessageService.getUserMessages(currentUserId, pageNum, pageSize, isRead);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取消息列表成功", messages));
        } catch (Exception e) {
            log.error("获取消息列表失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取消息列表失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread-count")
    public ResponseEntity<ApiResponse<Long>> getUnreadMessageCount() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            Long count = systemMessageService.getUnreadMessageCount(currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取未读消息数量成功", count));
        } catch (Exception e) {
            log.error("获取未读消息数量失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取未读消息数量失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取消息详情
     */
    @GetMapping("/{messageId}")
    public ResponseEntity<ApiResponse<SystemMessage>> getMessageDetail(@PathVariable Long messageId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            SystemMessage message = systemMessageService.getMessageDetail(messageId, currentUserId);
            if (message == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(new ApiResponse<>(true, "获取消息详情成功", message));
        } catch (Exception e) {
            log.error("获取消息详情失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取消息详情失败: " + e.getMessage(), null));
        }
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/{messageId}/read")
    public ResponseEntity<ApiResponse<Void>> markMessageAsRead(@PathVariable Long messageId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            systemMessageService.markMessageAsRead(messageId, currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "标记已读成功", null));
        } catch (Exception e) {
            log.error("标记消息已读失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "标记已读失败: " + e.getMessage(), null));
        }
    }

    /**
     * 标记所有消息为已读
     */
    @PostMapping("/read-all")
    public ResponseEntity<ApiResponse<Void>> markAllMessagesAsRead() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            systemMessageService.markAllMessagesAsRead(currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "全部标记已读成功", null));
        } catch (Exception e) {
            log.error("标记所有消息已读失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "全部标记已读失败: " + e.getMessage(), null));
        }
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    public ResponseEntity<ApiResponse<Void>> deleteMessage(@PathVariable Long messageId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            systemMessageService.deleteMessage(messageId, currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "删除消息成功", null));
        } catch (Exception e) {
            log.error("删除消息失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "删除消息失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取最近的系统通知
     */
    @GetMapping("/recent-notices")
    public ResponseEntity<ApiResponse<List<SystemMessage>>> getRecentNotices(
            @RequestParam(defaultValue = "5") int limit) {
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            List<SystemMessage> notices = systemMessageService.getRecentNotices(currentUserId, limit);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取最近通知成功", notices));
        } catch (Exception e) {
            log.error("获取最近通知失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取最近通知失败: " + e.getMessage(), null));
        }
    }
}
