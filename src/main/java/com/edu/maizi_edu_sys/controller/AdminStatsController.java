package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.TopicAuditService;
import com.edu.maizi_edu_sys.service.TopicService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 管理员统计数据控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/stats")
@RequiredArgsConstructor
public class AdminStatsController {

    private final TopicAuditService topicAuditService;
    private final TopicService topicService;
    private final AuthService authService;
    private final PermissionService permissionService;

    /**
     * 获取审核统计数据
     */
    @GetMapping("/audit")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAuditStats() {
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "SYSTEM_STATS")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            Map<String, Object> stats = new HashMap<>();
            
            // 获取待审核数量
            Long pendingCount = topicAuditService.getPendingAuditCount();
            stats.put("pendingCount", pendingCount);
            
            // 这里可以添加更多统计数据
            // 比如今日审核数量、本周审核数量等
            
            return ResponseEntity.ok(new ApiResponse<>(true, "获取审核统计成功", stats));
        } catch (Exception e) {
            log.error("获取审核统计失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取审核统计失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取题库统计数据
     */
    @GetMapping("/topics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTopicStats() {
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "SYSTEM_STATS")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            Map<String, Object> stats = new HashMap<>();
            
            // 获取题目总数
            long totalTopics = topicService.count();
            stats.put("totalTopics", totalTopics);
            
            // 按类型统计题目数量
            Map<String, Long> typeStats = getTopicCountByType();
            stats.put("typeStats", typeStats);
            
            // 按难度统计题目数量
            Map<String, Long> difficultyStats = getTopicCountByDifficulty();
            stats.put("difficultyStats", difficultyStats);
            
            return ResponseEntity.ok(new ApiResponse<>(true, "获取题库统计成功", stats));
        } catch (Exception e) {
            log.error("获取题库统计失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取题库统计失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取审核趋势数据
     */
    @GetMapping("/audit/trend")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAuditTrend(
            @RequestParam(defaultValue = "day") String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "SYSTEM_STATS")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            // 设置默认日期范围
            LocalDate start = startDate != null ? LocalDate.parse(startDate) : LocalDate.now().minusDays(30);
            LocalDate end = endDate != null ? LocalDate.parse(endDate) : LocalDate.now();
            
            Map<String, Object> trendData = generateAuditTrendData(type, start, end);
            
            return ResponseEntity.ok(new ApiResponse<>(true, "获取审核趋势成功", trendData));
        } catch (Exception e) {
            log.error("获取审核趋势失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取审核趋势失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取用户活跃度统计
     */
    @GetMapping("/users/activity")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserActivityStats() {
        try {
            // 检查权限
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "用户未登录", null));
            }
            
            if (!permissionService.hasPermission(currentUserId, "USER_MANAGE")) {
                return ResponseEntity.status(403)
                        .body(new ApiResponse<>(false, "无权限访问", null));
            }
            
            Map<String, Object> activityStats = new HashMap<>();
            
            // 这里可以添加用户活跃度相关的统计
            // 比如今日活跃用户数、本周新增用户数等
            
            return ResponseEntity.ok(new ApiResponse<>(true, "获取用户活跃度统计成功", activityStats));
        } catch (Exception e) {
            log.error("获取用户活跃度统计失败", e);
            return ResponseEntity.status(500)
                    .body(new ApiResponse<>(false, "获取用户活跃度统计失败: " + e.getMessage(), null));
        }
    }

    /**
     * 按类型统计题目数量
     */
    private Map<String, Long> getTopicCountByType() {
        Map<String, Long> typeStats = new HashMap<>();
        
        // 这里应该调用实际的数据库查询
        // 暂时返回模拟数据
        typeStats.put("choice", 150L);
        typeStats.put("multiple", 80L);
        typeStats.put("judge", 60L);
        typeStats.put("fill", 90L);
        typeStats.put("short", 45L);
        
        return typeStats;
    }

    /**
     * 按难度统计题目数量
     */
    private Map<String, Long> getTopicCountByDifficulty() {
        Map<String, Long> difficultyStats = new HashMap<>();
        
        // 这里应该调用实际的数据库查询
        // 暂时返回模拟数据
        difficultyStats.put("easy", 200L);
        difficultyStats.put("medium", 180L);
        difficultyStats.put("hard", 45L);
        
        return difficultyStats;
    }

    /**
     * 生成审核趋势数据
     */
    private Map<String, Object> generateAuditTrendData(String type, LocalDate start, LocalDate end) {
        Map<String, Object> trendData = new HashMap<>();
        
        List<String> labels = new ArrayList<>();
        List<Integer> submitData = new ArrayList<>();
        List<Integer> approveData = new ArrayList<>();
        List<Integer> rejectData = new ArrayList<>();
        
        // 根据类型生成不同粒度的数据
        switch (type) {
            case "day":
                generateDailyTrendData(start, end, labels, submitData, approveData, rejectData);
                break;
            case "week":
                generateWeeklyTrendData(start, end, labels, submitData, approveData, rejectData);
                break;
            case "month":
                generateMonthlyTrendData(start, end, labels, submitData, approveData, rejectData);
                break;
        }
        
        trendData.put("labels", labels);

        // 创建数据集
        List<Map<String, Object>> datasets = new ArrayList<>();

        // 提交数据集
        Map<String, Object> submitDataset = new HashMap<>();
        submitDataset.put("label", "提交");
        submitDataset.put("data", submitData);
        submitDataset.put("borderColor", "#007AFF");
        submitDataset.put("backgroundColor", "rgba(0, 122, 255, 0.1)");
        datasets.add(submitDataset);

        // 通过数据集
        Map<String, Object> approveDataset = new HashMap<>();
        approveDataset.put("label", "通过");
        approveDataset.put("data", approveData);
        approveDataset.put("borderColor", "#34C759");
        approveDataset.put("backgroundColor", "rgba(52, 199, 89, 0.1)");
        datasets.add(approveDataset);

        // 拒绝数据集
        Map<String, Object> rejectDataset = new HashMap<>();
        rejectDataset.put("label", "拒绝");
        rejectDataset.put("data", rejectData);
        rejectDataset.put("borderColor", "#FF3B30");
        rejectDataset.put("backgroundColor", "rgba(255, 59, 48, 0.1)");
        datasets.add(rejectDataset);

        trendData.put("datasets", datasets);
        
        return trendData;
    }

    /**
     * 生成按日统计的趋势数据
     */
    private void generateDailyTrendData(LocalDate start, LocalDate end, List<String> labels, 
                                      List<Integer> submitData, List<Integer> approveData, List<Integer> rejectData) {
        LocalDate current = start;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        
        while (!current.isAfter(end)) {
            labels.add(current.format(formatter));
            
            // 这里应该查询实际数据库数据
            // 暂时使用随机数据模拟
            submitData.add((int) (Math.random() * 20) + 5);
            approveData.add((int) (Math.random() * 15) + 3);
            rejectData.add((int) (Math.random() * 5) + 1);
            
            current = current.plusDays(1);
        }
    }

    /**
     * 生成按周统计的趋势数据
     */
    private void generateWeeklyTrendData(LocalDate start, LocalDate end, List<String> labels,
                                       List<Integer> submitData, List<Integer> approveData, List<Integer> rejectData) {
        LocalDate current = start;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        
        while (!current.isAfter(end)) {
            labels.add(current.format(formatter) + "周");
            
            // 这里应该查询实际数据库数据
            submitData.add((int) (Math.random() * 100) + 30);
            approveData.add((int) (Math.random() * 80) + 20);
            rejectData.add((int) (Math.random() * 20) + 5);
            
            current = current.plusWeeks(1);
        }
    }

    /**
     * 生成按月统计的趋势数据
     */
    private void generateMonthlyTrendData(LocalDate start, LocalDate end, List<String> labels,
                                        List<Integer> submitData, List<Integer> approveData, List<Integer> rejectData) {
        LocalDate current = start;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        
        while (!current.isAfter(end)) {
            labels.add(current.format(formatter));
            
            // 这里应该查询实际数据库数据
            submitData.add((int) (Math.random() * 400) + 100);
            approveData.add((int) (Math.random() * 300) + 80);
            rejectData.add((int) (Math.random() * 50) + 10);
            
            current = current.plusMonths(1);
        }
    }
}
