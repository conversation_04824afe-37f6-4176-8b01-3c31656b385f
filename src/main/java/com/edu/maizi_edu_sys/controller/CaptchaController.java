package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 验证码控制器
 */
@RestController
@RequestMapping("/api/captcha")
@RequiredArgsConstructor
@Slf4j
public class CaptchaController {

    private final CaptchaService captchaService;

    /**
     * 生成验证码
     */
    @GetMapping("/generate")
    public ApiResponse<?> generateCaptcha() {
        log.debug("请求生成验证码");
        return captchaService.generateCaptcha();
    }

    /**
     * 刷新验证码
     */
    @PostMapping("/refresh")
    public ApiResponse<?> refreshCaptcha(@RequestBody(required = false) Map<String, String> request) {
        String sessionId = null;
        if (request != null) {
            sessionId = request.get("sessionId");
        }
        log.debug("请求刷新验证码，会话ID: {}", sessionId);
        return captchaService.refreshCaptcha(sessionId);
    }

    /**
     * 验证验证码（用于测试）
     */
    @PostMapping("/verify")
    public ApiResponse<?> verifyCaptcha(@RequestBody Map<String, String> request) {
        String sessionId = request.get("sessionId");
        String captchaCode = request.get("captchaCode");
        
        log.debug("请求验证验证码，会话ID: {}, 验证码: {}", sessionId, captchaCode);
        
        boolean isValid = captchaService.verifyCaptcha(sessionId, captchaCode);
        
        if (isValid) {
            return ApiResponse.success("验证码验证成功");
        } else {
            return ApiResponse.error("验证码验证失败");
        }
    }
}
