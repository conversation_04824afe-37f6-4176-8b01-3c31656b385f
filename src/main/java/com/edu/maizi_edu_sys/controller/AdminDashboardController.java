package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.TopicService;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.service.UserUploadStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员后台控制器
 */
@Controller
@RequestMapping("/admin")
@RequiredArgsConstructor
@Slf4j
public class AdminDashboardController {

    private final AuthService authService;
    private final PermissionService permissionService;
    private final TopicService topicService;
    private final UserService userService;
    private final UserUploadStatsService userUploadStatsService;

    /**
     * 管理员后台首页
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        log.info("访问管理员后台首页");
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return "redirect:/admin/login";
            }

            // 检查管理员权限
            if (!permissionService.isAdmin(currentUserId)) {
                model.addAttribute("error", "您没有管理员权限");
                return "error/403";
            }

            // 可以在这里添加一些基础数据到model中
            model.addAttribute("pageTitle", "管理员后台");
            
        } catch (Exception e) {
            log.error("访问管理员后台失败", e);
            model.addAttribute("error", "系统错误");
            return "error/500";
        }
        
        return "admin/dashboard";
    }

    /**
     * 获取管理员统计概览
     */
    @GetMapping("/api/stats/overview")
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStatsOverview() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("权限不足"));
            }

            Map<String, Object> stats = new HashMap<>();
            
            // 获取题目总数
            long totalTopics = topicService.count();
            stats.put("totalTopics", totalTopics);
            
            // 获取用户总数
            long totalUsers = userService.count();
            stats.put("totalUsers", totalUsers);
            
            // 获取待审核题目数量（这里需要根据实际的审核表来查询）
            // 暂时设为0，后续可以添加审核服务
            stats.put("pendingAudits", 0);
            
            // 获取今日上传统计
            Map<String, Object> uploadStats = userUploadStatsService.getAdminUploadOverview();
            stats.put("todayUploads", uploadStats.get("todayTotal"));
            stats.put("todayActiveUsers", uploadStats.get("todayActiveUsers"));
            stats.put("weekTotal", uploadStats.get("weekTotal"));
            stats.put("monthTotal", uploadStats.get("monthTotal"));

            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取管理员统计概览失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取统计数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取题型分布统计
     */
    @GetMapping("/api/stats/topic-types")
    @ResponseBody
    public ResponseEntity<ApiResponse<?>> getTopicTypeStats() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("权限不足"));
            }

            // 这里需要实现题型统计查询
            // 暂时返回模拟数据
            Map<String, Object>[] mockData = new Map[]{
                createTypeData("单选题", 450),
                createTypeData("多选题", 280),
                createTypeData("判断题", 320),
                createTypeData("填空题", 180),
                createTypeData("简答题", 120),
                createTypeData("主观题", 80)
            };

            return ResponseEntity.ok(ApiResponse.success(mockData));
            
        } catch (Exception e) {
            log.error("获取题型分布统计失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取统计数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取最新活动记录
     */
    @GetMapping("/api/activities/recent")
    @ResponseBody
    public ResponseEntity<ApiResponse<?>> getRecentActivities(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("权限不足"));
            }

            if (limit <= 0 || limit > 50) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("限制数量参数无效，应在1-50之间"));
            }

            // 这里需要实现活动记录查询
            // 暂时返回模拟数据
            Map<String, Object>[] mockActivities = new Map[]{
                createActivity("topic_upload", "用户张三上传了新题目", "上传了5道单选题到\"数学基础\"知识点", "2分钟前", "bi-plus-circle", "#28a745"),
                createActivity("topic_audit", "题目审核完成", "管理员审核通过了10道题目", "15分钟前", "bi-check-circle", "#0066FF"),
                createActivity("user_register", "新用户注册", "用户\"李四\"完成注册", "1小时前", "bi-person-plus", "#17a2b8"),
                createActivity("system_backup", "系统备份完成", "数据库自动备份已完成", "2小时前", "bi-shield-check", "#ffc107"),
                createActivity("topic_delete", "题目删除", "管理员删除了3道重复题目", "3小时前", "bi-trash", "#dc3545")
            };

            return ResponseEntity.ok(ApiResponse.success(mockActivities));
            
        } catch (Exception e) {
            log.error("获取最新活动记录失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取活动记录失败: " + e.getMessage()));
        }
    }

    /**
     * 管理员用户管理页面
     */
    @GetMapping("/users")
    public String userManagement(Model model) {
        log.info("访问用户管理页面");
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return "redirect:/admin/login";
            }

            if (!permissionService.isAdmin(currentUserId)) {
                model.addAttribute("error", "您没有管理员权限");
                return "error/403";
            }

            model.addAttribute("pageTitle", "用户管理");
            
        } catch (Exception e) {
            log.error("访问用户管理页面失败", e);
            model.addAttribute("error", "系统错误");
            return "error/500";
        }
        
        return "admin/users";
    }

    /**
     * 管理员设置页面
     */
    @GetMapping("/settings")
    public String settings(Model model) {
        log.info("访问系统设置页面");
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return "redirect:/admin/login";
            }

            if (!permissionService.isAdmin(currentUserId)) {
                model.addAttribute("error", "您没有管理员权限");
                return "error/403";
            }

            model.addAttribute("pageTitle", "系统设置");
            
        } catch (Exception e) {
            log.error("访问系统设置页面失败", e);
            model.addAttribute("error", "系统错误");
            return "error/500";
        }
        
        return "admin/settings";
    }

    /**
     * 管理员日志页面
     */
    @GetMapping("/logs")
    public String logs(Model model) {
        log.info("访问系统日志页面");
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return "redirect:/admin/login";
            }

            if (!permissionService.isAdmin(currentUserId)) {
                model.addAttribute("error", "您没有管理员权限");
                return "error/403";
            }

            model.addAttribute("pageTitle", "系统日志");
            
        } catch (Exception e) {
            log.error("访问系统日志页面失败", e);
            model.addAttribute("error", "系统错误");
            return "error/500";
        }
        
        return "admin/logs";
    }

    /**
     * 创建题型数据
     */
    private Map<String, Object> createTypeData(String type, Integer count) {
        Map<String, Object> data = new HashMap<>();
        data.put("type", type);
        data.put("count", count);
        return data;
    }

    /**
     * 创建活动数据
     */
    private Map<String, Object> createActivity(String type, String title, String desc, String time, String icon, String iconColor) {
        Map<String, Object> activity = new HashMap<>();
        activity.put("type", type);
        activity.put("title", title);
        activity.put("desc", desc);
        activity.put("time", time);
        activity.put("icon", icon);
        activity.put("iconColor", iconColor);
        return activity;
    }
}
