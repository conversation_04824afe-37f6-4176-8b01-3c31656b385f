package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.PaperService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 试卷下载控制器
 */
@RestController
@RequestMapping("/api/papers")
@RequiredArgsConstructor
@Slf4j
public class PaperDownloadController {

    private final AuthService authService;
    private final PaperService paperService;
    
    // 下载目录
    private static final String DOWNLOAD_DIR = "downloads/papers/";
    
    /**
     * 单个试卷下载
     */
    @PostMapping("/download")
    public ResponseEntity<ApiResponse<Map<String, Object>>> downloadPaper(
            @RequestParam String paperId,
            @RequestParam(defaultValue = "pdf") String format,
            @RequestParam(defaultValue = "true") boolean includeAnswers) {
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            // 验证试卷ID
            if (paperId == null || paperId.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("试卷ID不能为空"));
            }

            // 验证格式
            if (!isValidFormat(format)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("不支持的下载格式"));
            }

            // 生成下载文件
            String downloadUrl = generatePaperFile(paperId, format, includeAnswers, currentUserId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("downloadUrl", downloadUrl);
            result.put("paperId", paperId);
            result.put("format", format);
            result.put("includeAnswers", includeAnswers);
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("下载试卷失败，试卷ID: {}, 格式: {}", paperId, format, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("下载失败: " + e.getMessage()));
        }
    }

    /**
     * 批量创建ZIP下载
     */
    @PostMapping("/create-zip")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createZipDownload(
            @RequestParam List<String> urls) {
        
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (urls == null || urls.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("下载链接列表不能为空"));
            }

            // 创建ZIP文件
            String zipUrl = createZipFile(urls, currentUserId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("zipUrl", zipUrl);
            result.put("fileCount", urls.size());
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("创建ZIP下载失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("创建ZIP失败: " + e.getMessage()));
        }
    }

    /**
     * 下载文件
     */
    @GetMapping("/download/{filename}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String filename) {
        try {
            Path filePath = Paths.get(DOWNLOAD_DIR).resolve(filename).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                String contentType = Files.probeContentType(filePath);
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, 
                                "attachment; filename=\"" + resource.getFilename() + "\"")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (MalformedURLException e) {
            log.error("下载文件失败: {}", filename, e);
            return ResponseEntity.badRequest().build();
        } catch (IOException e) {
            log.error("读取文件失败: {}", filename, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 生成试卷文件
     */
    private String generatePaperFile(String paperId, String format, boolean includeAnswers, Long userId) 
            throws IOException {
        
        // 确保下载目录存在
        Path downloadPath = Paths.get(DOWNLOAD_DIR);
        if (!Files.exists(downloadPath)) {
            Files.createDirectories(downloadPath);
        }
        
        // 生成文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String filename = String.format("paper_%s_%s_%s.%s", paperId, timestamp, UUID.randomUUID().toString().substring(0, 8), format);
        Path filePath = downloadPath.resolve(filename);
        
        // 根据格式生成不同类型的文件
        switch (format.toLowerCase()) {
            case "pdf":
                generatePdfFile(paperId, filePath, includeAnswers);
                break;
            case "word":
                generateWordFile(paperId, filePath, includeAnswers);
                break;
            default:
                throw new IllegalArgumentException("不支持的格式: " + format);
        }
        
        // 返回下载URL
        return "/api/papers/download/" + filename;
    }

    /**
     * 生成PDF文件
     */
    private void generatePdfFile(String paperId, Path filePath, boolean includeAnswers) throws IOException {
        // 这里应该实现PDF生成逻辑
        // 暂时创建一个示例文件
        String content = generatePaperContent(paperId, includeAnswers, "PDF");
        Files.write(filePath, content.getBytes("UTF-8"));
        
        log.info("生成PDF文件: {}", filePath);
    }

    /**
     * 生成Word文件
     */
    private void generateWordFile(String paperId, Path filePath, boolean includeAnswers) throws IOException {
        // 这里应该实现Word文档生成逻辑
        // 暂时创建一个示例文件
        String content = generatePaperContent(paperId, includeAnswers, "Word");
        Files.write(filePath, content.getBytes("UTF-8"));
        
        log.info("生成Word文件: {}", filePath);
    }

    /**
     * 生成试卷内容
     */
    private String generatePaperContent(String paperId, boolean includeAnswers, String format) {
        StringBuilder content = new StringBuilder();
        content.append("试卷ID: ").append(paperId).append("\n");
        content.append("格式: ").append(format).append("\n");
        content.append("生成时间: ").append(LocalDateTime.now()).append("\n");
        content.append("包含答案: ").append(includeAnswers ? "是" : "否").append("\n\n");
        
        // 这里应该从数据库获取实际的试卷内容
        content.append("=== 试卷内容 ===\n");
        content.append("1. 这是一道示例题目\n");
        content.append("A. 选项A\n");
        content.append("B. 选项B\n");
        content.append("C. 选项C\n");
        content.append("D. 选项D\n\n");
        
        if (includeAnswers) {
            content.append("=== 答案解析 ===\n");
            content.append("1. 答案：A\n");
            content.append("解析：这是答案解析内容...\n");
        }
        
        return content.toString();
    }

    /**
     * 创建ZIP文件
     */
    private String createZipFile(List<String> urls, Long userId) throws IOException {
        // 确保下载目录存在
        Path downloadPath = Paths.get(DOWNLOAD_DIR);
        if (!Files.exists(downloadPath)) {
            Files.createDirectories(downloadPath);
        }
        
        // 生成ZIP文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String zipFilename = String.format("papers_batch_%s_%s.zip", timestamp, UUID.randomUUID().toString().substring(0, 8));
        Path zipPath = downloadPath.resolve(zipFilename);
        
        // 创建ZIP文件
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipPath.toFile()))) {
            for (int i = 0; i < urls.size(); i++) {
                String url = urls.get(i);
                String entryName = String.format("paper_%d.txt", i + 1);
                
                ZipEntry entry = new ZipEntry(entryName);
                zos.putNextEntry(entry);
                
                // 这里应该读取实际文件内容
                String content = "试卷内容 " + (i + 1) + "\n来源URL: " + url;
                zos.write(content.getBytes("UTF-8"));
                zos.closeEntry();
            }
        }
        
        log.info("创建ZIP文件: {}", zipPath);
        return "/api/papers/download/" + zipFilename;
    }

    /**
     * 验证下载格式
     */
    private boolean isValidFormat(String format) {
        return format != null && (format.equalsIgnoreCase("pdf") || 
                                 format.equalsIgnoreCase("word") || 
                                 format.equalsIgnoreCase("zip"));
    }
}
