package com.edu.maizi_edu_sys.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class ViewController {

    @Value("${app.features.daily-upload-stats:false}")
    private boolean dailyUploadStatsEnabled;

    // 移除根路径映射，避免与 HomeController 冲突
    // 现在由 HomeController 处理根路径请求

    // 出题页面
    @GetMapping("/main/chat")
    public String chatPage() {
        return "main/chat";
    }

    // 上传题目页面
    @GetMapping("/topics/upload-topics")
    public String uploadTopics(Model model) {
        model.addAttribute("dailyUploadStatsEnabled", dailyUploadStatsEnabled);
        return "topics/upload-topics";
    }

    // 题库页面路由
    @GetMapping("/topics/bank")
    public String bankPage() {
        return "topics/bank";
    }

    // 组卷页面路由
    @GetMapping("/paper/generate")
    public String generatePage() {
        return "paper/generate";
    }

    //  试卷配置管理页面路由
    @GetMapping("/paper-configs")
    public String paperConfigsPage() {
        return "paper/config-management";
    }

    // 查重页面路由 - 重定向到正确的查重页面
    @GetMapping("/paper/check")
    public String checkPage() {
        return "redirect:/papers/duplicate-check";
    }

    // 用户信息页面路由
    @GetMapping("/user/profile")
    public String profilePage() {
        return "user/profile";
    }

    // 登录页面路由
    @GetMapping("/auth/login")
    public String loginPage() {
        return "auth/login";
    }

    // 旧上传路径重定向
    @GetMapping("/paper/upload")
    public String oldUploadPage() {
        return "redirect:/topics/upload-topics";
    }

    // 处理旧的 HTML 文件请求
    @GetMapping("/login.html")
    public String loginHtml() {
        return "redirect:/auth/login";
    }

    @GetMapping("/chat.html")
    public String chatHtml() {
        return "redirect:/main/chat";
    }

    @GetMapping("/profile.html")
    public String profileHtml() {
        return "redirect:/user/profile";
    }

    // 移除 /index.html 映射，避免与 HomeController 冲突
    // 现在由 HomeController 处理首页请求

    // 认证功能测试页面
    @GetMapping("/test/auth")
    public String authTestPage() {
        return "test/auth-test";
    }

    // 数学公式渲染测试页面
    @GetMapping("/test/math")
    public String mathFormulaTestPage() {
        return "test/math-formula-test";
    }

    // PDF生成测试页面
    @GetMapping("/test/pdf")
    public String pdfTestPage() {
        return "test/pdf-test";
    }

    // 知识点删除功能测试页面
    @GetMapping("/test/knowledge-point-delete")
    public String knowledgePointDeleteTestPage() {
        return "test/knowledge-point-delete-test";
    }
}