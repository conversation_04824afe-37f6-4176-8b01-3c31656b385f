package com.edu.maizi_edu_sys.task;

import com.edu.maizi_edu_sys.service.TopicAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 题目审核定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.task.topic-audit.enabled", havingValue = "true", matchIfMissing = true)
public class TopicAuditTask {

    private final TopicAuditService topicAuditService;

    /**
     * 自动审核超时题目
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void autoApproveOverdueTopics() {
        log.info("开始执行自动审核超时题目任务");
        
        try {
            topicAuditService.autoApproveOverdueTopics();
            log.info("自动审核超时题目任务执行完成");
        } catch (Exception e) {
            log.error("自动审核超时题目任务执行失败", e);
        }
    }

    /**
     * 审核统计报告
     * 每天上午9点执行
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void generateAuditReport() {
        log.info("开始生成审核统计报告");
        
        try {
            Long pendingCount = topicAuditService.getPendingAuditCount();
            log.info("当前待审核题目数量: {}", pendingCount);
            
            // 这里可以扩展发送邮件通知管理员等功能
            if (pendingCount > 100) {
                log.warn("待审核题目数量过多，建议及时处理: {}", pendingCount);
            }
            
        } catch (Exception e) {
            log.error("生成审核统计报告失败", e);
        }
    }
}
