package com.edu.maizi_edu_sys.config;

import com.edu.maizi_edu_sys.util.JwtUtil;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.AuthService;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.lang.NonNull;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

@Component
@Slf4j
public class AuthInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private AuthService authService;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception {
        String uri = request.getRequestURI();
        // 1. Try to get token from Authorization header first
        String token = request.getHeader("Authorization");

        // 2. If header not present, attempt to retrieve from cookies (e.g., Authorization or JWT_TOKEN)
        if (token == null || token.isEmpty()) {
            if (request.getCookies() != null) {
                for (javax.servlet.http.Cookie cookie : request.getCookies()) {
                    if ("Authorization".equalsIgnoreCase(cookie.getName()) || "JWT_TOKEN".equalsIgnoreCase(cookie.getName())) {
                        token = cookie.getValue();
                        break;
                    }
                }
            }
        }

        // 3. If still not found, check the "token" query parameter so that downloads via <a href> can work
        if (token == null || token.isEmpty()) {
            token = request.getParameter("token");
        }

        // Ensure we have a non-empty token string for subsequent logic
        if (token != null) {
            token = token.trim();
        }

        log.info("Request URI: {}, Token: {}", uri, token != null ? "exists" : "null");

        // 如果是登录相关的路径，直接放行
        if (uri.startsWith("/login") || uri.startsWith("/register") ||
            uri.startsWith("/static/css/") || uri.startsWith("/static/js/") ||
            uri.startsWith("/static/images/") || uri.startsWith("/static/")) {
            return true;
        }
        
        // 允许所有模板路径直接访问
        if (uri.equals("/") || 
            uri.startsWith("/main/") || 
            uri.startsWith("/topics/") || 
            uri.startsWith("/paper/") || 
            uri.startsWith("/user/") ||
            uri.startsWith("/auth/")) {
            log.info("Template path access: {}", uri);
            return true;
        }

        // 如果是API请求
        if (uri.startsWith("/api/")) {
            // 登录和注册API以及无需验证的公共API不需要验证
            if (uri.equals("/api/user/login") || uri.equals("/api/user/register") ||
                uri.equals("/api/user/validate") || uri.startsWith("/api/books/search") ||
                uri.equals("/api/books") || uri.startsWith("/api/books/type/")) {
                log.info("Public API access: {}", uri);
                return true;
            }

            // 其他API需要验证token
            if (token == null) {
                log.warn("API request without token: {}", uri);
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json");
                response.getWriter().write("{\"code\":401,\"message\":\"请先登录\"}");
                return false;
            }

            try {
                // 确保token格式正确
                if (token.toLowerCase().startsWith("bearer ")) {
                    token = token.substring(7);
                }
                
                // 验证token
                boolean isValid = jwtUtil.validateToken(token);
                if (!isValid) {
                    log.warn("Invalid token for URI: {}", uri);
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    response.setContentType("application/json");
                    response.getWriter().write("{\"code\":401,\"message\":\"登录已过期或无效\"}");
                    return false;
                }

                // 检查API权限
                Long currentUserId = authService.getCurrentUserId();
                if (currentUserId != null && !checkApiPermission(currentUserId, uri)) {
                    log.warn("User {} has no permission to access API: {}", currentUserId, uri);
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.setContentType("application/json");
                    response.getWriter().write("{\"code\":403,\"message\":\"无权限访问此接口\"}");
                    return false;
                }

                return true;
            } catch (Exception e) {
                log.error("Token validation failed: {}", e.getMessage());
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json");
                response.getWriter().write("{\"code\":401,\"message\":\"登录已过期\"}");
                return false;
            }
        }

        // 如果是页面请求（非静态资源）
        if (uri.startsWith("/question/")) {
            if (token == null) {
                log.warn("Page request without token: {}", uri);
                response.sendRedirect("/login");
                return false;
            }

            try {
                jwtUtil.validateToken(token);
                return true;
            } catch (Exception e) {
                log.error("Token validation failed for page request: {}", e.getMessage());
                response.sendRedirect("/login");
                return false;
            }
        }

        // 添加教材搜索接口白名单
        if (request.getRequestURI().startsWith("/api/v1/books/search")) {
            return validateToken(request, response); // 需要认证但不需要特定权限
        }

        return true;
    }

    private boolean validateToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            sendError(response, 401, "无效的认证格式");
            return false;
        }
        
        // 实际验证逻辑...
        return true;
    }

    private void sendError(HttpServletResponse response, int statusCode, String message) throws IOException {
        response.setStatus(statusCode);
        response.setContentType("application/json");
        response.getWriter().write("{\"code\":401,\"message\":\"" + message + "\"}");
    }

    /**
     * 检查API权限
     */
    private boolean checkApiPermission(Long userId, String apiPath) {
        try {
            // 管理员拥有所有权限，直接通过
            if (permissionService.isAdmin(userId)) {
                log.debug("管理员用户 {} 访问API: {}", userId, apiPath);
                return true;
            }

            // 管理员接口权限检查 - 只有管理员可以访问
            if (apiPath.startsWith("/api/admin/")) {
                return false; // 非管理员不能访问管理员接口
            }

            // 使用通用的API路径权限检查
            return permissionService.canAccessApi(userId, apiPath);
        } catch (Exception e) {
            log.error("权限检查失败: userId={}, apiPath={}", userId, apiPath, e);
            return false;
        }
    }
}