package com.edu.maizi_edu_sys.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 应用启动监听器
 * 在应用启动完成后打印管理员后台访问地址
 */
@Slf4j
@Component
public class ApplicationStartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        Environment env = event.getApplicationContext().getEnvironment();
        
        try {
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            String localAddress = "localhost";
            
            // 构建访问地址
            String protocol = "http";
            String port = env.getProperty("server.port", "8080");
            String context = env.getProperty("server.servlet.context-path", "");
            
            // 确保context-path格式正确
            if (context != null && !context.isEmpty() && !context.startsWith("/")) {
                context = "/" + context;
            }
            
            String localUrl = String.format("%s://%s:%s%s", protocol, localAddress, port, context);
            String networkUrl = String.format("%s://%s:%s%s", protocol, hostAddress, port, context);
            
            // 管理员后台地址
            String adminLoginLocalUrl = localUrl + "/admin/login";
            String adminLoginNetworkUrl = networkUrl + "/admin/login";
            String adminLocalUrl = localUrl + "/admin/topics/audit";
            String adminNetworkUrl = networkUrl + "/admin/topics/audit";
            
            // 打印启动信息
            log.info("\n" +
                "╔══════════════════════════════════════════════════════════════════════════════╗\n" +
                "║                           麦子教育系统启动成功                                ║\n" +
                "╠══════════════════════════════════════════════════════════════════════════════╣\n" +
                "║  应用访问地址:                                                                ║\n" +
                "║    本地访问: {}                                     ║\n" +
                "║    网络访问: {}                              ║\n" +
                "║                                                                              ║\n" +
                "║  🔐 管理员登录页面:                                                           ║\n" +
                "║    本地访问: {}                              ║\n" +
                "║    网络访问: {}                       ║\n" +
                "║                                                                              ║\n" +
                "║  🔐 管理员后台地址 (登录后访问):                                              ║\n" +
                "║    本地访问: {}                        ║\n" +
                "║    网络访问: {}                 ║\n" +
                "║                                                                              ║\n" +
                "║  📋 功能模块:                                                                 ║\n" +
                "║    • 智能出题: {}/main/chat                                    ║\n" +
                "║    • 题目上传: {}/topics/upload-topics                         ║\n" +
                "║    • 题库管理: {}/topics/bank                                  ║\n" +
                "║    • 智能组卷: {}/paper/generate                               ║\n" +
                "║    • 用户登录: {}/auth/login                                   ║\n" +
                "║                                                                              ║\n" +
                "║  ⚙️  管理员功能:                                                              ║\n" +
                "║    • 题目审核: {}/admin/topics/audit                           ║\n" +
                "║    • 用户管理: {}/admin/users                                  ║\n" +
                "║    • 系统统计: {}/admin/stats                                  ║\n" +
                "║    • 权限管理: {}/admin/permissions                            ║\n" +
                "║                                                                              ║\n" +
                "║  📊 API文档: {}/swagger-ui.html (如果启用)                      ║\n" +
                "║                                                                              ║\n" +
                "║  ⚠️  安全提示:                                                               ║\n" +
                "║    • 管理员后台入口已隐藏，普通用户无法在前端页面看到                          ║\n" +
                "║    • 只有 role=1 的管理员用户才能访问后台管理功能                            ║\n" +
                "║    • 建议定期更改管理员密码，确保系统安全                                    ║\n" +
                "╚══════════════════════════════════════════════════════════════════════════════╝",
                String.format("%-45s", localUrl),
                String.format("%-37s", networkUrl),
                String.format("%-38s", adminLoginLocalUrl),
                String.format("%-31s", adminLoginNetworkUrl),
                String.format("%-32s", adminLocalUrl),
                String.format("%-25s", adminNetworkUrl),
                String.format("%-37s", localUrl),
                String.format("%-32s", localUrl),
                String.format("%-37s", localUrl),
                String.format("%-37s", localUrl),
                String.format("%-37s", localUrl),
                String.format("%-32s", localUrl),
                String.format("%-37s", localUrl),
                String.format("%-37s", localUrl),
                String.format("%-32s", localUrl),
                String.format("%-32s", localUrl)
            );
            
            // 额外的管理员提示
            log.warn("\n" +
                "🔒 管理员后台安全提示:\n" +
                "   • 登录页面: {}\n" +
                "   • 后台地址: {}\n" +
                "   • 访问权限: 仅限 role=1 的管理员用户\n" +
                "   • 默认账号: admin / admin123 (请立即修改密码)\n" +
                "   • 安全建议: 请妥善保管管理员账号信息\n" +
                "   • 功能说明: 题目审核、用户管理、系统统计、权限管理",
                adminLoginLocalUrl,
                adminLocalUrl
            );
            
        } catch (UnknownHostException e) {
            log.error("获取主机地址失败", e);
            log.info("麦子教育系统启动成功，端口: {}", serverPort);
            log.warn("🔒 管理员后台地址: http://localhost:{}{}/admin/topics/audit (仅限管理员访问)", 
                    serverPort, contextPath);
        }
    }
}
