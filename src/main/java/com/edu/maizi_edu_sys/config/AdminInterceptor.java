package com.edu.maizi_edu_sys.config;

import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.service.AdminSecurityService;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 管理员后台访问拦截器
 * 确保只有 role=1 的管理员才能访问后台页面
 */
@Slf4j
@Component
public class AdminInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private AuthService authService;

    @Autowired
    private AdminSecurityService adminSecurityService;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, 
                           @NonNull HttpServletResponse response, 
                           @NonNull Object handler) throws Exception {
        
        String uri = request.getRequestURI();
        log.debug("管理员拦截器检查访问: {}", uri);
        
        try {
            // 获取当前用户ID
            Long currentUserId = authService.getCurrentUserId();
            
            if (currentUserId == null) {
                log.warn("未登录用户尝试访问管理后台: {}", uri);
                redirectToLogin(response, "请先登录");
                return false;
            }
            
            // 检查是否为活跃的管理员
            if (!adminSecurityService.isActiveAdmin(currentUserId)) {
                User user = adminSecurityService.getAdminUser(currentUserId);
                if (user == null) {
                    log.warn("非管理员用户尝试访问管理后台: userId={}, uri={}", currentUserId, uri);
                    sendForbidden(response, "您没有权限访问管理后台");
                } else {
                    log.warn("已禁用的管理员尝试访问后台: userId={}, uri={}", currentUserId, uri);
                    sendForbidden(response, "管理员账号已被禁用");
                }
                return false;
            }

            // 记录管理员访问日志
            adminSecurityService.logAdminAccess(currentUserId, "ACCESS", uri);

            User admin = adminSecurityService.getAdminUser(currentUserId);
            log.info("管理员访问后台: userId={}, username={}, uri={}",
                    currentUserId, admin != null ? admin.getUsername() : "unknown", uri);
            
            return true;
            
        } catch (Exception e) {
            log.error("管理员权限检查失败: uri={}", uri, e);
            sendError(response, "系统错误，请稍后重试");
            return false;
        }
    }

    /**
     * 重定向到管理员登录页面
     */
    private void redirectToLogin(HttpServletResponse response, String message) throws IOException {
        if (isAjaxRequest(response)) {
            sendJsonResponse(response, 401, message);
        } else {
            // 重定向到专用的管理员登录页面
            response.sendRedirect("/admin/login?message=" + java.net.URLEncoder.encode(message, "UTF-8"));
        }
    }

    /**
     * 发送403禁止访问响应
     */
    private void sendForbidden(HttpServletResponse response, String message) throws IOException {
        if (isAjaxRequest(response)) {
            sendJsonResponse(response, 403, message);
        } else {
            // 重定向到专用的管理员访问拒绝页面
            response.sendRedirect("/admin/access-denied?error=" + java.net.URLEncoder.encode(message, "UTF-8"));
        }
    }

    /**
     * 发送500错误响应
     */
    private void sendError(HttpServletResponse response, String message) throws IOException {
        if (isAjaxRequest(response)) {
            sendJsonResponse(response, 500, message);
        } else {
            response.sendRedirect("/?error=" + message);
        }
    }

    /**
     * 判断是否为Ajax请求
     */
    private boolean isAjaxRequest(HttpServletResponse response) {
        // 这里可以通过request header判断，但由于方法签名限制，暂时默认返回JSON
        return true; // 默认返回JSON响应
    }

    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(HttpServletResponse response, int statusCode, String message) throws IOException {
        response.setStatus(statusCode);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(String.format(
            "{\"success\":false,\"code\":%d,\"message\":\"%s\"}", 
            statusCode, message
        ));
    }
}
