package com.edu.maizi_edu_sys.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.List;


@Data
public class TopicDTO {
    private Integer id;
    @NotNull(message = "知识点ID不能为空")
    @JsonProperty("know_id")
    private Integer knowId;

    @NotBlank(message = "题型不能为空")
    @Pattern(regexp = "^(choice|multiple|judge|fill|short|subjective|group)$", message = "无效的题型")
    private String type;

    @NotBlank(message = "题目标题不能为空")
    private String title;

    private String tags;

    private List<OptionDTO> options;

    private String subs;

    private String parse;

    private String answer;

    @NotNull(message = "分值不能为空")
    @Min(value = 1, message = "分值必须大于0")
    private Integer score = 3;

    @NotBlank(message = "来源不能为空")
    private String source;

    @Min(value = 1, message = "排序值必须大于0")
    @Max(value = 255, message = "排序值不能超过255")
    private Integer sort = 1;

    @NotNull(message = "难度不能为空")
    @DecimalMin(value = "0.0", message = "难度必须在0-1之间")
    @DecimalMax(value = "1.0", message = "难度必须在0-1之间")
    private Double difficulty;



    @Data
    public static class OptionDTO {
        @NotBlank(message = "选项key不能为空")
        @Pattern(regexp = "^[A-Z]$", message = "选项key必须是大写字母")
        private String key;

        @NotBlank(message = "选项内容不能为空")
        private String name;
    }
} 