package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import com.edu.maizi_edu_sys.entity.Topic;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态规划调整器组件 (DPAdjuster)。
 * <p>
 * 该组件的核心职责是使用动态规划算法对初步选定的题目列表进行微调，
 * 目的是在保持题目集合尽可能接近原选集的基础上，使其总分精确匹配目标分数。
 * 这通常作为遗传算法或其他启发式选题算法的后处理步骤，以弥补它们在总分精确性上的不足。
 * </p>
 * <p>
 * 主要应用场景是在试卷生成过程中，当遗传算法选出的题目总分与期望总分有偏差时，
 * DPAdjuster 尝试通过移除部分题目或（在更复杂的实现中）替换题目来达到精确总分。
 * 当前实现侧重于通过"移除"多余分值的题目来向下调整至目标分数。
 * </p>
 */
@Component
@Slf4j
public class DPAdjuster {

    /**
     * 使用动态规划调整选定的题目列表，以使其总分精确匹配目标分数 {@code targetScore}。
     * <p>
     * 调整策略：
     * <ul>
     *   <li>如果当前题目总分已等于目标分数，则直接返回原列表。</li>
     *   <li>如果当前题目总分小于目标分数，当前实现不进行向上调整（假设之前的选题步骤已是最优），直接返回原列表。</li>
     *   <li>如果当前题目总分大于目标分数，则尝试通过移除一部分题目，使得剩余题目的总分恰好等于 {@code targetScore}。
     *       这是通过经典的0/1背包问题的动态规划变种来实现的，目标是找到一个子集，其元素（题目分数）之和为特定值。</li>
     * </ul>
     * </p>
     *
     * @param topics 候选题目列表，通常是遗传算法或其他初步选择算法的结果。
     * @param targetScore 期望达到的试卷确切总分。
     * @param typeScores 题目类型分数映射表。
     * @return 调整后的题目列表。如果无法精确匹配，则根据实现策略可能返回原列表或最接近的调整结果。
     *         当前实现下，若无法精确匹配（特别是在需要减分的情况下），会返回原始列表。
     */
    /**
     * 使用动态规划调整选定的题目列表，以使其总分精确匹配目标分数 {@code targetScore}。
     * <p>
     * 调整策略：
     * <ul>
     *   <li>如果当前题目总分已等于目标分数，则直接返回原列表。</li>
     *   <li>如果当前题目总分小于目标分数，当前实现不进行向上调整（假设之前的选题步骤已是最优），直接返回原列表并记录警告。</li>
     *   <li>如果当前题目总分大于目标分数，则尝试通过移除一部分题目，使得剩余题目的总分恰好等于 {@code targetScore}。
     *       这是通过经典的0/1背包问题的动态规划变种来实现的，目标是找到一个子集，其元素（题目分数）之和为特定值。</li>
     * </ul>
     * </p>
     *
     * @param topics 候选题目列表，通常是遗传算法或其他初步选择算法的结果。
     * @param targetScore 期望达到的试卷确切总分。
     * @param typeScores 题目类型分数映射表。
     * @return 调整后的题目列表。如果无法精确匹配（例如，需要减分但找不到合适的子集，或需要增分但当前方法不支持），
     *         则会记录相应的日志并返回原始列表。
     */
    public List<Topic> adjust(List<Topic> topics, int targetScore, Map<String, Integer> typeScores) {
        if (topics == null || topics.isEmpty()) {
            log.warn("DPAdjuster: Input topics list is null or empty. Returning empty list.");
            return Collections.emptyList();
        }

        // Calculate current score using the provided typeScores map
        int currentScore = topics.stream()
                                 .mapToInt(t -> getTopicScore(t, typeScores))
                                 .sum();
        log.info("DPAdjuster: Adjusting topics. Current score: {}, Target score: {}. Number of topics: {}",
                 currentScore, targetScore, topics.size());

        if (currentScore == targetScore) {
            log.info("DPAdjuster: Current score already matches target score. No adjustment needed.");
            return topics;
        }

        if (currentScore < targetScore) {
            log.warn("DPAdjuster: Current score ({}) is less than target score ({}). Upward adjustment is not supported. Returning original list.",
                     currentScore, targetScore);
            return topics;
        }

        log.info("DPAdjuster: Current score ({}) is greater than target ({}). Attempting to find a subset that sums to target score.", currentScore, targetScore);
        List<Topic> adjustedTopics = findOptimalSubset(topics, targetScore, typeScores);
        if (adjustedTopics.isEmpty()) {
            log.warn("DPAdjuster: findOptimalSubset could not find a suitable subset for target score {}. Returning original list of {} topics with score {}.",
                     targetScore, topics.size(), currentScore);
            return topics; // Adjustment failed, return original list
        }
        // Recalculate score of adjustedTopics for accurate logging, though it should be targetScore if DP worked correctly
        int finalAdjustedScore = adjustedTopics.stream().mapToInt(t -> getTopicScore(t, typeScores)).sum();
        log.info("DPAdjuster: Successfully adjusted topics. New score: {}. Number of topics: {}. Original score was {}.",
                 finalAdjustedScore, adjustedTopics.size(), currentScore);
        return adjustedTopics;
    }

    /**
     * 获取题目的分数，优先使用typeScores中的配置，如果没有则使用题目自身的分数
     *
     * @param topic 题目
     * @param typeScores 题型分数映射
     * @return 题目分数
     */
    private int getTopicScore(Topic topic, Map<String, Integer> typeScores) {
        String normalizedType = TopicTypeMapper.normalize(topic.getType());
        String frontendKey = TopicTypeMapper.toFrontendType(normalizedType); // Convert to key format used in typeScores
        // 默认分数为0，如果题目自身有分数且typeScores中没有，则使用题目自身分数
        return typeScores.getOrDefault(frontendKey, topic.getScore() != null ? topic.getScore() : 0);
    }

    /**
     * 增强版调整方法，同时考虑分数和题型约束
     * <p>
     * 此方法尝试在满足目标分数的同时，尽可能满足题型分布的要求
     * </p>
     *
     * @param topics 候选题目列表
     * @param targetScore 目标总分
     * @param typeScores 题目类型分数映射表
     * @param targetTypeCounts 各题型目标数量
     * @return 同时满足分数和题型约束的题目列表
     */
    /**
     * 增强版调整方法，同时考虑分数和题型约束
     * <p>
     * 此方法尝试在满足目标分数的同时，尽可能满足题型分布的要求
     * </p>
     * 
     * @param topics 候选题目列表
     * @param targetScore 目标总分
     * @param typeScores 题型分数映射
     * @param targetTypeCounts 目标题型数量
     * @return 同时满足分数和题型约束的题目列表
     */
    public List<Topic> adjust(List<Topic> topics, int targetScore, Map<String, Integer> typeScores,
                             Map<String, Integer> targetTypeCounts) {
        // 调用带有可用题目池参数的重载方法，但传入null表示不进行缺失题型的补充
        return adjust(topics, targetScore, typeScores, targetTypeCounts, null);
    }
    
    /**
     * 增强版调整方法，同时考虑分数和题型约束，并可补充缺失题型
     * <p>
     * 此方法首先尝试补充缺失的题型，然后尝试在满足目标分数的同时，尽可能满足题型分布的要求
     * </p>
     * 
     * @param topics 候选题目列表
     * @param targetScore 目标总分
     * @param typeScores 题型分数映射
     * @param targetTypeCounts 目标题型数量
     * @param allAvailableTopics 所有可用题目，用于补充缺失题型。可以为null，此时不会补充缺失题型
     * @return 同时满足分数和题型约束的题目列表
     */
    public List<Topic> adjust(List<Topic> topics, int targetScore, Map<String, Integer> typeScores,
                              Map<String, Integer> targetTypeCounts, List<Topic> allAvailableTopics) {
        if (topics == null || topics.isEmpty()) {
            log.warn("DPAdjuster: Input topics list is null or empty. Returning empty list.");
            return Collections.emptyList();
        }

        // Calculate current score using the provided typeScores map
        int currentScore = topics.stream()
                                 .mapToInt(t -> getTopicScore(t, typeScores))
                                 .sum();
        log.info("DPAdjuster: Adjusting topics with type constraints. Current score: {}, Target score: {}. Number of topics: {}",
                 currentScore, targetScore, topics.size());

        // 题型约束检查
        Map<String, Integer> currentTypeCounts = topics.stream()
            .collect(Collectors.groupingBy(
                topic -> TopicTypeMapper.normalize(topic.getType()),
                Collectors.counting()))
            .entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                e -> e.getValue().intValue()));

        log.info("DPAdjuster: Current type counts: {}, Target type counts: {}", currentTypeCounts, targetTypeCounts);

        // 如果提供了可用题目池，尝试补充缺失的题型
        if (allAvailableTopics != null && !allAvailableTopics.isEmpty()) {
            log.info("DPAdjuster: Attempting to fill missing topic types using available topic pool of {} topics", allAvailableTopics.size());
            List<Topic> supplementedTopics = fillMissingTypes(topics, targetTypeCounts, allAvailableTopics, typeScores);
            
            if (supplementedTopics.size() > topics.size()) {
                log.info("DPAdjuster: Successfully supplemented {} topics to fill missing types", 
                         supplementedTopics.size() - topics.size());
                topics = supplementedTopics;
                
                // 重新计算分数和题型分布
                currentScore = topics.stream().mapToInt(t -> getTopicScore(t, typeScores)).sum();
                currentTypeCounts = topics.stream()
                    .collect(Collectors.groupingBy(
                        topic -> TopicTypeMapper.normalize(topic.getType()),
                        Collectors.counting()))
                    .entrySet().stream()
                    .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().intValue()));
                
                log.info("DPAdjuster: After filling missing types - Current score: {}, Type counts: {}", 
                         currentScore, currentTypeCounts);
            }
        }

        // 检查当前分数是否已经满足要求
        if (currentScore == targetScore) {
            log.info("DPAdjuster: Current score already matches target score.");

            // 检查题型约束是否满足
            boolean typeConstraintsSatisfied = true;
            for (Map.Entry<String, Integer> entry : targetTypeCounts.entrySet()) {
                String type = entry.getKey();
                int targetCount = entry.getValue();
                int currentCount = currentTypeCounts.getOrDefault(type, 0);

                if (targetCount > 0 && currentCount != targetCount) {
                    typeConstraintsSatisfied = false;
                    log.warn("DPAdjuster: Type constraint not satisfied for {}: required={}, actual={}",
                            type, targetCount, currentCount);
                }
            }

            if (typeConstraintsSatisfied) {
                log.info("DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.");
                return topics;
            } else {
                log.info("DPAdjuster: Score matches but type constraints are not satisfied. Applying type-preserving optimization.");
            }
        }

        // --- 如果当前总分仍低于目标分数，尝试通过替换同类型更高分题目来提升分数 ---
        if (currentScore < targetScore && allAvailableTopics != null && !allAvailableTopics.isEmpty()) {
            log.info("DPAdjuster: Current score {} is lower than target {}. Attempting greedy replacement to increase score.", currentScore, targetScore);
            List<Topic> improved = greedyIncreaseScore(topics, allAvailableTopics, typeScores, targetScore);
            int improvedScore = improved.stream().mapToInt(t -> getTopicScore(t, typeScores)).sum();
            if (improvedScore > currentScore) {
                log.info("DPAdjuster: Greedy replacement increased score from {} to {}", currentScore, improvedScore);
                topics = improved;
                currentScore = improvedScore;
            }
        }

        // 使用考虑题型约束的优化方法
        List<Topic> adjustedTopics = findOptimalSubsetWithTypeConstraints(topics, targetScore, typeScores, targetTypeCounts);
        if (adjustedTopics.isEmpty()) {
            log.warn("DPAdjuster (Type Aware): Unable to find a subset for target score {} with exact type counts {}. Returning original list of {} topics with score {}.",
                     targetScore, targetTypeCounts, topics.size(), currentScore);
            return topics; // Adjustment failed, return original list
        }
        // Recalculate score of adjustedTopics for accurate logging, though it should be targetScore if DP worked correctly
        int finalAdjustedScore = adjustedTopics.stream().mapToInt(t -> getTopicScore(t, typeScores)).sum();
        log.info("DPAdjuster: Successfully adjusted topics with type constraints. New score: {}. Number of topics: {}. Original score was {}.",
                 finalAdjustedScore, adjustedTopics.size(), currentScore);
        return adjustedTopics;
    }

    /**
     * 寻找给定题目列表的一个子集，使其总分精确等于 {@code targetScore}。
     * <p>
     * 此方法使用动态规划（0/1背包问题的变种）来解决子集和问题。
     * DP状态 {@code dp[i][j]} 表示是否可以使用前 {@code i} 个题目达到总分 {@code j}。
     * </p>
     * <p>
     * 算法步骤：
     * <ol>
     *   <li>初始化DP表：{@code dp[i][0]} 为 true（总能用任意题目达到0分，即不选）。</li>
     *   <li>填充DP表：对于每道题目 {@code topics[i-1]} 和每个可能的分数 {@code j}：
     *     <ul>
     *       <li>不选当前题目：{@code dp[i][j] = dp[i-1][j]}</li>
     *       <li>选择当前题目（如果其分数 {@code topicScore <= j}）：{@code dp[i][j] = dp[i][j] || dp[i-1][j - topicScore]}</li>
     *     </ul>
     *   </li>
     *   <li>检查是否能达到目标分数：如果 {@code dp[n][targetScore]} 为 false，则无法精确匹配，返回原始列表。</li>
     *   <li>回溯DP表：如果可以达到目标分数，则从 {@code dp[n][targetScore]} 开始回溯，构造出实际选择的题目列表。</li>
     * </ol>
     * </p>
     *
     * @param topics 原始题目列表。
     * @param targetScore 目标总分数。
     * @param typeScores 题目类型分数映射表。
     * @return 一个新的题目列表，其总分精确等于 {@code targetScore}。
     *         如果无法找到这样的子集，则返回空列表。
     */
    private List<Topic> findOptimalSubset(List<Topic> topics, int targetScore, Map<String, Integer> typeScores) {
        int n = topics.size();
        boolean[][] dp = new boolean[n + 1][targetScore + 1];

        for (int i = 0; i <= n; i++) {
            dp[i][0] = true;
        }

        for (int i = 1; i <= n; i++) {
            Topic currentTopic = topics.get(i - 1);
            // Get score using our helper method
            int topicScore = getTopicScore(currentTopic, typeScores);
            if (topicScore < 0) topicScore = 0; // Ensure non-negative for DP

            for (int j = 1; j <= targetScore; j++) {
                dp[i][j] = dp[i - 1][j];
                if (topicScore <= j && topicScore > 0) {
                    dp[i][j] = dp[i][j] || dp[i - 1][j - topicScore];
                }
            }
        }

        if (!dp[n][targetScore]) {
            log.warn("DPAdjuster: Unable to find a subset of topics that exactly matches target score {}. No suitable subset found.", targetScore);
            return Collections.emptyList(); // Indicate failure to find a valid subset
        }

        List<Topic> result = new ArrayList<>();
        int remainingScore = targetScore;

        for (int i = n; i > 0 && remainingScore > 0; i--) {
            Topic currentTopic = topics.get(i - 1);
            int topicScore = getTopicScore(currentTopic, typeScores);
            if (topicScore < 0) topicScore = 0;

            if (topicScore > 0 && remainingScore >= topicScore && dp[i - 1][remainingScore - topicScore] && !dp[i-1][remainingScore]) {
                result.add(currentTopic);
                remainingScore -= topicScore;
            }
        }
        Collections.reverse(result);
        log.info("DPAdjuster: Successfully found a subset of {} topics matching target score {}. Original list size: {}.",
                 result.size(), targetScore, topics.size());
        return result;
    }

    /**
     * 寻找给定题目列表的一个子集，使其总分精确等于 {@code targetScore}，并且题型数量分布与 {@code targetTypeCounts} 一致。
     * <p>
     * 此方法使用动态规划来解决带有多重约束（分数和题型数量）的子集和问题。
     * </p>
     * <p>
     * DP状态设计：
     * {@code dp[s][typeKey]} 存储达到分数 {@code s} 且题型分布为 {@code typeKey} 时所选用的题目列表。
     * {@code typeKey} 是一个代表当前选定题目各类型数量的唯一标识符（例如，序列化后的字符串）。
     * </p>
     * <p>
     * 算法大致流程：
     * <ol>
     *   <li>初始化DP表：{@code dp[0][initialTypeKey]} 为一个空列表（表示0分，0题型计数是可达的，不需要题目）。</li>
     *   <li>遍历每道题目 {@code t}（分数 {@code score_t}，类型 {@code type_t}）。</li>
     *   <li>从后向前遍历分数 {@code s} (从 {@code targetScore} 到 {@code score_t})，以确保每道题只用一次。</li>
     *   <li>遍历当前分数 {@code s - score_t} 下所有已达的题型分布 {@code prevTypeKey}。</li>
     *   <li>计算加入题目 {@code t} 后的新题型分布 {@code newTypeKey} 和新分数 {@code s}。</li>
     *   <li>检查新题型分布是否超过 {@code targetTypeCounts} 中的限制。如果不超过，则可以更新 {@code dp[s][newTypeKey]}。</li>
     *   <li>最终，在 {@code dp[targetScore]} 中寻找与 {@code targetTypeCounts} 完全匹配的 {@code typeKey}。</li>
     *   <li>如果找到，则 {@code dp[targetScore][targetTypeKey]} 即为所求子集。</li>
     * </ol>
     * </p>
     *
     * @param topics 原始题目列表。
     * @param targetScore 目标总分。
     * @param typeScores 题型分数映射。
     * @param targetTypeCounts 目标题型数量映射。
     * @return 满足分数和题型约束的题目子集。
     *         If no such subset can be formed, returns an empty list.
     */
    private List<Topic> findOptimalSubsetWithTypeConstraints(List<Topic> topics, int targetScore,
                                                            Map<String, Integer> typeScores,
                                                            Map<String, Integer> targetTypeCounts) {
        // 性能优化: 如果题型约束很宽松，直接使用传统DP算法
        if (targetTypeCounts == null || targetTypeCounts.isEmpty()) {
            log.info("DPAdjuster: No type constraints provided, using traditional DP algorithm");
            return findOptimalSubset(topics, targetScore, typeScores);
        }

        log.info("DPAdjuster: Starting strict type-preserving optimization");

        // STEP 1: 确保首先满足题型约束 - 创建一个基础解
        List<Topic> mandatoryTopics = new ArrayList<>();
        Map<String, List<Topic>> topicsByType = topics.stream()
            .collect(Collectors.groupingBy(t -> TopicTypeMapper.normalize(t.getType()), Collectors.toList()));
        Map<String, Integer> currentTypeCounts = new HashMap<>();
        int currentTotalScore = 0;

        // 为每种题型选择精确数量的题目
        for (Map.Entry<String, Integer> entry : targetTypeCounts.entrySet()) {
            String type = entry.getKey();
            int targetCount = entry.getValue();

            if (targetCount <= 0) continue; // 跳过不需要的题型

            List<Topic> availableOfType = topicsByType.getOrDefault(type, Collections.emptyList());
            int availableCount = availableOfType.size();

            if (availableCount < targetCount) {
                log.warn("DPAdjuster: Not enough topics of type {}. Required: {}, Available: {}",
                         type, targetCount, availableCount);
                // 选择所有可用的
                mandatoryTopics.addAll(availableOfType);
                currentTypeCounts.put(type, availableCount);

                // 累加分数
                for (Topic t : availableOfType) {
                    currentTotalScore += getTopicScore(t, typeScores);
                }
            } else {
                // 有足够的题目，需要选择最佳子集
                // 按分数排序，首选选择分数接近或等于目标平均分的题目
                int targetTypeScore = (int)Math.round((double)targetScore * targetCount /
                                                    targetTypeCounts.values().stream().mapToInt(Integer::intValue).sum());
                int targetAvgScore = targetTypeScore / targetCount;

                // 按与目标平均分的接近程度排序
                List<Topic> sortedOfType = new ArrayList<>(availableOfType);
                final int finalTargetAvgScore = targetAvgScore;
                sortedOfType.sort(Comparator.comparingInt(t -> {
                    int score = typeScores.getOrDefault(type, t.getScore() != null ? t.getScore() : 0);
                    return Math.abs(score - finalTargetAvgScore);
                }));

                // 选择所需数量的题目
                List<Topic> selectedOfType = sortedOfType.subList(0, targetCount);
                mandatoryTopics.addAll(selectedOfType);
                currentTypeCounts.put(type, targetCount);

                // 累加分数
                for (Topic t : selectedOfType) {
                    currentTotalScore += getTopicScore(t, typeScores);
                }
            }
        }

        log.info("DPAdjuster: Initial selection with strict type constraints: {} topics, score={}, type distribution={}",
                 mandatoryTopics.size(), currentTotalScore, currentTypeCounts);

        // STEP 2: 如果当前总分与目标分数有偏差，尝试调整但保持题型数量不变
        int scoreDiff = currentTotalScore - targetScore;

        if (Math.abs(scoreDiff) <= 3) {
            // 如果分数接近，接受轻微偏差，优先保证题型分布
            log.info("DPAdjuster: Score {} is close to target {} (diff={}). Accepting minor score deviation to maintain type distribution.",
                     currentTotalScore, targetScore, scoreDiff);
            return mandatoryTopics;
        }

        // 尝试通过题目替换来调整分数
        if (scoreDiff != 0) {
            // 首先尝试严格保持题型数量的调整
            boolean strictSuccess = adjustScoreWithStrictTypePreserving(mandatoryTopics, targetScore, typeScores,
                                       currentTotalScore, currentTypeCounts, targetTypeCounts,
                                       topicsByType);

            // 如果严格调整失败，尝试更灵活的调整方式
            if (!strictSuccess) {
                log.info("DPAdjuster: Strict type-preserving adjustment failed, trying more flexible approach");
                adjustScoreWithTypePreserving(mandatoryTopics, targetScore, typeScores,
                                           currentTotalScore, currentTypeCounts, targetTypeCounts,
                                           topicsByType);
            }

            // 重新计算调整后分数
            int adjustedScore = mandatoryTopics.stream()
                .mapToInt(t -> getTopicScore(t, typeScores))
                .sum();

            // 重新计算调整后的题型分布
            Map<String, Long> adjustedTypeCounts = mandatoryTopics.stream()
                .collect(Collectors.groupingBy(
                    t -> TopicTypeMapper.normalize(t.getType()),
                    Collectors.counting()));

            log.info("DPAdjuster: After type-preserving adjustment: score={} (target={}), type distribution={}",
                     adjustedScore, targetScore, adjustedTypeCounts);
        }

        return mandatoryTopics;
    }

    /**
     * 严格保持题型数量的前提下调整总分。
     * 此方法确保每种题型的数量与目标数量完全一致，然后尝试通过替换题目来调整总分以逼近 targetScore。
     * <p>Note: This method uses a heuristic approach (greedy replacements) and may not always find an optimal solution
     * or any solution even if one exists, especially if the {@code allAvailableTopicsByType} pool is limited.</p>
     */
    private boolean adjustScoreWithStrictTypePreserving(List<Topic> topics, int targetScore, Map<String, Integer> typeScores,
                                                     int currentScore, Map<String, Integer> currentTypeCounts,
                                                     Map<String, Integer> targetTypeCounts,
                                                     Map<String, List<Topic>> allAvailableTopicsByType) {
        if (currentScore == targetScore) return true; // 已经达到目标

        boolean needToIncrease = currentScore < targetScore;
        int targetDiff = Math.abs(currentScore - targetScore);
        boolean improved = false;

        // 按题型分组当前选择的题目
        Map<String, List<Topic>> selectedTopicsByType = topics.stream()
            .collect(Collectors.groupingBy(t -> TopicTypeMapper.normalize(t.getType()), Collectors.toList()));

        // 对于每种题型，尝试替换题目以调整分数，但严格保持题型数量
        for (String type : targetTypeCounts.keySet()) {
            int targetTypeCount = targetTypeCounts.getOrDefault(type, 0);
            if (targetTypeCount <= 0) continue;

            int currentTypeCount = currentTypeCounts.getOrDefault(type, 0);
            if (currentTypeCount != targetTypeCount) {
                log.warn("DPAdjuster: Type count mismatch for {}: current={}, target={}. Cannot perform strict adjustment.",
                         type, currentTypeCount, targetTypeCount);
                return false; // 题型数量不匹配，无法进行严格调整
            }

            List<Topic> selectedOfType = selectedTopicsByType.getOrDefault(type, new ArrayList<>());
            List<Topic> availableOfType = allAvailableTopicsByType.getOrDefault(type, new ArrayList<>());

            // 从可选池中移除已选择的题目
            availableOfType = new ArrayList<>(availableOfType);
            availableOfType.removeAll(selectedOfType);

            if (availableOfType.isEmpty()) {
                log.debug("DPAdjuster: No additional topics of type {} available for replacement", type);
                continue;
            }

            if (needToIncrease) {
                // 需要增加分数：尝试用高分题替换低分题
                // 按分数升序排序已选题目（低分在前）
                selectedOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));
                // 按分数降序排序可选题目（高分在前）
                availableOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic lowScoreTopic = selectedOfType.get(i);
                    int lowScore = getTopicScore(lowScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic highScoreTopic = it.next();
                        int highScore = getTopicScore(highScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0) {
                            if (scoreDelta == targetDiff) {
                                // 完美匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(lowScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, highScoreTopic);
                                    log.debug("Perfect match! Replaced topic of type {} with score {} with topic with score {}",
                                             type, lowScore, highScore);
                                    return true; // 完美匹配，直接返回成功
                                }
                            } else if (scoreDelta < targetDiff) {
                                // 部分匹配，继续寻找更好的匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(lowScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, highScoreTopic);
                                    improved = true;
                                    targetDiff -= scoreDelta;
                                    log.debug("Replaced topic of type {} with score {} with topic with score {}, remaining diff: {}",
                                             type, lowScore, highScore, targetDiff);
                                    break; // 继续下一个低分题目
                                }
                            }
                            // 如果scoreDelta > targetDiff，跳过此题目，寻找更合适的
                        }
                    }
                }
            } else {
                // 需要减少分数：尝试用低分题替换高分题
                // 按分数降序排序已选题目（高分在前）
                selectedOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));
                // 按分数升序排序可选题目（低分在前）
                availableOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic highScoreTopic = selectedOfType.get(i);
                    int highScore = getTopicScore(highScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic lowScoreTopic = it.next();
                        int lowScore = getTopicScore(lowScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0) {
                            if (scoreDelta == targetDiff) {
                                // 完美匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(highScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, lowScoreTopic);
                                    log.debug("Perfect match! Replaced topic of type {} with score {} with topic with score {}",
                                             type, highScore, lowScore);
                                    return true; // 完美匹配，直接返回成功
                                }
                            } else if (scoreDelta < targetDiff) {
                                // 部分匹配，继续寻找更好的匹配
                                it.remove(); // 从可选池移除
                                int topicIndex = topics.indexOf(highScoreTopic);
                                if (topicIndex >= 0) {
                                    topics.set(topicIndex, lowScoreTopic);
                                    improved = true;
                                    targetDiff -= scoreDelta;
                                    log.debug("Replaced topic of type {} with score {} with topic with score {}, remaining diff: {}",
                                             type, highScore, lowScore, targetDiff);
                                    break; // 继续下一个高分题目
                                }
                            }
                            // 如果scoreDelta > targetDiff，跳过此题目，寻找更合适的
                        }
                    }
                }
            }
        }

        return improved;
    }

    /**
     * 在保持题型分布的前提下调整总分。
     * 此方法允许题型数量有一定的灵活性，主要通过替换题目来调整总分，如果替换无法满足，则可能尝试添加或移除题目（较少）。
     * <p>Note: This method uses a heuristic approach (greedy replacements/additions/removals) and may not always find an optimal solution
     * or any solution even if one exists, especially if the {@code allAvailableTopicsByType} pool is limited.</p>
     */
    private boolean adjustScoreWithTypePreserving(List<Topic> topics, int targetScore, Map<String, Integer> typeScores,
                                                int currentScore, Map<String, Integer> currentTypeCounts,
                                                Map<String, Integer> targetTypeCounts,
                                                Map<String, List<Topic>> allAvailableTopicsByType) {
        if (currentScore == targetScore) return true; // 已经达到目标

        boolean needToIncrease = currentScore < targetScore;
        int targetDiff = Math.abs(currentScore - targetScore);
        boolean improved = false;

        // 按题型分组当前选择的题目
        Map<String, List<Topic>> selectedTopicsByType = topics.stream()
            .collect(Collectors.groupingBy(t -> TopicTypeMapper.normalize(t.getType()), Collectors.toList()));

        // 对于每种题型，尝试替换题目以调整分数
        for (String type : targetTypeCounts.keySet()) {
            int targetTypeCount = targetTypeCounts.getOrDefault(type, 0);
            if (targetTypeCount <= 0) continue;

            int currentTypeCount = currentTypeCounts.getOrDefault(type, 0);
            if (currentTypeCount < targetTypeCount) continue; // 题型数量不足，不能调整

            List<Topic> selectedOfType = selectedTopicsByType.getOrDefault(type, new ArrayList<>());
            List<Topic> availableOfType = allAvailableTopicsByType.getOrDefault(type, new ArrayList<>());

            // 从可选池中移除已选择的题目
            availableOfType = new ArrayList<>(availableOfType);
            availableOfType.removeAll(selectedOfType);

            if (needToIncrease) {
                // 需要增加分数：尝试用高分题替换低分题
                selectedOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));
                if (availableOfType.isEmpty()) {
                    log.debug("DPAdjuster (Type Preserving): No available topics of type {} in allAvailableTopicsByType for potential swaps to increase score.", type);
                }
                availableOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic lowScoreTopic = selectedOfType.get(i);
                    int lowScore = getTopicScore(lowScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic highScoreTopic = it.next();
                        int highScore = getTopicScore(highScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0 && scoreDelta <= targetDiff) {
                            // 可以替换
                            it.remove(); // 从可选池移除
                            int topicIndex = topics.indexOf(lowScoreTopic);
                            if (topicIndex >= 0) {
                                topics.set(topicIndex, highScoreTopic);
                                improved = true;
                                targetDiff -= scoreDelta;
                                log.debug("DPAdjuster (Type Preserving - Increase): Replaced topic ID: {} (score: {}) with topic ID: {} (score: {}), type: {}, remaining diff: {}", 
                                          lowScoreTopic.getId(), lowScore, highScoreTopic.getId(), highScore, type, targetDiff);

                                // 如果已达到目标，退出
                                if (targetDiff == 0) return true;
                                break;
                            }
                        }
                    }
                }
            } else {
                // 需要减少分数：尝试用低分题替换高分题
                selectedOfType.sort(Comparator.comparingInt(t -> -getTopicScore(t, typeScores)));
                if (availableOfType.isEmpty()) {
                    log.debug("DPAdjuster (Type Preserving): No available topics of type {} in allAvailableTopicsByType for potential swaps to decrease score.", type);
                }
                availableOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));

                // 尝试替换
                for (int i = 0; i < selectedOfType.size() && !availableOfType.isEmpty(); i++) {
                    Topic highScoreTopic = selectedOfType.get(i);
                    int highScore = getTopicScore(highScoreTopic, typeScores);

                    for (Iterator<Topic> it = availableOfType.iterator(); it.hasNext(); ) {
                        Topic lowScoreTopic = it.next();
                        int lowScore = getTopicScore(lowScoreTopic, typeScores);

                        int scoreDelta = highScore - lowScore;
                        if (scoreDelta > 0 && scoreDelta <= targetDiff) {
                            // 可以替换
                            it.remove(); // 从可选池移除
                            int topicIndex = topics.indexOf(highScoreTopic);
                            if (topicIndex >= 0) {
                                topics.set(topicIndex, lowScoreTopic);
                                improved = true;
                                targetDiff -= scoreDelta;
                                log.debug("DPAdjuster (Type Preserving - Decrease): Replaced topic ID: {} (score: {}) with topic ID: {} (score: {}), type: {}, remaining diff: {}", 
                                          highScoreTopic.getId(), highScore, lowScoreTopic.getId(), lowScore, type, targetDiff);

                                // 如果已达到目标，退出
                                if (targetDiff == 0) return true;
                                break;
                            }
                        } else {
                            log.debug("Skipping topic ID: {} (score: {}) as score delta {} is not within target diff {}", 
                                      lowScoreTopic.getId(), lowScore, scoreDelta, targetDiff);
                        }
                    }
                }
            }
        }

        return improved;
    }
    
    /**
     * 填充缺失的题型
     * <p>
     * 此方法从可用题目中为每个缺失的题型添加题目，直到满足目标数量或用完可用题目
     * </p>
     * 
     * @param selectedTopics 当前选中的题目
     * @param targetTypeCounts 目标题型数量
     * @param availableTopics 可用于补充的所有题目
     * @param typeScores 题型分数映射
     * @return 补充后的题目列表
     */
    /**
     * 尝试在固定题型计数下，通过替换更高分题目来提升总分，直到不超过 targetScore。
     */
    private List<Topic> greedyIncreaseScore(List<Topic> currentSelection,
                                            List<Topic> availableAll,
                                            Map<String, Integer> typeScores,
                                            int targetScore) {
        // 组织当前题目按题型分组并按分数升序
        Map<String, List<Topic>> currentByType = currentSelection.stream().collect(Collectors.groupingBy(
                t -> TopicTypeMapper.normalize(t.getType())));

        Map<String, List<Topic>> candidatesByType = availableAll.stream()
                .filter(t -> currentSelection.stream().noneMatch(sel -> sel.getId().equals(t.getId())))
                .collect(Collectors.groupingBy(t -> TopicTypeMapper.normalize(t.getType())));

        int currentTotal = currentSelection.stream().mapToInt(t -> getTopicScore(t, typeScores)).sum();
        if (currentTotal >= targetScore) {
            return currentSelection; // Already ok
        }

        // 复制列表用于结果
        List<Topic> working = new ArrayList<>(currentSelection);

        // 尝试每种题型单点替换
        boolean improved = true;
        while (improved && currentTotal < targetScore) {
            improved = false;
            String bestType = null;
            int bestDelta = 0;
            Topic replaceOut = null;
            Topic replaceIn = null;

            for (Map.Entry<String, List<Topic>> entry : candidatesByType.entrySet()) {
                String type = entry.getKey();
                List<Topic> candidateList = entry.getValue();
                if (candidateList.isEmpty()) continue;
                // highest scoring candidate not in current selection
                candidateList.sort((a, b) -> Integer.compare(getTopicScore(b, typeScores), getTopicScore(a, typeScores)));
                Topic highest = candidateList.get(0);
                int inScore = getTopicScore(highest, typeScores);
                // find lowest scoring current topic of same type
                List<Topic> currentOfType = currentByType.getOrDefault(type, Collections.emptyList());
                if (currentOfType.isEmpty()) continue;
                currentOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));
                Topic lowest = currentOfType.get(0);
                int outScore = getTopicScore(lowest, typeScores);
                int delta = inScore - outScore;
                if (delta <= 0) continue;
                if (currentTotal + delta > targetScore) continue; // would overshoot
                // choose best delta
                if (delta > bestDelta) {
                    bestDelta = delta;
                    bestType = type;
                    replaceOut = lowest;
                    replaceIn = highest;
                }
            }
            if (bestDelta > 0 && replaceIn != null && replaceOut != null) {
                working.remove(replaceOut);
                working.add(replaceIn);
                // update maps & totals
                currentByType.get(bestType).remove(replaceOut);
                currentByType.get(bestType).add(replaceIn);
                candidatesByType.get(bestType).remove(replaceIn);
                candidatesByType.get(bestType).add(replaceOut); // replaced topic becomes candidate
                currentTotal += bestDelta;
                improved = true;
            }
        }
        return working;
    }

    private List<Topic> fillMissingTypes(List<Topic> selectedTopics, Map<String, Integer> targetTypeCounts, 
                                     List<Topic> availableTopics, Map<String, Integer> typeScores) {
        // 统计当前选定题目的题型分布
        Map<String, List<Topic>> currentTypeDistribution = new HashMap<>();
        for (Topic topic : selectedTopics) {
            String type = TopicTypeMapper.normalize(topic.getType());
            currentTypeDistribution.computeIfAbsent(type, k -> new ArrayList<>()).add(topic);
        }
        
        // 查找缺失的题型及其缺少的数量
        Map<String, Integer> missingTypeCounts = new HashMap<>();
        for (Map.Entry<String, Integer> entry : targetTypeCounts.entrySet()) {
            String type = entry.getKey();
            int targetCount = entry.getValue();
            int currentCount = currentTypeDistribution.getOrDefault(type, Collections.emptyList()).size();
            
            if (currentCount < targetCount) {
                missingTypeCounts.put(type, targetCount - currentCount);
                log.info("DPAdjuster: Type '{}' is missing {} questions", type, targetCount - currentCount);
            }
        }
        
        if (missingTypeCounts.isEmpty()) {
            log.debug("DPAdjuster: No missing types to fill");
            return selectedTopics;
        }
        
        // 按题型分组可用题目，但排除已选择的题目
        Set<Integer> selectedIds = selectedTopics.stream()
            .map(Topic::getId)
            .collect(Collectors.toSet());
            
        Map<String, List<Topic>> availableByType = availableTopics.stream()
            .filter(t -> !selectedIds.contains(t.getId()))
            .collect(Collectors.groupingBy(
                t -> TopicTypeMapper.normalize(t.getType()), 
                Collectors.toList()
            ));
        
        // 填充每个缺失的题型
        List<Topic> result = new ArrayList<>(selectedTopics);
        int addedCount = 0;
        
        for (Map.Entry<String, Integer> entry : missingTypeCounts.entrySet()) {
            String type = entry.getKey();
            int needed = entry.getValue();
            List<Topic> availableOfType = availableByType.getOrDefault(type, Collections.emptyList());
            
            if (availableOfType.isEmpty()) {
                log.warn("DPAdjuster: Cannot fill missing type '{}', no available topics of this type", type);
                continue;
            }
            
            // 根据分数排序，先添加分数较低的题目以减少对总分的影响
            availableOfType.sort(Comparator.comparingInt(t -> getTopicScore(t, typeScores)));
            
            int added = 0;
            for (Topic topic : availableOfType) {
                if (added >= needed) break;
                result.add(topic);
                added++;
                addedCount++;
                log.info("DPAdjuster: Added topic ID: {} of type '{}' to fill missing type requirement", 
                         topic.getId(), type);
            }
            
            if (added < needed) {
                log.warn("DPAdjuster: Could only add {} out of {} needed topics for type '{}'",
                         added, needed, type);
            }
        }
        
        log.info("DPAdjuster: Added a total of {} topics to fill missing types", addedCount);
        return result;
    }
}