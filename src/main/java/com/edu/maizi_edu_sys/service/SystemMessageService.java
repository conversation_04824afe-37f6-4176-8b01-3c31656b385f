package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.entity.SystemMessage;

import java.util.List;

/**
 * 系统消息服务接口
 */
public interface SystemMessageService {

    /**
     * 发送审核通过消息
     */
    void sendAuditApprovedMessage(Long userId, Long auditId, String topicTitle);

    /**
     * 发送审核拒绝消息
     */
    void sendAuditRejectedMessage(Long userId, Long auditId, String topicTitle, String rejectReason);

    /**
     * 发送自动审核通过消息
     */
    void sendAutoApprovedMessage(Long userId, Long auditId, String topicTitle);

    /**
     * 发送系统通知
     */
    void sendSystemNotice(Long userId, String title, String content, Integer priority);

    /**
     * 批量发送系统通知
     */
    void sendSystemNoticeToAll(String title, String content, Integer priority);

    /**
     * 分页查询用户消息
     */
    IPage<SystemMessage> getUserMessages(Long userId, int pageNum, int pageSize, Boolean isRead);

    /**
     * 获取用户未读消息数量
     */
    Long getUnreadMessageCount(Long userId);

    /**
     * 标记消息为已读
     */
    void markMessageAsRead(Long messageId, Long userId);

    /**
     * 标记所有消息为已读
     */
    void markAllMessagesAsRead(Long userId);

    /**
     * 获取消息详情
     */
    SystemMessage getMessageDetail(Long messageId, Long userId);

    /**
     * 删除消息
     */
    void deleteMessage(Long messageId, Long userId);

    /**
     * 获取最近的系统通知
     */
    List<SystemMessage> getRecentNotices(Long userId, int limit);
}
