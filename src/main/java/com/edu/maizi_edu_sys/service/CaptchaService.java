package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;

/**
 * 验证码服务接口
 */
public interface CaptchaService {
    
    /**
     * 生成验证码
     * @return 验证码响应，包含图片Base64和会话ID
     */
    ApiResponse<?> generateCaptcha();
    
    /**
     * 刷新验证码
     * @param sessionId 会话ID（可选）
     * @return 新的验证码响应
     */
    ApiResponse<?> refreshCaptcha(String sessionId);
    
    /**
     * 验证验证码
     * @param sessionId 会话ID
     * @param captchaCode 用户输入的验证码
     * @return 验证结果
     */
    boolean verifyCaptcha(String sessionId, String captchaCode);
    
    /**
     * 清理过期的验证码
     */
    void cleanExpiredCaptcha();
}
