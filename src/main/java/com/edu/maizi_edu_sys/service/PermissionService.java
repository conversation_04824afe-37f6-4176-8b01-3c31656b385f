package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.RolePermission;
import com.edu.maizi_edu_sys.entity.UserPermission;

import java.util.List;

/**
 * 权限管理服务接口
 */
public interface PermissionService {

    /**
     * 检查用户是否有特定权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 检查用户是否可以访问特定API路径
     */
    boolean canAccessApi(Long userId, String apiPath);

    /**
     * 获取用户的所有权限
     */
    List<UserPermission> getUserPermissions(Long userId);

    /**
     * 获取角色的所有权限
     */
    List<RolePermission> getRolePermissions(Integer roleId);

    /**
     * 为用户授予权限
     */
    void grantPermissionToUser(Long userId, String permissionCode, Long grantedBy);

    /**
     * 撤销用户权限
     */
    void revokePermissionFromUser(Long userId, String permissionCode);

    /**
     * 检查用户角色权限
     */
    boolean hasRolePermission(Integer roleId, String permissionCode);

    /**
     * 获取用户可访问的API路径列表
     */
    List<String> getUserApiPaths(Long userId);

    /**
     * 获取角色可访问的API路径列表
     */
    List<String> getRoleApiPaths(Integer roleId);

    /**
     * 检查用户是否为管理员
     */
    boolean isAdmin(Long userId);

    /**
     * 检查用户是否为超级管理员
     */
    boolean isSuperAdmin(Long userId);

    /**
     * 获取所有权限代码
     */
    List<String> getAllPermissionCodes();
}
