package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.User;

/**
 * 管理员安全服务接口
 */
public interface AdminSecurityService {
    
    /**
     * 检查用户是否为管理员
     */
    boolean isAdmin(Long userId);
    
    /**
     * 检查用户是否为活跃的管理员
     */
    boolean isActiveAdmin(Long userId);
    
    /**
     * 获取管理员用户信息
     */
    User getAdminUser(Long userId);
    
    /**
     * 记录管理员访问日志
     */
    void logAdminAccess(Long userId, String action, String resource);
    
    /**
     * 检查管理员权限
     */
    boolean hasAdminPermission(Long userId, String permission);
}
