package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.maizi_edu_sys.entity.UserUploadStats;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户上传统计服务接口
 */
public interface UserUploadStatsService extends IService<UserUploadStats> {

    /**
     * 记录用户上传题目
     * @param userId 用户ID
     * @param count 上传数量
     */
    void recordUserUpload(Long userId, Integer count);

    /**
     * 获取用户今日上传数量
     * @param userId 用户ID
     * @return 今日上传数量
     */
    Integer getUserTodayUploadCount(Long userId);

    /**
     * 获取用户指定日期的上传数量
     * @param userId 用户ID
     * @param date 日期
     * @return 上传数量
     */
    Integer getUserUploadCountByDate(Long userId, LocalDate date);

    /**
     * 检查用户是否超过每日上传限制
     * @param userId 用户ID
     * @param additionalCount 额外要上传的数量
     * @return 是否超过限制
     */
    boolean isExceedDailyLimit(Long userId, Integer additionalCount);

    /**
     * 获取用户上传统计概览
     * @param userId 用户ID
     * @return 统计概览
     */
    Map<String, Object> getUserUploadOverview(Long userId);

    /**
     * 获取用户最近N天的上传趋势
     * @param userId 用户ID
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getUserUploadTrend(Long userId, Integer days);

    /**
     * 获取用户指定日期范围的上传统计
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    List<Map<String, Object>> getUserUploadStatsByDateRange(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取管理员统计概览
     * @return 管理员统计概览
     */
    Map<String, Object> getAdminUploadOverview();

    /**
     * 获取全站最近N天的上传趋势
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getTotalUploadTrend(Integer days);

    /**
     * 获取用户上传排行榜
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行榜数据
     */
    List<Map<String, Object>> getUserUploadRanking(LocalDate startDate, LocalDate endDate, Integer limit);

    /**
     * 获取指定日期的全站上传统计
     * @param date 日期
     * @return 统计数据
     */
    List<Map<String, Object>> getTotalUploadStatsByDate(LocalDate date);

    /**
     * 获取指定日期范围的全站上传统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    List<Map<String, Object>> getTotalUploadStatsByDateRange(LocalDate startDate, LocalDate endDate);
}
