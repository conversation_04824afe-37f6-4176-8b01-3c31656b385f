package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CaptchaServiceImpl implements CaptchaService {

    private final RedisTemplate<String, Object> redisTemplate;
    
    @Value("${captcha.enabled:true}")
    private boolean captchaEnabled;
    
    @Value("${captcha.length:4}")
    private int captchaLength;
    
    @Value("${captcha.width:120}")
    private int captchaWidth;
    
    @Value("${captcha.height:40}")
    private int captchaHeight;
    
    @Value("${captcha.expire-time:300}")
    private int expireTime;
    
    @Value("${captcha.font-size:25}")
    private int fontSize;
    
    @Value("${captcha.interference-line:3}")
    private int interferenceLine;
    
    private static final String CAPTCHA_PREFIX = "captcha:";
    private static final String CAPTCHA_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    
    @Override
    public ApiResponse<?> generateCaptcha() {
        if (!captchaEnabled) {
            log.debug("验证码功能已禁用");
            return ApiResponse.success(createDisabledCaptchaResponse());
        }
        
        try {
            String sessionId = UUID.randomUUID().toString();
            String captchaCode = generateRandomCode();
            String captchaImage = createCaptchaImage(captchaCode);
            
            // 存储到Redis
            redisTemplate.opsForValue().set(
                CAPTCHA_PREFIX + sessionId, 
                captchaCode.toLowerCase(), 
                expireTime, 
                TimeUnit.SECONDS
            );
            
            Map<String, Object> data = new HashMap<>();
            data.put("sessionId", sessionId);
            data.put("captchaImage", "data:image/png;base64," + captchaImage);
            
            log.debug("生成验证码成功，会话ID: {}", sessionId);
            return ApiResponse.success(data);
            
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            return ApiResponse.error("验证码生成失败");
        }
    }
    
    @Override
    public ApiResponse<?> refreshCaptcha(String sessionId) {
        if (!captchaEnabled) {
            return ApiResponse.success(createDisabledCaptchaResponse());
        }
        
        try {
            // 如果提供了sessionId，先删除旧的验证码
            if (sessionId != null && !sessionId.isEmpty()) {
                redisTemplate.delete(CAPTCHA_PREFIX + sessionId);
            }
            
            // 生成新的验证码
            return generateCaptcha();
            
        } catch (Exception e) {
            log.error("刷新验证码失败", e);
            return ApiResponse.error("验证码刷新失败");
        }
    }
    
    @Override
    public boolean verifyCaptcha(String sessionId, String captchaCode) {
        if (!captchaEnabled) {
            log.debug("验证码功能已禁用，跳过验证");
            return true;
        }
        
        if (sessionId == null || sessionId.isEmpty() || captchaCode == null || captchaCode.isEmpty()) {
            log.warn("验证码验证失败：参数为空");
            return false;
        }
        
        try {
            String storedCode = (String) redisTemplate.opsForValue().get(CAPTCHA_PREFIX + sessionId);
            if (storedCode == null) {
                log.warn("验证码验证失败：验证码已过期或不存在，会话ID: {}", sessionId);
                return false;
            }
            
            // 验证后删除验证码（一次性使用）
            redisTemplate.delete(CAPTCHA_PREFIX + sessionId);
            
            boolean isValid = storedCode.equalsIgnoreCase(captchaCode.trim());
            if (!isValid) {
                log.warn("验证码验证失败：验证码错误，会话ID: {}, 输入: {}, 期望: {}", 
                        sessionId, captchaCode, storedCode);
            } else {
                log.debug("验证码验证成功，会话ID: {}", sessionId);
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("验证码验证异常", e);
            return false;
        }
    }
    
    @Override
    public void cleanExpiredCaptcha() {
        // Redis会自动清理过期的key，这里可以添加额外的清理逻辑
        log.debug("清理过期验证码（Redis自动处理）");
    }
    
    /**
     * 生成随机验证码
     */
    private String generateRandomCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < captchaLength; i++) {
            code.append(CAPTCHA_CHARS.charAt(random.nextInt(CAPTCHA_CHARS.length())));
        }
        return code.toString();
    }
    
    /**
     * 创建验证码图片
     */
    private String createCaptchaImage(String captchaCode) throws IOException {
        BufferedImage image = new BufferedImage(captchaWidth, captchaHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 填充背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, captchaWidth, captchaHeight);
        
        // 设置字体
        Font font = new Font("Arial", Font.BOLD, fontSize);
        g2d.setFont(font);
        
        // 绘制验证码字符
        Random random = new Random();
        for (int i = 0; i < captchaCode.length(); i++) {
            // 随机颜色
            g2d.setColor(new Color(random.nextInt(150), random.nextInt(150), random.nextInt(150)));
            
            // 随机位置和角度
            int x = (captchaWidth / captchaLength) * i + 10;
            int y = captchaHeight / 2 + fontSize / 3;
            
            // 轻微旋转
            double angle = (random.nextDouble() - 0.5) * 0.4;
            g2d.rotate(angle, x, y);
            g2d.drawString(String.valueOf(captchaCode.charAt(i)), x, y);
            g2d.rotate(-angle, x, y);
        }
        
        // 添加干扰线
        for (int i = 0; i < interferenceLine; i++) {
            g2d.setColor(new Color(random.nextInt(200), random.nextInt(200), random.nextInt(200)));
            int x1 = random.nextInt(captchaWidth);
            int y1 = random.nextInt(captchaHeight);
            int x2 = random.nextInt(captchaWidth);
            int y2 = random.nextInt(captchaHeight);
            g2d.drawLine(x1, y1, x2, y2);
        }
        
        g2d.dispose();
        
        // 转换为Base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }
    
    /**
     * 创建禁用状态的验证码响应
     */
    private Map<String, Object> createDisabledCaptchaResponse() {
        Map<String, Object> data = new HashMap<>();
        data.put("sessionId", "disabled");
        data.put("captchaImage", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
        return data;
    }
}
