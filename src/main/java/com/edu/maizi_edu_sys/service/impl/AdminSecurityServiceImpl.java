package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.service.AdminSecurityService;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 管理员安全服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminSecurityServiceImpl implements AdminSecurityService {

    private final UserService userService;
    private final PermissionService permissionService;

    @Override
    public boolean isAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        
        try {
            User user = userService.getById(userId);
            return user != null && user.getRole() == 1; // role=1 为管理员
        } catch (Exception e) {
            log.error("检查管理员权限失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public boolean isActiveAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        
        try {
            User user = userService.getById(userId);
            return user != null && 
                   user.getRole() == 1 &&      // 是管理员
                   user.getStatus() == 1 &&    // 状态正常
                   user.getDeleted() == 0;     // 未删除
        } catch (Exception e) {
            log.error("检查活跃管理员权限失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public User getAdminUser(Long userId) {
        if (!isActiveAdmin(userId)) {
            return null;
        }
        
        try {
            return userService.getById(userId);
        } catch (Exception e) {
            log.error("获取管理员用户信息失败: userId={}", userId, e);
            return null;
        }
    }

    @Override
    public void logAdminAccess(Long userId, String action, String resource) {
        try {
            User admin = getAdminUser(userId);
            if (admin != null) {
                log.info("管理员操作日志: 用户[{}({})] 执行操作[{}] 访问资源[{}]", 
                        admin.getUsername(), userId, action, resource);
                
                // 这里可以扩展为写入专门的管理员操作日志表
                // 记录更详细的操作信息，如IP地址、时间戳、操作结果等
            }
        } catch (Exception e) {
            log.error("记录管理员访问日志失败: userId={}, action={}, resource={}", 
                    userId, action, resource, e);
        }
    }

    @Override
    public boolean hasAdminPermission(Long userId, String permission) {
        if (!isActiveAdmin(userId)) {
            return false;
        }
        
        try {
            // 管理员默认拥有所有权限，但也可以通过权限服务进行细粒度控制
            return permissionService.hasPermission(userId, permission);
        } catch (Exception e) {
            log.error("检查管理员权限失败: userId={}, permission={}", userId, permission, e);
            return false;
        }
    }
}
