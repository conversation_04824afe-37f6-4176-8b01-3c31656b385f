package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.dto.PaperGenerationRequest;
import com.edu.maizi_edu_sys.dto.PaperGenerationResponse;
import com.edu.maizi_edu_sys.dto.PaperDetailDTO;
import com.edu.maizi_edu_sys.dto.PaginationResponse;
import com.edu.maizi_edu_sys.dto.BatchPaperGenerationRequest;
import com.edu.maizi_edu_sys.dto.BatchPaperGenerationResponse;
import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.entity.PaperDownload;
import com.edu.maizi_edu_sys.repository.PaperRepository;
import com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.service.PaperGenerationService;
import com.edu.maizi_edu_sys.service.engine.DPAdjuster;
import com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine;
import com.edu.maizi_edu_sys.service.engine.KnowledgePointUsageTracker;
import com.edu.maizi_edu_sys.service.recommend.QualityScorer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.edu.maizi_edu_sys.repository.PaperDownloadRepository;
import com.edu.maizi_edu_sys.service.KnowledgePointQuestionService;
import java.util.HashMap;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.scilab.forge.jlatexmath.TeXConstants;

import org.springframework.stereotype.Service;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.itextpdf.text.*;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.text.BaseColor;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;

import com.edu.maizi_edu_sys.dto.CustomPaperRequest;
import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.dto.PaperPreviewRequest;
import com.edu.maizi_edu_sys.dto.PaperPreviewResponse;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;

@Service
@Slf4j
public class PaperGenerationServiceImpl implements PaperGenerationService {

    private final PaperGenerationEngine engine;
    private final QualityScorer qualityScorer;
    private final TopicEnhancementDataMapper enhancementDataMapper;
    private final PaperRepository paperRepository;
    private final TopicMapper topicMapper;
    private final DPAdjuster dpAdjuster;
    private final PaperDownloadRepository paperDownloadRepository;
    private final KnowledgePointQuestionService knowledgePointQuestionService;
    private final KnowledgePointUsageTracker knowledgePointUsageTracker;
    private final ObjectMapper mapper = new ObjectMapper();

    // private static final Pattern LATEX_PATTERN = Pattern.compile(
    //     "(" + Pattern.quote("\\[") + ".*?" + Pattern.quote("\\]") + "|" +
    //     Pattern.quote("\\(") + ".*?" + Pattern.quote("\\)") + "|" +
    //     Pattern.quote("$$") + ".*?" + Pattern.quote("$$") + "|" +
    //     Pattern.quote("$") + ".*?" + Pattern.quote("$") + ")"
    // );

    private static final Map<String, String> CHINESE_TOPIC_TYPE_NAMES = new HashMap<>();
    static {
        CHINESE_TOPIC_TYPE_NAMES.put("singleChoice", "单选题");
        CHINESE_TOPIC_TYPE_NAMES.put("multipleChoice", "多选题");
        CHINESE_TOPIC_TYPE_NAMES.put("judgment", "判断题");
        CHINESE_TOPIC_TYPE_NAMES.put("shortAnswer", "简答题");
        CHINESE_TOPIC_TYPE_NAMES.put("fillBlank", "填空题");
        CHINESE_TOPIC_TYPE_NAMES.put("subjective", "主观题");
        CHINESE_TOPIC_TYPE_NAMES.put("groupQuestion", "组合题");
    }

    public PaperGenerationServiceImpl(
            PaperGenerationEngine engine,
            QualityScorer qualityScorer,
            TopicEnhancementDataMapper enhancementDataMapper,
            PaperRepository paperRepository,
            TopicMapper topicMapper,
            DPAdjuster dpAdjuster,
            PaperDownloadRepository paperDownloadRepository,
            KnowledgePointQuestionService knowledgePointQuestionService,
            KnowledgePointUsageTracker knowledgePointUsageTracker) {
        this.engine = engine;
        this.qualityScorer = qualityScorer;
        this.enhancementDataMapper = enhancementDataMapper;
        this.paperRepository = paperRepository;
        this.topicMapper = topicMapper;
        this.dpAdjuster = dpAdjuster;
        this.paperDownloadRepository = paperDownloadRepository;
        this.knowledgePointQuestionService = knowledgePointQuestionService;
        this.knowledgePointUsageTracker = knowledgePointUsageTracker;
    }

    /**
     * 保存试卷下载记录
     *
     * @param download 试卷下载记录
     * @return 保存后的试卷下载记录
     */
    @Override
    @Transactional
    public PaperDownload savePaperDownload(PaperDownload download) {
        return paperDownloadRepository.save(download);
    }

    /**
     * 根据ID获取试卷
     *
     * @param id 试卷ID
     * @return 试卷实体，如果不存在返回null
     */
    @Override
    public Paper getPaperById(Long id) {
        return paperRepository.findByIdAndIsDeletedFalse(id).orElse(null);
    }

    @Override
    @Transactional
    public PaperGenerationResponse generatePaper(PaperGenerationRequest request) {
        log.info("Generating paper with detailed request: Title='{}', KnowledgePointConfigs={}, GlobalTypeCounts={}, GlobalTypeScores={}, DifficultyCriteria={}",
                request.getTitle(),
                request.getKnowledgePointConfigs(),
                request.getTopicTypeCounts(),
                request.getTypeScoreMap(),
                request.getDifficultyCriteria());

        PaperGenerationResponse response = new PaperGenerationResponse();

        List<Topic> selectedTopics = engine.generatePaper(request);

        Map<String, Integer> requestedGlobalCounts = request.getTopicTypeCounts();
        log.info("Using topics directly from engine. Count: {}. Requested global counts (for warning reference): {}", selectedTopics.size(), requestedGlobalCounts);
        List<Topic> enforcedTopics = enforceTypeCounts(selectedTopics, requestedGlobalCounts);

        int targetTotalScore = request.getTotalScore() != null ? request.getTotalScore() : calculateExpectedScore(request.getTypeScoreMap(), requestedGlobalCounts);
        log.info("Applying DP adjustment to optimize total score (target: {}) after enforcing type counts...", targetTotalScore);

        // Normalize targetTypeCounts keys for DPAdjuster
        Map<String, Integer> normalizedTargetTypeCounts = new HashMap<>();
        if (requestedGlobalCounts != null) {
            for (Map.Entry<String, Integer> entry : requestedGlobalCounts.entrySet()) {
                normalizedTargetTypeCounts.put(TopicTypeMapper.normalize(entry.getKey()), entry.getValue());
            }
        }
        log.info("DPAdjuster will receive targetTypeCounts (normalized keys): {}", normalizedTargetTypeCounts);

        enforcedTopics = dpAdjuster.adjust(enforcedTopics, targetTotalScore, request.getTypeScoreMap(), normalizedTargetTypeCounts);

        // 再次强制执行题型数量约束，确保DP调整不会改变题型数量
        log.info("Re-enforcing exact type counts after DP adjustment to ensure strict adherence to requested counts");
        enforcedTopics = enforceTypeCounts(enforcedTopics, requestedGlobalCounts);

        // 使用目标题型数量和分值计算总分，而不是基于实际题目
        int actualScoreAfterDP = calculateExpectedScore(request.getTypeScoreMap(), requestedGlobalCounts);
        log.info("After final enforcement: {} topics, calculated score: {} (target score: {})", enforcedTopics.size(), actualScoreAfterDP, targetTotalScore);

        // 统计实际题型数量，用于调试
        Map<String, Long> finalTypeCounts = enforcedTopics.stream()
                .collect(Collectors.groupingBy(t -> mapTopicType(t.getType()), Collectors.counting()));
        log.info("Final type counts: {}, Requested counts: {}", finalTypeCounts, requestedGlobalCounts);

        if (enforcedTopics == null || enforcedTopics.isEmpty()) {
            log.warn("No topics selected or enforced for paper generation. Title: {}", request.getTitle());
            response.setSuccess(false);
            response.setErrorMessage("无法根据当前条件筛选出足够的题目，请调整设置或扩充题库。");
            return response;
        }

        Map<String, Long> actualCounts = enforcedTopics.stream()
                .collect(Collectors.groupingBy(t -> mapTopicType(t.getType()), Collectors.counting()));

        int totalActualCount = actualCounts.values().stream().mapToInt(Long::intValue).sum();
        int totalRequestedCount = requestedGlobalCounts != null ?
                requestedGlobalCounts.values().stream().mapToInt(Integer::intValue).sum() : 0;

        StringBuilder warningMsg = new StringBuilder();
        if (totalActualCount < totalRequestedCount) {
            StringBuilder typeDetailBuilder = new StringBuilder();
            if (requestedGlobalCounts != null) {
                typeDetailBuilder.append("\n具体题型统计：");
                for (Map.Entry<String, Integer> entry : requestedGlobalCounts.entrySet()) {
                    String type = mapTopicType(entry.getKey());
                    int requested = entry.getValue();
                    long available = actualCounts.getOrDefault(type, 0L);
                    String chineseTypeName = getChineseTopicTypeName(type);

                    typeDetailBuilder.append("\n- ").append(chineseTypeName);
                    typeDetailBuilder.append(": 请求").append(requested);
                    typeDetailBuilder.append("/实际").append(available);

                    if (available < requested) {
                        typeDetailBuilder.append(" (不足" + (requested - available) + ")");
                    }
                }
            }

            String detailedWarning = String.format(
                    "题库中可用题目不足。\n请求总题目数：%d，实际可用题目数：%d。\n这可能是因为选择的知识点中没有足够的题目。%s",
                    totalRequestedCount, totalActualCount, typeDetailBuilder.toString());
            log.warn(detailedWarning);
            warningMsg.append("题库中可用题目不足，请求" + totalRequestedCount + "题，实际可用" + totalActualCount + "题。");
            warningMsg.append("\n\n").append("详细题型统计：").append(typeDetailBuilder.toString());
        }

        if (requestedGlobalCounts != null) {
            for (Map.Entry<String, Integer> requested : requestedGlobalCounts.entrySet()) {
                String type = mapTopicType(requested.getKey());
                long actual = actualCounts.getOrDefault(type, 0L);
                if (actual < requested.getValue()) {
                    String warningText = String.format("请注意：%s数量不足，请求%d题，实际仅有%d题。(全局配置)",
                            getChineseTopicTypeName(type), requested.getValue(), actual);
                    log.warn(warningText);
                    if (warningMsg.length() > 0) warningMsg.append("\n");
                    warningMsg.append(warningText);
                }
            }
        }

        Map<String, Integer> typeScoreMapFromRequest = request.getTypeScoreMap();
        if (typeScoreMapFromRequest != null) {
            for (Topic topic : enforcedTopics) {
                String internalTopicKey = mapTopicType(topic.getType());
                Integer scoreFromConfig = typeScoreMapFromRequest.get(internalTopicKey);
                topic.setScore(scoreFromConfig != null ? scoreFromConfig : (topic.getScore() != null ? topic.getScore() : 0));
            }
        } else {
            log.warn("Request's global typeScoreMap is null. Scores from DB or default 0 will be used.");
            for (Topic topic : enforcedTopics) {
                if (topic.getScore() == null) topic.setScore(0);
            }
        }

        double paperQualityScore = qualityScorer.scorePaper(enforcedTopics);

        Paper paperToSave = new Paper();
        paperToSave.setTitle(request.getTitle());

        int totalScoreCalculated = enforcedTopics.stream()
                .mapToInt(t -> t.getScore() != null ? t.getScore() : 0)
                .sum();
        paperToSave.setTotalScore(totalScoreCalculated);

        String topicIdsContent = enforcedTopics.stream()
                .map(t -> String.valueOf(t.getId()))
                .collect(Collectors.joining(","));
        paperToSave.setContent(topicIdsContent);

        Map<String, Object> configMap = new HashMap<>();
        configMap.put("difficultyDistribution", request.getDifficultyCriteria());
        configMap.put("globalTopicTypeCounts", request.getTopicTypeCounts());
        configMap.put("globalTypeScoreMap", request.getTypeScoreMap());
        configMap.put("knowledgePointConfigs", request.getKnowledgePointConfigs());

        String configJson;
        try {
            ObjectMapper mapper = new ObjectMapper();
            configJson = mapper.writeValueAsString(configMap);
        } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
            log.error("Error serializing paper config to JSON for title '{}': {}", request.getTitle(), configMap, e);
            configJson = "{\"error\": \"failed to serialize config\"}";
        }
        paperToSave.setConfig(configJson);
        paperToSave.setDifficulty(paperQualityScore);
        paperToSave.setCreateTime(LocalDateTime.now());
        paperToSave.setUpdateTime(LocalDateTime.now());
        paperToSave.setIsDeleted(false);

        Paper savedPaper = paperRepository.save(paperToSave);
        log.info("Saved new paper with ID: {}. Title: {}", savedPaper.getId(), savedPaper.getTitle());

        response.setSuccess(true);
        response.setId(savedPaper.getId());
        response.setTitle(savedPaper.getTitle());
        response.setSelectedTopics(enforcedTopics);
        response.setPaperDifficulty(paperQualityScore);

        if (warningMsg.length() > 0) {
            response.setWarningMessage(warningMsg.toString());
        }

        Map<String, Integer> chineseKeyTypeScoreMap = new HashMap<>();
        if (request.getTypeScoreMap() != null) {
            for (Map.Entry<String, Integer> entry : request.getTypeScoreMap().entrySet()) {
                String standardInternalKey = mapTopicType(entry.getKey());
                chineseKeyTypeScoreMap.put(getChineseTopicTypeName(standardInternalKey), entry.getValue());
            }
        }
        response.setTypeScoreMap(chineseKeyTypeScoreMap);

        Map<String, Integer> chineseKeyTypeCountMap = new HashMap<>();
        if (request.getTopicTypeCounts() != null) {
            for (Map.Entry<String, Integer> entry : request.getTopicTypeCounts().entrySet()) {
                String standardInternalKey = mapTopicType(entry.getKey());
                chineseKeyTypeCountMap.put(getChineseTopicTypeName(standardInternalKey), entry.getValue());
            }
        }
        response.setTypeCountMap(chineseKeyTypeCountMap);

        response.setTotalScore(totalScoreCalculated);
        response.setActualTotalScore(totalScoreCalculated);
        response.setDifficultyDistribution(request.getDifficultyCriteria());

        if (!enforcedTopics.isEmpty()) {
            try {
                updateTopicStatsWithNewTransaction(enforcedTopics);
                // 同时更新知识点级别的使用统计
                knowledgePointUsageTracker.updateKnowledgePointUsageStats(enforcedTopics);
            } catch (Exception e) {
                log.error("Failed to update topic usage statistics after paper generation for paper ID: {}", savedPaper.getId(), e);
            }
        }

        return response;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTopicStatsWithNewTransaction(List<Topic> topics) {
        updateTopicUsageStats(topics);
    }

    @Override
    public void updateTopicUsageStats(List<Topic> topics) {
        if (topics == null || topics.isEmpty()) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        for (Topic topic : topics) {
            if (topic == null || topic.getId() == null) {
                log.warn("Skipping stats update for null topic or topic with null ID.");
                continue;
            }
            TopicEnhancementData data = enhancementDataMapper.selectByTopicId(topic.getId());
            boolean isNew = false;
            if (data == null) {
                isNew = true;
                data = new TopicEnhancementData();
                data.setTopicId(topic.getId());
                data.setUsageCount(1);
            } else {
                data.setUsageCount(data.getUsageCount() == null ? 1 : data.getUsageCount() + 1);
            }
            data.setLastUsedTime(now);

            if (isNew) {
                // Optional: If TopicEnhancementData has a createTime field and it should be set for new records
                // data.setCreateTime(now);
                try {
                    log.debug("Attempting to insert new TopicEnhancementData for Topic ID: {}, usageCount=1, lastUsedTime={}", data.getTopicId(), data.getLastUsedTime());
                    enhancementDataMapper.insert(data);
                } catch (org.springframework.dao.DuplicateKeyException e) {
                    log.warn("DuplicateKeyException on insert for Topic ID: {}. Assuming concurrent creation. Will attempt to fetch and update.", data.getTopicId(), e);
                    // The record was likely inserted by another thread between our selectByTopicId and insert.
                    // Re-fetch the data to get the record inserted by the other thread.
                    TopicEnhancementData existingDataAfterConflict = enhancementDataMapper.selectByTopicId(topic.getId());
                    if (existingDataAfterConflict != null) {
                        // Update counts and times on the fetched record
                        existingDataAfterConflict.setUsageCount(existingDataAfterConflict.getUsageCount() == null ? 1 : existingDataAfterConflict.getUsageCount() + 1);
                        existingDataAfterConflict.setLastUsedTime(now); // Update last used time
                        // Optional: If TopicEnhancementData has an updateTime field
                        // existingDataAfterConflict.setUpdateTime(now);
                        enhancementDataMapper.updateById(existingDataAfterConflict);
                        log.debug("Successfully updated concurrently created TopicEnhancementData for Topic ID: {}", existingDataAfterConflict.getTopicId());
                    } else {
                        // This state is problematic: insert failed due to duplicate, but then we couldn't find the record.
                        log.error("Failed to insert (DuplicateKey) and then failed to find TopicEnhancementData for Topic ID: {}. Stats may be inconsistent.", topic.getId());
                    }
                }
            } else { // Not new, data was found by selectByTopicId.
                     // Its usageCount (incremented) and lastUsedTime were already set by lines 356 and 358.
                // Optional: If TopicEnhancementData has an updateTime field
                // data.setUpdateTime(now);
                log.debug("Updating existing TopicEnhancementData for Topic ID: {} to usageCount={}, lastUsedTime={}", data.getTopicId(), data.getUsageCount(), data.getLastUsedTime());
                enhancementDataMapper.updateById(data);
            }
        }
        log.info("Updated usage statistics for up to {} topics.", topics.size());
    }

    @Override
    @Transactional
    public Paper savePaper(Paper paper) {
        if (paper.getId() == null) {
            paper.setCreateTime(LocalDateTime.now());
        }
        paper.setUpdateTime(LocalDateTime.now());
        return paperRepository.save(paper);
    }

    @Override
    public List<Paper> getAllPapers() {
        return paperRepository.findAllByIsDeletedFalseOrderByCreateTimeDesc();
    }

    @Override
    public PaginationResponse<Paper> getPapersWithPagination(String search, Integer type,
                                                             String sortField, boolean isAsc,
                                                             Integer page, Integer size) {
        log.info("Fetching papers with pagination: search='{}', type={}, sortField='{}', isAsc={}, page={}, size={}",
                search, type, sortField, isAsc, page, size);

        if (page == null || page < 0) page = 0;
        if (size == null || size <= 0) size = 10;

        Page<Paper> pageParam = new Page<>(page + 1, size);
        log.debug("MyBatis-Plus Page parameters: current={}, size={}", pageParam.getCurrent(), pageParam.getSize());

        LambdaQueryWrapper<Paper> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(Paper::getIsDeleted, false);
        log.debug("Added isDeleted=false condition to query");

        if (StringUtils.isNotBlank(search)) {
            queryWrapper.like(Paper::getTitle, search);
            log.debug("Added title LIKE '%{}%' condition to query", search);
        }

        if (type != null) {
            queryWrapper.eq(Paper::getType, type);
            log.debug("Added type={} condition to query", type);
        }

        if (StringUtils.isNotBlank(sortField)) {
            if (sortField.equals("create_time")) {
                if (isAsc) {
                    queryWrapper.orderByAsc(Paper::getCreateTime);
                    log.debug("Added ORDER BY create_time ASC");
                } else {
                    queryWrapper.orderByDesc(Paper::getCreateTime);
                    log.debug("Added ORDER BY create_time DESC");
                }
            } else if (sortField.equals("total_score")) {
                if (isAsc) {
                    queryWrapper.orderByAsc(Paper::getTotalScore);
                    log.debug("Added ORDER BY total_score ASC");
                } else {
                    queryWrapper.orderByDesc(Paper::getTotalScore);
                    log.debug("Added ORDER BY total_score DESC");
                }
            } else if (sortField.equals("title")) {
                if (isAsc) {
                    queryWrapper.orderByAsc(Paper::getTitle);
                    log.debug("Added ORDER BY title ASC");
                } else {
                    queryWrapper.orderByDesc(Paper::getTitle);
                    log.debug("Added ORDER BY title DESC");
                }
            } else {
                queryWrapper.orderByDesc(Paper::getCreateTime);
                log.debug("Added default ORDER BY create_time DESC (unknown sort field: {})", sortField);
            }
        } else {
            queryWrapper.orderByDesc(Paper::getCreateTime);
            log.debug("Added default ORDER BY create_time DESC (no sort field provided)");
        }

        try {
            log.debug("Executing paginated query with wrapper: {}", queryWrapper.getCustomSqlSegment());
            IPage<Paper> pageResult = paperRepository.selectPage(pageParam, queryWrapper);

            log.info("Query results: total={}, pages={}, current={}, size={}, records={}",
                    pageResult.getTotal(), pageResult.getPages(), pageResult.getCurrent(),
                    pageResult.getSize(), pageResult.getRecords().size());

            return PaginationResponse.fromPage(pageResult);
        } catch (Exception e) {
            log.error("Error executing paginated paper query: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public PaperDetailDTO getPaperDetail(Long id) {
        Paper paper = paperRepository.findByIdAndIsDeletedFalse(id).orElse(null);
        if (paper == null) {
            log.warn("Paper with id {} not found or is deleted.", id);
            return null;
        }


        List<Topic> topics = getTopicsForPaper(paper);

        PaperDetailDTO dto = new PaperDetailDTO();
        dto.setId(paper.getId());
        dto.setTitle(paper.getTitle());
        dto.setTotalScore(paper.getTotalScore());
        dto.setDifficulty(paper.getDifficulty());
        dto.setCreateTime(paper.getCreateTime());
        dto.setTopics(topics);

        // Set difficulty distribution from paper entity if available
        if (paper.getDifficultyDistribution() != null && !paper.getDifficultyDistribution().isEmpty()) {
            try {
                // Convert JSON string to Map
                ObjectMapper objectMapper = new ObjectMapper();
                List<Map<String, Double>> listDistributionRaw = objectMapper.readValue(
                    paper.getDifficultyDistribution(),
                    new TypeReference<List<Map<String, Double>>>() {}
                );

                if (listDistributionRaw != null && !listDistributionRaw.isEmpty()) {
                    Map<String, Double> rawMap = listDistributionRaw.get(0);
                    Map<String, Double> difficultyDistribution = new HashMap<>();
                    // Convert keys "1", "2", "3" to "easy", "medium", "hard"
                    if (rawMap.containsKey("1")) difficultyDistribution.put("easy", rawMap.get("1"));
                    if (rawMap.containsKey("2")) difficultyDistribution.put("medium", rawMap.get("2"));
                    if (rawMap.containsKey("3")) difficultyDistribution.put("hard", rawMap.get("3"));
                    
                    // Preserve any other keys if they exist, though not expected by current format description
                    rawMap.forEach((key, value) -> {
                        if (!key.equals("1") && !key.equals("2") && !key.equals("3")) {
                            difficultyDistribution.put(key, value);
                        }
                    });

                    dto.setDifficultyDistribution(difficultyDistribution);
                    log.info("Loaded and mapped difficulty distribution from paper: {}", difficultyDistribution);
                } else {
                    log.warn("Parsed difficulty distribution list is null or empty for paper id: {}. JSON string: {}", id, paper.getDifficultyDistribution());
                }
            } catch (Exception e) {
                log.error("Error parsing difficulty distribution JSON: {}", e.getMessage());
            }
        } else if (paper.getDifficulty() != null) {
            // Calculate difficulty distribution from overall difficulty if not available
            Map<String, Double> calculatedDistribution = calculateDifficultyDistribution(paper.getDifficulty(), topics);
            dto.setDifficultyDistribution(calculatedDistribution);
            log.info("Calculated difficulty distribution from overall difficulty: {}", calculatedDistribution);
        }

        if (!topics.isEmpty()) {
            Map<String, List<Topic>> rawTopicsByType = topics.stream()
                    .collect(Collectors.groupingBy(topic -> getChineseTopicTypeName(mapTopicType(topic.getType()))));

            Map<String, List<Topic>> orderedTopicsByType = new LinkedHashMap<>();

            String[] standardOrder = {"单选题", "多选题", "判断题", "填空题", "简答题", "主观题", "组合题"};
            for (String typeName : standardOrder) {
                if (rawTopicsByType.containsKey(typeName)) {
                    orderedTopicsByType.put(typeName, rawTopicsByType.get(typeName));
                }
            }

            for (Map.Entry<String, List<Topic>> entry : rawTopicsByType.entrySet()) {
                if (!orderedTopicsByType.containsKey(entry.getKey())) {
                    orderedTopicsByType.put(entry.getKey(), entry.getValue());
                }
            }

            dto.setTopicsByType(orderedTopicsByType);
        } else {
            dto.setTopicsByType(new HashMap<>());
        }
        return dto;
    }

    @Override
    @Transactional
    public boolean deletePaper(Long id) {
        return paperRepository.findByIdAndIsDeletedFalse(id).map(paper -> {
            paper.setIsDeleted(true);
            paperRepository.save(paper);
            log.info("Soft deleted paper with id: {}", id);
            return true;
        }).orElseGet(() -> {
            log.warn("Paper with id {} not found for deletion.", id);
            return false;
        });
    }

    private List<Topic> getTopicsForPaper(Paper paper) {
        String content = paper.getContent();
        if (content == null || content.isEmpty()) {
            log.warn("Paper id: {} has no content to parse for topics.", paper.getId());
            return new ArrayList<>();
        }

        try {
            List<Integer> topicIds = Arrays.stream(content.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            if (topicIds.isEmpty()) {
                log.warn("Paper id: {} has no valid topic IDs in content: '{}'", paper.getId(), content);
                return new ArrayList<>();
            }

            log.info("Loading {} topics for paper id: {}", topicIds.size(), paper.getId());
            List<Topic> topics = topicMapper.selectBatchIds(topicIds);

            if (topics == null || topics.isEmpty()) {
                log.warn("No topics found in database for paper id: {} with topic IDs: {}", paper.getId(), topicIds);
                return new ArrayList<>();
            }

            log.info("Found {} topics in database (from {} requested IDs) for paper id: {}",
                    topics.size(), topicIds.size(), paper.getId());

            Map<Integer, Topic> idTopicMap = topics.stream()
                    .collect(Collectors.toMap(Topic::getId, t -> t));
            List<Topic> ordered = new ArrayList<>();
            for (Integer tid : topicIds) {
                Topic t = idTopicMap.get(tid);
                if (t != null) {
                    if ((t.getType().equals("choice") || t.getType().equals("multiple") ||
                            t.getType().equals("singleChoice") || t.getType().equals("multipleChoice")) &&
                            t.getOptions() != null && !t.getOptions().isEmpty()) {
                        try {
                            ObjectMapper mapper = new ObjectMapper();
                            mapper.readTree(t.getOptions());
                        } catch (Exception e) {
                            log.warn("Topic ID {} has invalid JSON options format: {}", t.getId(), e.getMessage());
                        }
                    }
                    ordered.add(t);
                } else {
                    log.warn("Topic ID {} specified in paper but not found in database", tid);
                }
            }

            log.info("Returning {} ordered topics for paper id: {}", ordered.size(), paper.getId());
            return ordered;
        } catch (Exception e) {
            log.error("Error processing topics for paper id: {}: {}", paper.getId(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    // 第二部分代码请继续查看后续内容
    @Override
    public Resource generatePaperResource(Long id, String paperType) {
        Paper paper = paperRepository.findByIdAndIsDeletedFalse(id).orElse(null);
        if (paper == null) {
            log.error("Paper not found for PDF generation: id={}", id);
            return null;
        }

        // 如果没有指定类型，则使用试卷默认类型
        if (paperType == null || paperType.isEmpty()) {
            paperType = paper.getPaperType() != null ? paper.getPaperType() : "regular";
        }

        log.info("开始生成PDF - 试卷ID: {}, 类型: {}, 配置: {}", id, paperType, paper.getConfig());

        try {
            com.itextpdf.text.Document document = new com.itextpdf.text.Document(PageSize.A4);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, baos);

            // 设置PDF文档属性
            document.addTitle(paper.getTitle() != null ? paper.getTitle() : "试卷");
            document.addAuthor("麦子教育系统");
            document.addCreator("麦子教育试卷生成系统");
            document.addSubject(paper.getTitle() != null ? paper.getTitle() : "试卷");
            document.addKeywords("试卷,麦子教育,考试");

            // 设置PDF版本和压缩
            writer.setPdfVersion(PdfWriter.PDF_VERSION_1_7);
            writer.setCompressionLevel(9); // 最高压缩级别

            document.open();

            Font titleFont;
            Font textFont;
            Font optionFont;
            Font scoreFont;
            Font answerFont;

            try {
                BaseFont bfSimHei = loadFontFromResource("fonts/simhei.ttf", "SimHei");
                BaseFont bfBody = bfSimHei;

                titleFont = new Font(bfSimHei, 16, Font.BOLD);
                textFont = new Font(bfBody, 10.5f, Font.NORMAL);
                optionFont = new Font(bfBody, 10, Font.NORMAL);
                scoreFont = new Font(bfSimHei, 9, Font.NORMAL);
                answerFont = new Font(bfSimHei, 10, Font.BOLD, BaseColor.RED);

                log.info("试卷字体加载完成，已优化字体排版");
            } catch (DocumentException | IOException e) {
                log.error("加载嵌入式SimHei字体（SimHei/SimSun）时出错。PDF可能无法正确显示中文或生成失败。错误: {}", e.getMessage(), e);
                log.warn("将回退使用iText默认字体（如Helvetica）。这会导致中文字符显示为乱码或方框。请确保 'src/main/resources/fonts/' 目录下的字体文件 (simhei.ttf, simsun.ttf) 存在且已包含在项目中。");
                titleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
                textFont = FontFactory.getFont(FontFactory.HELVETICA, 10);
                optionFont = FontFactory.getFont(FontFactory.HELVETICA, 10);
                scoreFont = FontFactory.getFont(FontFactory.HELVETICA, 9);
                answerFont = FontFactory.getFont(FontFactory.HELVETICA, 10, Font.BOLD, BaseColor.RED);
            }

            // 添加文档标题
            Paragraph paperTitle = new Paragraph(new Phrase(paper.getTitle() != null ? paper.getTitle() : "无标题", titleFont));
            paperTitle.setAlignment(Element.ALIGN_CENTER);
            paperTitle.setSpacingAfter(10f);
            document.add(paperTitle);

            // 添加试卷基本信息
            String paperInfoStr = String.format("总分: %s | 难度: %s | %s试卷",
                    paper.getTotalScore() != null ? paper.getTotalScore().toString() : "N/A",
                    paper.getDifficulty() != null ? String.format("%.2f", paper.getDifficulty()) : "N/A",
                    getPaperTypeDisplayName(paperType));
            Paragraph paperInfo = new Paragraph(paperInfoStr, textFont);
            paperInfo.setAlignment(Element.ALIGN_CENTER);
            paperInfo.setSpacingAfter(10f);
            document.add(paperInfo);

            // 添加姓名栏
            if ("regular".equals(paperType) || "standard".equals(paperType)) {
                Paragraph nameField = new Paragraph("姓名：________________     班级：________________", textFont);
                nameField.setAlignment(Element.ALIGN_RIGHT);
                nameField.setSpacingAfter(15f);
                document.add(nameField);
            }

            Map<String, Integer> typeScoresFromConfig = parseTypeScoreMapFromConfig(paper.getConfig());
            log.info("从配置解析的分值映射: {}", typeScoresFromConfig);

            List<Topic> topics = getTopicsForPaper(paper);
            if (!topics.isEmpty()) {
                Map<String, List<Topic>> rawTopicsByType = topics.stream()
                        .collect(Collectors.groupingBy(t -> getChineseTopicTypeName(mapTopicType(t.getType()))));

                // 如果配置中没有分值信息，使用默认分值或从试卷总分计算
                if (typeScoresFromConfig.isEmpty() && paper.getTotalScore() != null && paper.getTotalScore() > 0) {
                    typeScoresFromConfig = calculateDefaultTypeScores(rawTopicsByType, paper.getTotalScore());
                    log.info("使用计算的默认分值映射: {}", typeScoresFromConfig);
                }

                // 按规定顺序添加题目，动态生成序号
                String[] baseOrder = {"单选题", "多选题", "判断题", "填空题", "简答题", "主观题", "组合题"};
                String[] chineseNumbers = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};

                int currentQuestionNumber = 1;
                int typeIndex = 0; // 用于动态生成题型序号

                //  修复教师版本逻辑：教师版本应该显示题目内容（只显示答案和解析）
                boolean showQuestions = "regular".equals(paperType) || "standard".equals(paperType) || "teacher".equals(paperType);
                boolean showAnswers = "teacher".equals(paperType) || "standard".equals(paperType);

                // 按标准顺序添加题目，动态生成序号
                for (String baseTypeName : baseOrder) {
                    List<Topic> typeTopics = rawTopicsByType.get(baseTypeName);
                    if (typeTopics != null && !typeTopics.isEmpty()) {
                        if (showQuestions) {
                            // 动态生成带序号的题型名称
                            String numberedTypeName = chineseNumbers[typeIndex] + "、" + baseTypeName;
                            typeIndex++; // 只有当题型存在时才增加序号

                            currentQuestionNumber = addTopicsToDocumentByType(document, numberedTypeName, typeTopics,
                                    titleFont, textFont, optionFont, scoreFont,
                                    typeScoresFromConfig, currentQuestionNumber, mapper, paperType);
                        }
                    }
                }

                //  修复：只有标准版本才需要额外的答案部分，教师版本已经在题目中显示了答案
                if (showAnswers && "standard".equals(paperType)) {
                    document.newPage();
                    Paragraph answerTitle = new Paragraph("参考答案与解析", titleFont);
                    answerTitle.setAlignment(Element.ALIGN_CENTER);
                    answerTitle.setSpacingAfter(15);
                    document.add(answerTitle);

                    int answerNumber = 1;
                    int answerTypeIndex = 0; // 用于答案部分的题型序号
                    for (String baseTypeName : baseOrder) {
                        List<Topic> typeTopics = rawTopicsByType.get(baseTypeName);
                        if (typeTopics != null && !typeTopics.isEmpty()) {
                            // 动态生成带序号的题型名称
                            String numberedTypeName = chineseNumbers[answerTypeIndex] + "、" + baseTypeName;
                            answerTypeIndex++; // 只有当题型存在时才增加序号

                            // 添加题型标题
                            Paragraph typeTitle = new Paragraph(numberedTypeName + " 答案", textFont);
                            typeTitle.setSpacingBefore(10);
                            typeTitle.setSpacingAfter(5);
                            document.add(typeTitle);

                            // 添加每道题的答案
                            for (Topic topic : typeTopics) {
                                Paragraph answerParagraph = new Paragraph();
                                answerParagraph.add(new Chunk(answerNumber + ". ", textFont));
                                answerParagraph.add(new Chunk("答案: ", textFont));

                                if (topic.getAnswer() != null && !topic.getAnswer().isEmpty()) {
                                    answerParagraph.add(new Chunk(topic.getAnswer(), answerFont));
                                } else {
                                    answerParagraph.add(new Chunk("（无答案）", textFont));
                                }

                                document.add(answerParagraph);

                                if (topic.getAnalysis() != null && !topic.getAnalysis().isEmpty()) {
                                    Paragraph analysisParagraph = new Paragraph();
                                    analysisParagraph.setIndentationLeft(20);
                                    analysisParagraph.add(new Chunk("解析: ", textFont));
                                    analysisParagraph.add(new Chunk(topic.getAnalysis(), textFont));
                                    document.add(analysisParagraph);
                                }

                                answerNumber++;
                            }
                        }
                    }
                }
            } else {
                document.add(new Paragraph("\n(该试卷没有题目内容)", textFont));
            }

            document.close();
            log.info("Successfully generated PDF for paper id: {}, type: {}", id, paperType);
            return new ByteArrayResource(baos.toByteArray());
        } catch (Exception e) {
            log.error("Error generating PDF for paper id: {}, type: {}: {}", id, paperType, e.getMessage(), e);
            try {
                java.io.ByteArrayOutputStream errorBaos = new java.io.ByteArrayOutputStream();
                com.itextpdf.text.Document errorDoc = new com.itextpdf.text.Document();
                com.itextpdf.text.pdf.PdfWriter.getInstance(errorDoc, errorBaos);
                errorDoc.open();
                errorDoc.add(new com.itextpdf.text.Paragraph("Error generating PDF: " + e.getMessage()));
                errorDoc.close();
                return new ByteArrayResource(errorBaos.toByteArray());
            } catch (com.itextpdf.text.DocumentException dex) {
                log.error("Even error document generation failed: {}", dex.getMessage(), dex);
                return null;
            }
        }
    }

    /**
     * 根据试卷类型代码获取显示名称
     */
    private String getPaperTypeDisplayName(String paperType) {
        if (paperType == null) return "普通";

        switch (paperType) {
            case "regular": return "学生";
            case "teacher": return "教师";
            case "standard": return "标准";
            default: return paperType;
        }
    }

    private int addTopicsToDocumentByType(com.itextpdf.text.Document document, String typeName, List<Topic> topics,
                                          Font titleFont, Font textFont, Font optionFont, Font scoreFont,
                                          Map<String, Integer> typeScores, int startQuestionNumber, ObjectMapper mapper, String paperType) throws com.itextpdf.text.DocumentException {
        String internalType = mapToInternalType(typeName);
        Integer scorePerQuestion = typeScores.getOrDefault(internalType, 0);
        Integer totalScore = scorePerQuestion * topics.size();

        Paragraph typeParagraph = new Paragraph();
        typeParagraph.add(new Chunk(typeName, titleFont));
        typeParagraph.add(new Chunk(" (共" + topics.size() + "题，共" + totalScore + "分)", scoreFont));
        typeParagraph.setSpacingBefore(10f);
        typeParagraph.setSpacingAfter(5f);
        document.add(typeParagraph);

        int questionNumber = startQuestionNumber;
        for (Topic topic : topics) {
            questionNumber = addTopicToDocument(document, topic, textFont, optionFont, scoreFont,
                    scorePerQuestion, questionNumber, mapper, paperType);
        }

        return questionNumber;
    }

    private String mapToInternalType(String chineseTypeName) {
        if (chineseTypeName == null) return "";

        // 移除序号前缀（如"一、"、"二、"等）
        String cleanTypeName = chineseTypeName.replaceAll("^[一二三四五六七八九十]、", "");

        // 使用统一的TopicTypeMapper进行映射，然后转换为前端格式（PDF生成需要前端格式）
        String dbType = TopicTypeMapper.toDbFormat(cleanTypeName);
        return TopicTypeMapper.toFrontendFormat(dbType);
    }

    // private String getChineseTypeName(String typeName) {
    //     switch (typeName) {
    //         case "singleChoice": return "一、单选题";
    //         case "multipleChoice": return "二、多选题";
    //         case "judgment": return "三、判断题";
    //         case "fillBlank": return "四、填空题";
    //         case "shortAnswer": return "五、简答题";
    //         case "groupQuestion": return "六、组合题";
    //         default: return typeName;
    //     }
    // }

    private int addTopicToDocument(com.itextpdf.text.Document document, Topic topic, Font textFont, Font optionFont, Font scoreFont,
                                   Integer scorePerQuestion, int qNum, ObjectMapper mapper, String paperType) throws com.itextpdf.text.DocumentException {

        // 如果是教师版，只显示答案和解析
        if ("teacher".equals(paperType)) {
            return addTeacherAnswerToDocument(document, topic, textFont, scoreFont, qNum);
        }
        String topicInternalType = mapTopicType(topic.getType());

        PdfPTable questionTable = new PdfPTable(1);
        questionTable.setWidthPercentage(100);
        questionTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

        Phrase titlePhrase = new Phrase();
        titlePhrase.add(new Chunk(qNum + ". ", textFont));
        titlePhrase.add(createContentPhrase(topic.getTitle() != null ? topic.getTitle() : "(无题目标题)", textFont, 10f, TeXConstants.STYLE_TEXT));

        // 移除单个题目的分数显示，只在题型级别显示总分
        // if (scorePerQuestion != null && scorePerQuestion > 0) {
        //     titlePhrase.add(new Chunk(" [" + scorePerQuestion + "分]", scoreFont));
        // }

        // 如果是教师版，添加答案信息到题目标题后
        if ("teacher".equals(paperType) && topic.getAnswer() != null && !topic.getAnswer().isEmpty()) {
            titlePhrase.add(new Chunk(" 【答案: " + topic.getAnswer() + "】", scoreFont));
        }

        PdfPCell titleCell = new PdfPCell(titlePhrase);
        titleCell.setBorder(PdfPCell.NO_BORDER);
        titleCell.setPaddingBottom(5f);
        questionTable.addCell(titleCell);

        log.info("处理题目 {} - 原始类型: {}, 内部类型: {}, 选项数据: {}",
                topic.getId(), topic.getType(), topicInternalType, topic.getOptions());

        if (("SINGLE_CHOICE".equals(topicInternalType) || "MULTIPLE_CHOICE".equals(topicInternalType)) &&
                topic.getOptions() != null && !topic.getOptions().isEmpty()) {
            log.info("开始解析题目 {} 的选项，选项数据: '{}'", topic.getId(), topic.getOptions());
            try {
                Object options = null;
                try {
                    options = mapper.readValue(topic.getOptions(), new TypeReference<List<Map<String, String>>>() {});
                    log.info("成功解析为List格式，选项数量: {}", ((List<?>) options).size());
                } catch (Exception e) {
                    log.info("List格式解析失败，尝试Map格式: {}", e.getMessage());
                    try {
                        options = mapper.readValue(topic.getOptions(), new TypeReference<Map<String, String>>() {});
                        log.info("成功解析为Map格式，选项数量: {}", ((Map<?, ?>) options).size());
                    } catch (Exception e2) {
                        log.warn("Failed to parse options for topic {}: {}", topic.getId(), e2.getMessage());
                        throw e2;
                    }
                }

                List<Map.Entry<String, String>> optionEntries = new ArrayList<>();

                if (options instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, String>> optionsList = (List<Map<String, String>>) options;
                    log.info("处理List格式选项，共 {} 个选项", optionsList.size());
                    for (int i = 0; i < optionsList.size(); i++) {
                        Map<String, String> optionMap = optionsList.get(i);
                        log.info("选项 {}: {}", i, optionMap);
                        String key = optionMap.getOrDefault("key", "");
                        String name = optionMap.getOrDefault("name", "");
                        if (!key.isEmpty() && !name.isEmpty()) {
                            optionEntries.add(new AbstractMap.SimpleEntry<>(key, name));
                            log.info("添加选项: {} -> {}", key, name);
                        } else {
                            log.warn("选项 {} 缺少key或name: key='{}', name='{}'", i, key, name);
                        }
                    }
                } else if (options instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> optionsMap = (Map<String, String>) options;
                    log.info("处理Map格式选项，共 {} 个选项", optionsMap.size());
                    for (Map.Entry<String, String> entry : optionsMap.entrySet()) {
                        log.info("选项: {} -> {}", entry.getKey(), entry.getValue());
                        if (entry.getKey() != null && entry.getValue() != null &&
                                !entry.getKey().isEmpty() && !entry.getValue().isEmpty()) {
                            optionEntries.add(entry);
                            log.info("添加选项: {} -> {}", entry.getKey(), entry.getValue());
                        } else {
                            log.warn("跳过无效选项: key='{}', value='{}'", entry.getKey(), entry.getValue());
                        }
                    }
                }

                // 如果没有解析到选项，尝试其他可能的格式
                if (optionEntries.isEmpty()) {
                    log.warn("未能解析到选项，尝试其他格式。原始选项数据: {}", topic.getOptions());
                    // 尝试直接解析为字符串数组或其他格式
                    try {
                        // 尝试解析为简单的字符串数组
                        String[] simpleOptions = mapper.readValue(topic.getOptions(), String[].class);
                        log.info("成功解析为字符串数组，选项数量: {}", simpleOptions.length);
                        for (int i = 0; i < simpleOptions.length; i++) {
                            String optionKey = String.valueOf((char)('A' + i)); // A, B, C, D...
                            optionEntries.add(new AbstractMap.SimpleEntry<>(optionKey, simpleOptions[i]));
                        }
                    } catch (Exception e3) {
                        log.warn("字符串数组格式解析也失败: {}", e3.getMessage());
                    }
                }

                // 确保选项不为空
                log.info("题目 {} 最终解析得到 {} 个选项", topic.getId(), optionEntries.size());
                if (optionEntries.isEmpty()) {
                    log.warn("No valid options found for topic {}, options JSON: {}", topic.getId(), topic.getOptions());
                    Paragraph noOptionsMsg = new Paragraph("   (该题目暂无选项)", optionFont);
                    noOptionsMsg.setIndentationLeft(15f);
                    PdfPCell noOptionsCell = new PdfPCell(noOptionsMsg);
                    noOptionsCell.setBorder(PdfPCell.NO_BORDER);
                    questionTable.addCell(noOptionsCell);
                } else {
                    log.info("题目 {} 开始添加 {} 个选项到PDF", topic.getId(), optionEntries.size());

                    int optionCount = optionEntries.size();
                    double averageLength = optionEntries.stream()
                            .mapToInt(e -> e.getValue().length())
                            .average().orElse(0);

                    boolean useOneLine = optionCount <= 5 && averageLength < 8;
                    boolean useTwoColumns = !useOneLine && optionCount % 2 == 0 && averageLength < 15;

                    if (useOneLine) {
                        PdfPTable oneLineTable = new PdfPTable(optionCount);
                        oneLineTable.setWidthPercentage(95);

                        for (Map.Entry<String, String> entry : optionEntries) {
                            Phrase optionPhrase = new Phrase();
                            optionPhrase.add(new Chunk(entry.getKey() + ". ", optionFont));
                            optionPhrase.add(createContentPhrase(entry.getValue(), optionFont, 10f, TeXConstants.STYLE_TEXT));

                            PdfPCell optionCell = new PdfPCell(optionPhrase);
                            optionCell.setBorder(PdfPCell.NO_BORDER);
                            optionCell.setPaddingBottom(2f);
                            oneLineTable.addCell(optionCell);
                        }

                        PdfPCell containingCell = new PdfPCell(oneLineTable);
                        containingCell.setBorder(PdfPCell.NO_BORDER);
                        containingCell.setPaddingLeft(10f);
                        questionTable.addCell(containingCell);

                    } else if (useTwoColumns) {
                        PdfPTable twoColTable = new PdfPTable(2);
                        twoColTable.setWidthPercentage(95);

                        for (Map.Entry<String, String> entry : optionEntries) {
                            Phrase optionPhrase = new Phrase();
                            optionPhrase.add(new Chunk(entry.getKey() + ". ", optionFont));
                            optionPhrase.add(createContentPhrase(entry.getValue(), optionFont, 10f, TeXConstants.STYLE_TEXT));

                            PdfPCell optionCell = new PdfPCell(optionPhrase);
                            optionCell.setBorder(PdfPCell.NO_BORDER);
                            optionCell.setPaddingBottom(2f);
                            twoColTable.addCell(optionCell);
                        }

                        PdfPCell containingCell = new PdfPCell(twoColTable);
                        containingCell.setBorder(PdfPCell.NO_BORDER);
                        containingCell.setPaddingLeft(10f);
                        questionTable.addCell(containingCell);

                    } else {
                        PdfPTable optionsTable = new PdfPTable(1);
                        optionsTable.setWidthPercentage(95);
                        optionsTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

                        for (Map.Entry<String, String> entry : optionEntries) {
                            Phrase optionLine = new Phrase();
                            optionLine.add(new Chunk("   " + entry.getKey() + ". ", optionFont));
                            optionLine.add(createContentPhrase(entry.getValue(), optionFont, 10f, TeXConstants.STYLE_TEXT));

                            PdfPCell optionCell = new PdfPCell(optionLine);
                            optionCell.setBorder(PdfPCell.NO_BORDER);
                            optionCell.setPaddingBottom(2f);
                            optionsTable.addCell(optionCell);
                        }

                        PdfPCell containingOptionsCell = new PdfPCell(optionsTable);
                        containingOptionsCell.setBorder(PdfPCell.NO_BORDER);
                        containingOptionsCell.setPaddingLeft(10f);
                        questionTable.addCell(containingOptionsCell);
                    }
                } // 关闭 else 块

            } catch (Exception e) {
                log.warn("Failed to parse options JSON for topic {}: {}. Options: '{}'", topic.getId(), e.getMessage(), topic.getOptions());
                Paragraph errorOpt = new Paragraph("   (选项解析失败或格式错误)", optionFont);
                errorOpt.setIndentationLeft(15f);
                PdfPCell errorCell = new PdfPCell(errorOpt);
                errorCell.setBorder(PdfPCell.NO_BORDER);
                questionTable.addCell(errorCell);
            }
        }
        else if ("JUDGE".equals(topicInternalType)) {
//            Paragraph answerLine = new Paragraph("答案: (　　　)", optionFont);
//            answerLine.setIndentationLeft(15f);
//            PdfPCell answerCell = new PdfPCell(answerLine);
//            answerCell.setBorder(PdfPCell.NO_BORDER);
//            answerCell.setPaddingTop(2f);
//            questionTable.addCell(answerCell);
        } else if ("FILL".equals(topicInternalType)) {
            Paragraph answerLine = new Paragraph("答案: __________________________", optionFont);
            answerLine.setIndentationLeft(15f);
            PdfPCell fillBlankAnswerCell = new PdfPCell(answerLine);
            fillBlankAnswerCell.setBorder(PdfPCell.NO_BORDER);
            fillBlankAnswerCell.setPaddingTop(2f);
            questionTable.addCell(fillBlankAnswerCell);
        } else if ("SHORT".equals(topicInternalType)) {
            Paragraph answerLine = new Paragraph("答案:", optionFont);
            answerLine.setIndentationLeft(15f);
            PdfPCell shortAnswerCell = new PdfPCell(answerLine);
            shortAnswerCell.setBorder(PdfPCell.NO_BORDER);
            shortAnswerCell.setPaddingTop(2f);
            questionTable.addCell(shortAnswerCell);

            PdfPCell blankCell = new PdfPCell();
            blankCell.setBorder(PdfPCell.NO_BORDER);
            blankCell.setFixedHeight(80f);
            questionTable.addCell(blankCell);
        }

        if ("GROUP".equals(topicInternalType) && topic.getSubs() != null && !topic.getSubs().isEmpty()) {
            try {
                List<Topic> subTopics = mapper.readValue(topic.getSubs(), new TypeReference<List<Topic>>() {});
                if (!subTopics.isEmpty()) {
                    PdfPTable subsTable = new PdfPTable(1);
                    subsTable.setWidthPercentage(95);
                    subsTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
                    int subNum = 1;
                    for (Topic subTopic : subTopics) {
                        PdfPCell subHeaderCell = new PdfPCell(new Phrase("子题 " + subNum++, textFont));
                        subHeaderCell.setBorder(PdfPCell.NO_BORDER);
                        subHeaderCell.setPaddingTop(10f);
                        subHeaderCell.setPaddingBottom(5f);
                        subsTable.addCell(subHeaderCell);

                        Phrase subTitlePhrase = new Phrase();
                        Phrase contentPhrase = createContentPhrase(subTopic.getTitle() != null ? subTopic.getTitle() : "(无题目标题)", textFont, 10f, TeXConstants.STYLE_TEXT);
                        subTitlePhrase.add(contentPhrase);
                        PdfPCell subTitleCell = new PdfPCell(subTitlePhrase);
                        subTitleCell.setBorder(PdfPCell.NO_BORDER);
                        subTitleCell.setPaddingLeft(15f);
                        subTitleCell.setPaddingBottom(5f);
                        subsTable.addCell(subTitleCell);

                        String internalSubTopicType = mapTopicType(subTopic.getType());
                        if (("SINGLE_CHOICE".equals(internalSubTopicType) || "MULTIPLE_CHOICE".equals(internalSubTopicType)) &&
                                subTopic.getOptions() != null && !subTopic.getOptions().isEmpty()) {
                            try {
                                // 处理子题选项的代码
                            } catch (Exception e) {
                                log.warn("Failed to parse options for sub-topic {}: {}", subTopic.getId(), e.getMessage());
                                Paragraph errorSubOpt = new Paragraph("   (子题选项解析失败)", optionFont);
                                errorSubOpt.setIndentationLeft(15f);
                                PdfPCell errorSubCell = new PdfPCell(errorSubOpt);
                                errorSubCell.setBorder(PdfPCell.NO_BORDER);
                                subsTable.addCell(errorSubCell);
                            }
                        }
                    }
                    PdfPCell subsContainerCell = new PdfPCell(subsTable);
                    subsContainerCell.setBorder(PdfPCell.NO_BORDER);
                    subsContainerCell.setPaddingLeft(10f);
                    questionTable.addCell(subsContainerCell);
                }
            } catch (Exception e) {
                log.warn("Failed to parse subs for group topic id {}: {}", topic.getId(), e.getMessage());
                Paragraph errorSubs = new Paragraph("   (组合题解析失败)", optionFont);
                errorSubs.setIndentationLeft(15f);
                PdfPCell errorCell = new PdfPCell(errorSubs);
                errorCell.setBorder(PdfPCell.NO_BORDER);
                questionTable.addCell(errorCell);
            }
        }

        // 如果是标准版，在题目后添加答案和解析
        PdfPTable answerAnalysisTable = null;
        if ("standard".equals(paperType)) {
            answerAnalysisTable = new PdfPTable(1);
            answerAnalysisTable.setWidthPercentage(100);
            answerAnalysisTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
            answerAnalysisTable.setSpacingAfter(5f);

            // 添加答案
            if (topic.getAnswer() != null && !topic.getAnswer().isEmpty()) {
                Phrase answerPhrase = new Phrase();
                answerPhrase.add(new Chunk("【答案】", scoreFont));
                answerPhrase.add(new Chunk(topic.getAnswer(), textFont));

                PdfPCell answerCell = new PdfPCell(answerPhrase);
                answerCell.setBorder(PdfPCell.NO_BORDER);
                answerCell.setPaddingTop(5f);
                answerCell.setPaddingLeft(15f);
                answerCell.setBackgroundColor(new BaseColor(240, 248, 255)); // 浅蓝色背景
                answerAnalysisTable.addCell(answerCell);
            }

            // 添加解析
            if (topic.getAnalysis() != null && !topic.getAnalysis().isEmpty()) {
                Phrase analysisPhrase = new Phrase();
                analysisPhrase.add(new Chunk("【解析】", scoreFont));
                analysisPhrase.add(createContentPhrase(topic.getAnalysis(), textFont, 10f, TeXConstants.STYLE_TEXT));

                PdfPCell analysisCell = new PdfPCell(analysisPhrase);
                analysisCell.setBorder(PdfPCell.NO_BORDER);
                analysisCell.setPaddingTop(5f);
                analysisCell.setPaddingLeft(15f);
                analysisCell.setBackgroundColor(new BaseColor(245, 245, 245)); // 浅灰色背景
                answerAnalysisTable.addCell(analysisCell);
            }
            // 注意顺序：先添加题目内容，再添加答案和解析
        }
        document.add(questionTable);
        if ("standard".equals(paperType) && answerAnalysisTable != null) {
            document.add(answerAnalysisTable);
        }
        return qNum + 1;
    }

    @NonNull
    private Phrase createContentPhrase(String content, Font font, float size, int style) {
        if (content == null || content.isEmpty()) {
            return new Phrase("", font);
        }

        Phrase phrase = new Phrase();

        // 检查是否包含LaTeX数学公式
        if (content.contains("$") || content.contains("\\(") || content.contains("\\[")) {
            try {
                // 尝试渲染LaTeX公式
                String processedContent = renderLatexFormulas(content);
                phrase.add(new Chunk(processedContent, font));
                return phrase;
            } catch (Exception e) {
                log.warn("Failed to render LaTeX formulas in content: {}, error: {}", content, e.getMessage());
                // 如果LaTeX渲染失败，使用原始内容
                phrase.add(new Chunk(content, font));
                return phrase;
            }
        }

        phrase.add(new Chunk(content, font));
        return phrase;
    }

    /**
     * 获取增强的LaTeX符号映射
     * 包含完整的数学符号支持，基于标准LaTeX符号表
     */
    private Map<String, String> getEnhancedLatexSymbols() {
        Map<String, String> symbols = new HashMap<>();

        // 1. 希腊字母 (Greek and Hebrew letters)
        symbols.put("\\alpha", "α");
        symbols.put("\\beta", "β");
        symbols.put("\\gamma", "γ");
        symbols.put("\\delta", "δ");
        symbols.put("\\epsilon", "ε");
        symbols.put("\\varepsilon", "ε");
        symbols.put("\\zeta", "ζ");
        symbols.put("\\eta", "η");
        symbols.put("\\theta", "θ");
        symbols.put("\\vartheta", "ϑ");
        symbols.put("\\iota", "ι");
        symbols.put("\\kappa", "κ");
        symbols.put("\\varkappa", "κ");
        symbols.put("\\lambda", "λ");
        symbols.put("\\mu", "μ");
        symbols.put("\\nu", "ν");
        symbols.put("\\xi", "ξ");
        symbols.put("\\pi", "π");
        symbols.put("\\varpi", "ϖ");
        symbols.put("\\rho", "ρ");
        symbols.put("\\varrho", "ϱ");
        symbols.put("\\sigma", "σ");
        symbols.put("\\varsigma", "ς");
        symbols.put("\\tau", "τ");
        symbols.put("\\upsilon", "υ");
        symbols.put("\\phi", "φ");
        symbols.put("\\varphi", "φ");
        symbols.put("\\chi", "χ");
        symbols.put("\\psi", "ψ");
        symbols.put("\\omega", "ω");

        // 大写希腊字母
        symbols.put("\\Gamma", "Γ");
        symbols.put("\\Delta", "Δ");
        symbols.put("\\Theta", "Θ");
        symbols.put("\\Lambda", "Λ");
        symbols.put("\\Xi", "Ξ");
        symbols.put("\\Pi", "Π");
        symbols.put("\\Sigma", "Σ");
        symbols.put("\\Upsilon", "Υ");
        symbols.put("\\Phi", "Φ");
        symbols.put("\\Psi", "Ψ");
        symbols.put("\\Omega", "Ω");

        // 希伯来字母
        symbols.put("\\aleph", "ℵ");
        symbols.put("\\beth", "ℶ");
        symbols.put("\\gimel", "ℷ");
        symbols.put("\\daleth", "ℸ");

        // 2. 二元运算符 (Binary Operation/Relation Symbols)
        symbols.put("\\ast", "∗");
        symbols.put("\\star", "⋆");
        symbols.put("\\cdot", "·");
        symbols.put("\\circ", "∘");
        symbols.put("\\bullet", "•");
        symbols.put("\\bigcirc", "○");
        symbols.put("\\diamond", "⋄");
        symbols.put("\\times", "×");
        symbols.put("\\div", "÷");
        symbols.put("\\centerdot", "·");
        symbols.put("\\pm", "±");
        symbols.put("\\mp", "∓");
        symbols.put("\\cap", "∩");
        symbols.put("\\cup", "∪");
        symbols.put("\\uplus", "⊎");
        symbols.put("\\sqcap", "⊓");
        symbols.put("\\sqcup", "⊔");
        symbols.put("\\wedge", "∧");
        symbols.put("\\vee", "∨");
        symbols.put("\\dagger", "†");
        symbols.put("\\ddagger", "‡");
        symbols.put("\\triangleleft", "◁");
        symbols.put("\\triangleright", "▷");
        symbols.put("\\setminus", "∖");
        symbols.put("\\wr", "≀");
        symbols.put("\\oplus", "⊕");
        symbols.put("\\ominus", "⊖");
        symbols.put("\\otimes", "⊗");
        symbols.put("\\oslash", "⊘");
        symbols.put("\\odot", "⊙");

        // 3. 关系符号 (Relation Symbols)
        symbols.put("\\equiv", "≡");
        symbols.put("\\leq", "≤");
        symbols.put("\\geq", "≥");
        symbols.put("\\perp", "⊥");
        symbols.put("\\cong", "≅");
        symbols.put("\\prec", "≺");
        symbols.put("\\succ", "≻");
        symbols.put("\\preceq", "⪯");
        symbols.put("\\succeq", "⪰");
        symbols.put("\\ll", "≪");
        symbols.put("\\gg", "≫");
        symbols.put("\\subset", "⊂");
        symbols.put("\\supset", "⊃");
        symbols.put("\\subseteq", "⊆");
        symbols.put("\\supseteq", "⊇");
        symbols.put("\\sqsubset", "⊏");
        symbols.put("\\sqsupset", "⊐");
        symbols.put("\\sqsubseteq", "⊑");
        symbols.put("\\sqsupseteq", "⊒");
        symbols.put("\\dashv", "⊣");
        symbols.put("\\vdash", "⊢");
        symbols.put("\\in", "∈");
        symbols.put("\\ni", "∋");
        symbols.put("\\notin", "∉");
        symbols.put("\\mid", "∣");
        symbols.put("\\parallel", "∥");
        symbols.put("\\bowtie", "⋈");
        symbols.put("\\Join", "⋈");
        symbols.put("\\ltimes", "⋉");
        symbols.put("\\rtimes", "⋊");
        symbols.put("\\smile", "⌣");
        symbols.put("\\frown", "⌢");
        symbols.put("\\neq", "≠");
        symbols.put("\\sim", "∼");
        symbols.put("\\simeq", "≃");
        symbols.put("\\approx", "≈");
        symbols.put("\\asymp", "≍");
        symbols.put("\\doteq", "≐");
        symbols.put("\\propto", "∝");
        symbols.put("\\models", "⊨");

        // 4. 箭头符号 (Arrow symbols)
        symbols.put("\\leftarrow", "←");
        symbols.put("\\longleftarrow", "⟵");
        symbols.put("\\uparrow", "↑");
        symbols.put("\\Leftarrow", "⇐");
        symbols.put("\\Longleftarrow", "⟸");
        symbols.put("\\Uparrow", "⇑");
        symbols.put("\\rightarrow", "→");
        symbols.put("\\longrightarrow", "⟶");
        symbols.put("\\downarrow", "↓");
        symbols.put("\\Rightarrow", "⇒");
        symbols.put("\\Longrightarrow", "⟹");
        symbols.put("\\Downarrow", "⇓");
        symbols.put("\\leftrightarrow", "↔");
        symbols.put("\\longleftrightarrow", "⟷");
        symbols.put("\\updownarrow", "↕");
        symbols.put("\\Leftrightarrow", "⇔");
        symbols.put("\\Longleftrightarrow", "⟺");
        symbols.put("\\Updownarrow", "⇕");
        symbols.put("\\mapsto", "↦");
        symbols.put("\\longmapsto", "⟼");
        symbols.put("\\nearrow", "↗");
        symbols.put("\\hookleftarrow", "↩");
        symbols.put("\\hookrightarrow", "↪");
        symbols.put("\\searrow", "↘");
        symbols.put("\\leftharpoonup", "↼");
        symbols.put("\\rightharpoonup", "⇀");
        symbols.put("\\swarrow", "↙");
        symbols.put("\\leftharpoondown", "↽");
        symbols.put("\\rightharpoondown", "⇁");
        symbols.put("\\nwarrow", "↖");
        symbols.put("\\rightleftharpoons", "⇌");
        symbols.put("\\leadsto", "↝");

        // 5. 大型运算符 (Variable-sized symbols)
        symbols.put("\\sum", "∑");
        symbols.put("\\prod", "∏");
        symbols.put("\\coprod", "∐");
        symbols.put("\\int", "∫");
        symbols.put("\\oint", "∮");
        symbols.put("\\iint", "∬");
        symbols.put("\\iiint", "∭");
        symbols.put("\\iiiint", "⨌");
        symbols.put("\\bigcap", "⋂");
        symbols.put("\\bigcup", "⋃");
        symbols.put("\\biguplus", "⊎");
        symbols.put("\\bigoplus", "⊕");
        symbols.put("\\bigotimes", "⊗");
        symbols.put("\\bigodot", "⊙");
        symbols.put("\\bigvee", "⋁");
        symbols.put("\\bigwedge", "⋀");
        symbols.put("\\bigsqcup", "⊔");

        // 6. 标准函数名 (Standard Function Names)
        symbols.put("\\arccos", "arccos");
        symbols.put("\\arcsin", "arcsin");
        symbols.put("\\arctan", "arctan");
        symbols.put("\\arg", "arg");
        symbols.put("\\cos", "cos");
        symbols.put("\\cosh", "cosh");
        symbols.put("\\cot", "cot");
        symbols.put("\\coth", "coth");
        symbols.put("\\csc", "csc");
        symbols.put("\\deg", "deg");
        symbols.put("\\det", "det");
        symbols.put("\\dim", "dim");
        symbols.put("\\exp", "exp");
        symbols.put("\\gcd", "gcd");
        symbols.put("\\hom", "hom");
        symbols.put("\\inf", "inf");
        symbols.put("\\ker", "ker");
        symbols.put("\\lg", "lg");
        symbols.put("\\lim", "lim");
        symbols.put("\\liminf", "lim inf");
        symbols.put("\\limsup", "lim sup");
        symbols.put("\\ln", "ln");
        symbols.put("\\log", "log");
        symbols.put("\\max", "max");
        symbols.put("\\min", "min");
        symbols.put("\\Pr", "Pr");
        symbols.put("\\sec", "sec");
        symbols.put("\\sin", "sin");
        symbols.put("\\sinh", "sinh");
        symbols.put("\\sup", "sup");
        symbols.put("\\tan", "tan");
        symbols.put("\\tanh", "tanh");

        // 7. 分隔符 (Delimiters)
        symbols.put("\\lfloor", "⌊");
        symbols.put("\\rfloor", "⌋");
        symbols.put("\\lceil", "⌈");
        symbols.put("\\rceil", "⌉");
        symbols.put("\\langle", "⟨");
        symbols.put("\\rangle", "⟩");
        symbols.put("\\llcorner", "⌞");
        symbols.put("\\lrcorner", "⌟");
        symbols.put("\\ulcorner", "⌜");
        symbols.put("\\urcorner", "⌝");

        // 8. 杂项符号 (Miscellaneous symbols)
        symbols.put("\\infty", "∞");
        symbols.put("\\forall", "∀");
        symbols.put("\\exists", "∃");
        symbols.put("\\nexists", "∄");
        symbols.put("\\emptyset", "∅");
        symbols.put("\\varnothing", "∅");
        symbols.put("\\imath", "ı");
        symbols.put("\\jmath", "ȷ");
        symbols.put("\\ell", "ℓ");
        symbols.put("\\wp", "℘");
        symbols.put("\\Re", "ℜ");
        symbols.put("\\Im", "ℑ");
        symbols.put("\\mho", "℧");
        symbols.put("\\prime", "′");
        symbols.put("\\backprime", "‵");
        symbols.put("\\surd", "√");
        symbols.put("\\triangle", "△");
        symbols.put("\\square", "□");
        symbols.put("\\blacksquare", "■");
        symbols.put("\\triangledown", "▽");
        symbols.put("\\blacktriangle", "▲");
        symbols.put("\\blacktriangledown", "▼");
        symbols.put("\\lozenge", "◊");
        symbols.put("\\blacklozenge", "⧫");
        symbols.put("\\bigstar", "★");
        symbols.put("\\angle", "∠");
        symbols.put("\\measuredangle", "∡");
        symbols.put("\\sphericalangle", "∢");
        symbols.put("\\complement", "∁");
        symbols.put("\\partial", "∂");
        symbols.put("\\eth", "ð");
        symbols.put("\\hbar", "ℏ");
        symbols.put("\\nabla", "∇");
        symbols.put("\\Box", "□");
        symbols.put("\\Diamond", "◊");
        symbols.put("\\clubsuit", "♣");
        symbols.put("\\diamondsuit", "♦");
        symbols.put("\\heartsuit", "♥");
        symbols.put("\\spadesuit", "♠");
        symbols.put("\\sharp", "♯");
        symbols.put("\\flat", "♭");
        symbols.put("\\natural", "♮");
        symbols.put("\\circledS", "Ⓢ");

        // 9. 特殊字符和转义符号
        symbols.put("\\{", "{");
        symbols.put("\\}", "}");
        symbols.put("\\%", "%");
        symbols.put("\\$", "$");
        symbols.put("\\&", "&");
        symbols.put("\\#", "#");
        symbols.put("\\_", "_");
        symbols.put("\\backslash", "\\");

        // 10. 扩展的数学符号和变体
        // 无穷大的变体
        symbols.put("-\\infty", "-∞");
        symbols.put("+\\infty", "+∞");
        symbols.put("\\pm\\infty", "±∞");
        symbols.put("\\mp\\infty", "∓∞");

        // 更多希腊字母变体
        symbols.put("\\digamma", "ϝ");
        symbols.put("\\varkappa", "ϰ");
        symbols.put("\\varpi", "ϖ");
        symbols.put("\\varrho", "ϱ");
        symbols.put("\\varsigma", "ς");
        symbols.put("\\vartheta", "ϑ");
        symbols.put("\\varepsilon", "ε");
        symbols.put("\\varphi", "φ");

        // 更多二元运算符
        symbols.put("\\amalg", "⨿");
        symbols.put("\\barwedge", "⊼");
        symbols.put("\\veebar", "⊻");
        symbols.put("\\curlywedge", "⋏");
        symbols.put("\\curlyvee", "⋎");
        symbols.put("\\Cap", "⋒");
        symbols.put("\\Cup", "⋓");
        symbols.put("\\rightthreetimes", "⋌");
        symbols.put("\\leftthreetimes", "⋋");
        symbols.put("\\divideontimes", "⋇");
        symbols.put("\\dotplus", "∔");
        symbols.put("\\intercal", "⊺");
        symbols.put("\\doublebarwedge", "⩞");

        // 更多关系符号
        symbols.put("\\approxeq", "≊");
        symbols.put("\\thicksim", "∼");
        symbols.put("\\backsim", "∽");
        symbols.put("\\backsimeq", "⋍");
        symbols.put("\\triangleq", "≜");
        symbols.put("\\circeq", "≗");
        symbols.put("\\bumpeq", "≏");
        symbols.put("\\Bumpeq", "≎");
        symbols.put("\\doteqdot", "≑");
        symbols.put("\\thickapprox", "≈");
        symbols.put("\\fallingdotseq", "≒");
        symbols.put("\\risingdotseq", "≓");
        symbols.put("\\varpropto", "∝");
        symbols.put("\\therefore", "∴");
        symbols.put("\\because", "∵");
        symbols.put("\\eqcirc", "≖");
        symbols.put("\\neq", "≠");
        symbols.put("\\ncong", "≇");
        symbols.put("\\nmid", "∤");
        symbols.put("\\nparallel", "∦");
        symbols.put("\\nleq", "≰");
        symbols.put("\\ngeq", "≱");
        symbols.put("\\nless", "≮");
        symbols.put("\\ngtr", "≯");
        symbols.put("\\nprec", "⊀");
        symbols.put("\\nsucc", "⊁");
        symbols.put("\\npreceq", "⋠");
        symbols.put("\\nsucceq", "⋡");
        symbols.put("\\precnapprox", "⪹");
        symbols.put("\\succnapprox", "⪺");
        symbols.put("\\precnsim", "⋨");
        symbols.put("\\succnsim", "⋩");
        symbols.put("\\lnapprox", "⪉");
        symbols.put("\\gnapprox", "⪊");
        symbols.put("\\lneq", "⪇");
        symbols.put("\\gneq", "⪈");
        symbols.put("\\lneqq", "≨");
        symbols.put("\\gneqq", "≩");
        symbols.put("\\lnsim", "⋦");
        symbols.put("\\gnsim", "⋧");
        symbols.put("\\lvertneqq", "≨");
        symbols.put("\\gvertneqq", "≩");
        symbols.put("\\nsubseteq", "⊈");
        symbols.put("\\nsupseteq", "⊉");
        symbols.put("\\subsetneq", "⊊");
        symbols.put("\\supsetneq", "⊋");
        symbols.put("\\varsubsetneq", "⊊");
        symbols.put("\\varsupsetneq", "⊋");
        symbols.put("\\subsetneqq", "⫋");
        symbols.put("\\supsetneqq", "⫌");
        symbols.put("\\varsubsetneqq", "⫋");
        symbols.put("\\varsupsetneqq", "⫌");

        // 更多箭头符号
        symbols.put("\\dashrightarrow", "⇢");
        symbols.put("\\dashleftarrow", "⇠");
        symbols.put("\\leftleftarrows", "⇇");
        symbols.put("\\leftrightarrows", "⇆");
        symbols.put("\\Lleftarrow", "⇚");
        symbols.put("\\twoheadleftarrow", "↞");
        symbols.put("\\leftrightharpoons", "⇋");
        symbols.put("\\leftarrowtail", "↢");
        symbols.put("\\looparrowleft", "↫");
        symbols.put("\\curvearrowleft", "↶");
        symbols.put("\\circlearrowleft", "↺");
        symbols.put("\\Lsh", "↰");
        symbols.put("\\upuparrows", "⇈");
        symbols.put("\\upharpoonleft", "↿");
        symbols.put("\\downharpoonleft", "⇃");
        symbols.put("\\multimap", "⊸");
        symbols.put("\\leftrightsquigarrow", "↭");
        symbols.put("\\rightrightarrows", "⇉");
        symbols.put("\\rightleftarrows", "⇄");
        symbols.put("\\twoheadrightarrow", "↠");
        symbols.put("\\rightarrowtail", "↣");
        symbols.put("\\looparrowright", "↬");
        symbols.put("\\curvearrowright", "↷");
        symbols.put("\\circlearrowright", "↻");
        symbols.put("\\Rsh", "↱");
        symbols.put("\\downdownarrows", "⇊");
        symbols.put("\\upharpoonright", "↾");
        symbols.put("\\downharpoonright", "⇂");
        symbols.put("\\rightsquigarrow", "↝");
        symbols.put("\\nleftarrow", "↚");
        symbols.put("\\nrightarrow", "↛");
        symbols.put("\\nLeftarrow", "⇍");
        symbols.put("\\nRightarrow", "⇏");
        symbols.put("\\nleftrightarrow", "↮");
        symbols.put("\\nLeftrightarrow", "⇎");

        // 更多杂项符号
        symbols.put("\\Bbbk", "𝕜");
        symbols.put("\\bigstar", "★");
        symbols.put("\\diagdown", "╲");
        symbols.put("\\diagup", "╱");
        symbols.put("\\Diamond", "◊");
        symbols.put("\\Finv", "Ⅎ");
        symbols.put("\\Game", "⅁");
        symbols.put("\\vartriangle", "△");
        symbols.put("\\blacklozenge", "⧫");
        symbols.put("\\blacksquare", "■");
        symbols.put("\\blacktriangle", "▲");
        symbols.put("\\blacktriangledown", "▼");
        symbols.put("\\lozenge", "◊");
        symbols.put("\\square", "□");
        symbols.put("\\triangledown", "▽");
        symbols.put("\\triangle", "△");
        symbols.put("\\vartriangle", "△");
        symbols.put("\\blacklozenge", "⧫");
        symbols.put("\\hslash", "ℏ");
        symbols.put("\\mho", "℧");
        symbols.put("\\prime", "′");
        symbols.put("\\backprime", "‵");
        symbols.put("\\circledS", "Ⓢ");

        // 数学字体符号
        symbols.put("\\mathcal{A}", "𝒜");
        symbols.put("\\mathcal{B}", "ℬ");
        symbols.put("\\mathcal{C}", "𝒞");
        symbols.put("\\mathcal{D}", "𝒟");
        symbols.put("\\mathcal{E}", "ℰ");
        symbols.put("\\mathcal{F}", "ℱ");
        symbols.put("\\mathcal{G}", "𝒢");
        symbols.put("\\mathcal{H}", "ℋ");
        symbols.put("\\mathcal{I}", "ℐ");
        symbols.put("\\mathcal{J}", "𝒥");
        symbols.put("\\mathcal{K}", "𝒦");
        symbols.put("\\mathcal{L}", "ℒ");
        symbols.put("\\mathcal{M}", "ℳ");
        symbols.put("\\mathcal{N}", "𝒩");
        symbols.put("\\mathcal{O}", "𝒪");
        symbols.put("\\mathcal{P}", "𝒫");
        symbols.put("\\mathcal{Q}", "𝒬");
        symbols.put("\\mathcal{R}", "ℛ");
        symbols.put("\\mathcal{S}", "𝒮");
        symbols.put("\\mathcal{T}", "𝒯");
        symbols.put("\\mathcal{U}", "𝒰");
        symbols.put("\\mathcal{V}", "𝒱");
        symbols.put("\\mathcal{W}", "𝒲");
        symbols.put("\\mathcal{X}", "𝒳");
        symbols.put("\\mathcal{Y}", "𝒴");
        symbols.put("\\mathcal{Z}", "𝒵");

        // 黑板粗体符号
        symbols.put("\\mathbb{A}", "𝔸");
        symbols.put("\\mathbb{B}", "𝔹");
        symbols.put("\\mathbb{C}", "ℂ");
        symbols.put("\\mathbb{D}", "𝔻");
        symbols.put("\\mathbb{E}", "𝔼");
        symbols.put("\\mathbb{F}", "𝔽");
        symbols.put("\\mathbb{G}", "𝔾");
        symbols.put("\\mathbb{H}", "ℍ");
        symbols.put("\\mathbb{I}", "𝕀");
        symbols.put("\\mathbb{J}", "𝕁");
        symbols.put("\\mathbb{K}", "𝕂");
        symbols.put("\\mathbb{L}", "𝕃");
        symbols.put("\\mathbb{M}", "𝕄");
        symbols.put("\\mathbb{N}", "ℕ");
        symbols.put("\\mathbb{O}", "𝕆");
        symbols.put("\\mathbb{P}", "ℙ");
        symbols.put("\\mathbb{Q}", "ℚ");
        symbols.put("\\mathbb{R}", "ℝ");
        symbols.put("\\mathbb{S}", "𝕊");
        symbols.put("\\mathbb{T}", "𝕋");
        symbols.put("\\mathbb{U}", "𝕌");
        symbols.put("\\mathbb{V}", "𝕍");
        symbols.put("\\mathbb{W}", "𝕎");
        symbols.put("\\mathbb{X}", "𝕏");
        symbols.put("\\mathbb{Y}", "𝕐");
        symbols.put("\\mathbb{Z}", "ℤ");

        // 德文尖角体符号
        symbols.put("\\mathfrak{A}", "𝔄");
        symbols.put("\\mathfrak{B}", "𝔅");
        symbols.put("\\mathfrak{C}", "ℭ");
        symbols.put("\\mathfrak{D}", "𝔇");
        symbols.put("\\mathfrak{E}", "𝔈");
        symbols.put("\\mathfrak{F}", "𝔉");
        symbols.put("\\mathfrak{G}", "𝔊");
        symbols.put("\\mathfrak{H}", "ℌ");
        symbols.put("\\mathfrak{I}", "ℑ");
        symbols.put("\\mathfrak{J}", "𝔍");
        symbols.put("\\mathfrak{K}", "𝔎");
        symbols.put("\\mathfrak{L}", "𝔏");
        symbols.put("\\mathfrak{M}", "𝔐");
        symbols.put("\\mathfrak{N}", "𝔑");
        symbols.put("\\mathfrak{O}", "𝔒");
        symbols.put("\\mathfrak{P}", "𝔓");
        symbols.put("\\mathfrak{Q}", "𝔔");
        symbols.put("\\mathfrak{R}", "ℜ");
        symbols.put("\\mathfrak{S}", "𝔖");
        symbols.put("\\mathfrak{T}", "𝔗");
        symbols.put("\\mathfrak{U}", "𝔘");
        symbols.put("\\mathfrak{V}", "𝔙");
        symbols.put("\\mathfrak{W}", "𝔚");
        symbols.put("\\mathfrak{X}", "𝔛");
        symbols.put("\\mathfrak{Y}", "𝔜");
        symbols.put("\\mathfrak{Z}", "ℨ");

        return symbols;
    }

    /**
     * 渲染LaTeX数学公式
     * 将LaTeX公式转换为可显示的文本格式
     */
    private String renderLatexFormulas(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        // 简单的LaTeX符号替换映射
        Map<String, String> latexSymbols = new HashMap<>();
        latexSymbols.put("\\varnothing", "∅");
        latexSymbols.put("\\emptyset", "∅");
        latexSymbols.put("\\cup", "∪");
        latexSymbols.put("\\cap", "∩");
        latexSymbols.put("\\subset", "⊂");
        latexSymbols.put("\\subseteq", "⊆");
        latexSymbols.put("\\supset", "⊃");
        latexSymbols.put("\\supseteq", "⊇");
        latexSymbols.put("\\in", "∈");
        latexSymbols.put("\\notin", "∉");
        latexSymbols.put("\\infty", "∞");
        latexSymbols.put("\\pm", "±");
        latexSymbols.put("\\mp", "∓");
        latexSymbols.put("\\times", "×");
        latexSymbols.put("\\div", "÷");
        latexSymbols.put("\\leq", "≤");
        latexSymbols.put("\\geq", "≥");
        latexSymbols.put("\\neq", "≠");
        latexSymbols.put("\\approx", "≈");
        latexSymbols.put("\\equiv", "≡");
        latexSymbols.put("\\alpha", "α");
        latexSymbols.put("\\beta", "β");
        latexSymbols.put("\\gamma", "γ");
        latexSymbols.put("\\delta", "δ");
        latexSymbols.put("\\pi", "π");
        latexSymbols.put("\\theta", "θ");
        latexSymbols.put("\\lambda", "λ");
        latexSymbols.put("\\mu", "μ");
        latexSymbols.put("\\sigma", "σ");
        latexSymbols.put("\\phi", "φ");
        latexSymbols.put("\\omega", "ω");
        // 添加花括号符号和保留字符
        latexSymbols.put("\\{", "{");
        latexSymbols.put("\\}", "}");
        latexSymbols.put("\\%", "%");        // 百分号
        latexSymbols.put("\\$", "$");        // 美元符号
        latexSymbols.put("\\&", "&");        // 和号
        latexSymbols.put("\\#", "#");        // 井号
        latexSymbols.put("\\_", "_");        // 下划线

        // 添加比较符号
        latexSymbols.put("\\lt", "<");       // 小于
        latexSymbols.put("\\gt", ">");       // 大于

        // 添加更多数学符号
        latexSymbols.put("\\forall", "∀");
        latexSymbols.put("\\exists", "∃");
        latexSymbols.put("\\neg", "¬");
        latexSymbols.put("\\land", "∧");
        latexSymbols.put("\\lor", "∨");
        latexSymbols.put("\\rightarrow", "→");
        latexSymbols.put("\\leftarrow", "←");
        latexSymbols.put("\\leftrightarrow", "↔");
        latexSymbols.put("\\Rightarrow", "⇒");
        latexSymbols.put("\\Leftarrow", "⇐");
        latexSymbols.put("\\Leftrightarrow", "⇔");
        latexSymbols.put("\\partial", "∂");
        latexSymbols.put("\\nabla", "∇");
        latexSymbols.put("\\sum", "Σ");
        latexSymbols.put("\\prod", "Π");
        latexSymbols.put("\\int", "∫");

        // 添加逻辑电路和数字电路符号
        latexSymbols.put("\\cdot", "·");        // 点乘/逻辑与
        latexSymbols.put("\\oplus", "⊕");      // 异或
        latexSymbols.put("\\odot", "⊙");       // 点积
        latexSymbols.put("\\otimes", "⊗");     // 张量积
        latexSymbols.put("\\ominus", "⊖");     // 减法
        latexSymbols.put("\\oslash", "⊘");     // 除法

        // 添加上划线和下划线处理（取反符号）
        // 注意：这些会在后面的特殊处理中处理

        // 添加更多运算符
        latexSymbols.put("\\ast", "∗");        // 星号
        latexSymbols.put("\\star", "⋆");       // 五角星
        latexSymbols.put("\\bullet", "•");     // 实心圆点
        latexSymbols.put("\\circ", "∘");       // 空心圆
        latexSymbols.put("\\diamond", "⋄");    // 菱形
        latexSymbols.put("\\triangle", "△");   // 三角形
        latexSymbols.put("\\triangledown", "▽"); // 倒三角形

        // 添加更多比较符号
        latexSymbols.put("\\ll", "≪");         // 远小于
        latexSymbols.put("\\gg", "≫");         // 远大于
        latexSymbols.put("\\sim", "∼");        // 相似
        latexSymbols.put("\\simeq", "≃");      // 渐近等于
        latexSymbols.put("\\cong", "≅");       // 全等
        latexSymbols.put("\\propto", "∝");     // 正比于

        // 添加更多箭头
        latexSymbols.put("\\uparrow", "↑");
        latexSymbols.put("\\downarrow", "↓");
        latexSymbols.put("\\updownarrow", "↕");
        latexSymbols.put("\\Uparrow", "⇑");
        latexSymbols.put("\\Downarrow", "⇓");
        latexSymbols.put("\\Updownarrow", "⇕");

        // 添加数学分析符号
        latexSymbols.put("\\lim", "lim");       // 极限
        latexSymbols.put("\\max", "max");       // 最大值
        latexSymbols.put("\\min", "min");       // 最小值
        latexSymbols.put("\\sup", "sup");       // 上确界
        latexSymbols.put("\\inf", "inf");       // 下确界
        latexSymbols.put("\\log", "log");       // 对数
        latexSymbols.put("\\ln", "ln");         // 自然对数
        latexSymbols.put("\\sin", "sin");       // 正弦
        latexSymbols.put("\\cos", "cos");       // 余弦
        latexSymbols.put("\\tan", "tan");       // 正切
        latexSymbols.put("\\sec", "sec");       // 正割
        latexSymbols.put("\\csc", "csc");       // 余割
        latexSymbols.put("\\cot", "cot");       // 余切

        // 添加更多希腊字母
        latexSymbols.put("\\Gamma", "Γ");
        latexSymbols.put("\\Delta", "Δ");
        latexSymbols.put("\\Theta", "Θ");
        latexSymbols.put("\\Lambda", "Λ");
        latexSymbols.put("\\Xi", "Ξ");
        latexSymbols.put("\\Pi", "Π");
        latexSymbols.put("\\Sigma", "Σ");
        latexSymbols.put("\\Phi", "Φ");
        latexSymbols.put("\\Psi", "Ψ");
        latexSymbols.put("\\Omega", "Ω");
        latexSymbols.put("\\xi", "ξ");
        latexSymbols.put("\\zeta", "ζ");
        latexSymbols.put("\\eta", "η");
        latexSymbols.put("\\iota", "ι");
        latexSymbols.put("\\kappa", "κ");
        latexSymbols.put("\\nu", "ν");
        latexSymbols.put("\\rho", "ρ");
        latexSymbols.put("\\tau", "τ");
        latexSymbols.put("\\upsilon", "υ");
        latexSymbols.put("\\chi", "χ");
        latexSymbols.put("\\psi", "ψ");

        // 添加更多数学常数和符号
        latexSymbols.put("\\hbar", "ℏ");        // 约化普朗克常数
        latexSymbols.put("\\ell", "ℓ");         // 脚本l
        latexSymbols.put("\\wp", "℘");          // 魏尔斯特拉斯p
        latexSymbols.put("\\Re", "ℜ");          // 实部
        latexSymbols.put("\\Im", "ℑ");          // 虚部

        // 希伯来字母
        latexSymbols.put("\\aleph", "ℵ");       // 阿列夫
        latexSymbols.put("\\beth", "ℶ");        // 贝特
        latexSymbols.put("\\gimel", "ℷ");       // 吉梅尔
        latexSymbols.put("\\daleth", "ℸ");      // 达列特

        // 几何符号
        latexSymbols.put("\\angle", "∠");       // 角
        latexSymbols.put("\\measuredangle", "∡"); // 测量角
        latexSymbols.put("\\sphericalangle", "∢"); // 球面角
        latexSymbols.put("\\triangle", "△");    // 三角形
        latexSymbols.put("\\square", "□");      // 正方形
        latexSymbols.put("\\diamond", "◊");     // 菱形
        latexSymbols.put("\\bigcirc", "○");     // 大圆

        // 特殊符号
        latexSymbols.put("\\infty", "∞");       // 无穷
        latexSymbols.put("\\partial", "∂");     // 偏微分
        latexSymbols.put("\\nabla", "∇");       // 梯度/倒三角
        latexSymbols.put("\\Box", "□");         // 方框
        latexSymbols.put("\\Diamond", "◊");     // 菱形
        latexSymbols.put("\\clubsuit", "♣");    // 梅花
        latexSymbols.put("\\diamondsuit", "♦"); // 方块
        latexSymbols.put("\\heartsuit", "♥");   // 红心
        latexSymbols.put("\\spadesuit", "♠");   // 黑桃

        // 大型运算符
        latexSymbols.put("\\sum", "Σ");         // 求和
        latexSymbols.put("\\prod", "Π");        // 求积
        latexSymbols.put("\\coprod", "∐");      // 余积
        latexSymbols.put("\\bigcup", "⋃");      // 大并集
        latexSymbols.put("\\bigcap", "⋂");      // 大交集
        latexSymbols.put("\\bigoplus", "⊕");    // 大异或
        latexSymbols.put("\\bigotimes", "⊗");   // 大张量积
        latexSymbols.put("\\bigodot", "⊙");     // 大点积
        latexSymbols.put("\\biguplus", "⊎");    // 大不交并
        latexSymbols.put("\\bigsqcup", "⊔");    // 大方并
        latexSymbols.put("\\bigvee", "⋁");      // 大逻辑或
        latexSymbols.put("\\bigwedge", "⋀");    // 大逻辑与

        // 积分符号
        latexSymbols.put("\\int", "∫");         // 积分
        latexSymbols.put("\\iint", "∬");        // 二重积分
        latexSymbols.put("\\iiint", "∭");       // 三重积分
        latexSymbols.put("\\oint", "∮");        // 环积分
        latexSymbols.put("\\oiint", "∯");       // 面积分
        latexSymbols.put("\\oiiint", "∰");      // 体积分

        // 括号符号
        latexSymbols.put("\\langle", "⟨");      // 左尖括号
        latexSymbols.put("\\rangle", "⟩");      // 右尖括号
        latexSymbols.put("\\lceil", "⌈");       // 左上取整
        latexSymbols.put("\\rceil", "⌉");       // 右上取整
        latexSymbols.put("\\lfloor", "⌊");      // 左下取整
        latexSymbols.put("\\rfloor", "⌋");      // 右下取整

        String processedContent = content;

        // 处理行内公式 $...$
        processedContent = processedContent.replaceAll("\\$([^$]+)\\$", "$1");

        // 处理行内公式 \\(...\\)
        processedContent = processedContent.replaceAll("\\\\\\(([^)]+)\\\\\\)", "$1");

        // 处理行间公式 \\[...\\]
        processedContent = processedContent.replaceAll("\\\\\\[([^\\]]+)\\\\\\]", "$1");

        // 替换LaTeX符号 - 使用增强的符号映射
        Map<String, String> enhancedLatexSymbols = getEnhancedLatexSymbols();
        for (Map.Entry<String, String> entry : enhancedLatexSymbols.entrySet()) {
            processedContent = processedContent.replace(entry.getKey(), entry.getValue());
        }

        log.debug("LaTeX符号替换后的内容: {}", processedContent);

        // 特殊处理上划线（取反符号）- 数字电路中的重要符号
        // 处理 \overline{A} -> A̅ (带上划线的A)
        processedContent = processedContent.replaceAll("\\\\overline\\{([^}]+)\\}", "$1̅");

        // 处理 \bar{A} -> A̅ (另一种上划线表示法)
        processedContent = processedContent.replaceAll("\\\\bar\\{([^}]+)\\}", "$1̅");

        // 处理简单的上划线表示法 \overline A -> A̅
        processedContent = processedContent.replaceAll("\\\\overline\\s+([A-Za-z0-9])", "$1̅");
        processedContent = processedContent.replaceAll("\\\\bar\\s+([A-Za-z0-9])", "$1̅");

        log.debug("上划线处理后的内容: {}", processedContent);

        // 处理上下标（简化处理）
        processedContent = processedContent.replaceAll("\\^\\{([^}]+)\\}", "^($1)");
        processedContent = processedContent.replaceAll("_\\{([^}]+)\\}", "_($1)");
        processedContent = processedContent.replaceAll("\\^([a-zA-Z0-9])", "^$1");
        processedContent = processedContent.replaceAll("_([a-zA-Z0-9])", "_$1");

        // 处理分数（简化处理）
        processedContent = processedContent.replaceAll("\\\\frac\\{([^}]+)\\}\\{([^}]+)\\}", "($1)/($2)");

        // 处理根号（改进处理，支持更复杂的表达式）
        // 处理带指数的根号 \sqrt[n]{x}
        processedContent = processedContent.replaceAll("\\\\sqrt\\[([^\\]]+)\\]\\{([^}]+)\\}", "$1√($2)");
        // 处理普通平方根 \sqrt{x}
        processedContent = processedContent.replaceAll("\\\\sqrt\\{([^}]+)\\}", "√($1)");

        // 修复根号显示问题 - 确保数字根号格式正确
        // 处理类似 4√2 的情况，确保显示为 4√(2)
        processedContent = processedContent.replaceAll("(\\d+)√(\\d+)\\b", "$1√($2)");
        processedContent = processedContent.replaceAll("(\\d+)√([A-Za-z])\\b", "$1√($2)");

        // 处理数学函数（扩展支持更多函数）
        processedContent = processedContent.replaceAll("\\\\(sin|cos|tan|sec|csc|cot|arcsin|arccos|arctan|sinh|cosh|tanh|log|ln|lg|exp|max|min|sup|inf|det|gcd|lcm)\\{([^}]+)\\}", "$1($2)");
        processedContent = processedContent.replaceAll("\\\\(sin|cos|tan|sec|csc|cot|arcsin|arccos|arctan|sinh|cosh|tanh|log|ln|lg|exp|max|min|sup|inf|det|gcd|lcm)\\s*\\(([^)]+)\\)", "$1($2)");

        // 处理极限表达式
        processedContent = processedContent.replaceAll("\\\\lim\\s*_\\{([^}]+)\\}", "lim_($1)");
        processedContent = processedContent.replaceAll("\\\\lim\\s*_([A-Za-z0-9]+)", "lim_$1");
        processedContent = processedContent.replaceAll("\\\\(limsup|liminf)", "$1");

        // 处理二项式系数
        processedContent = processedContent.replaceAll("\\\\binom\\{([^}]+)\\}\\{([^}]+)\\}", "C($1,$2)");
        processedContent = processedContent.replaceAll("\\\\dbinom\\{([^}]+)\\}\\{([^}]+)\\}", "C($1,$2)");
        processedContent = processedContent.replaceAll("\\{([^}]+)\\s+\\\\choose\\s+([^}]+)\\}", "C($1,$2)");

        // 处理带上下标的大型运算符
        processedContent = processedContent.replaceAll("\\\\sum_\\{([^}]+)\\}\\^\\{([^}]+)\\}", "Σ_($1)^($2)");
        processedContent = processedContent.replaceAll("\\\\prod_\\{([^}]+)\\}\\^\\{([^}]+)\\}", "Π_($1)^($2)");
        processedContent = processedContent.replaceAll("\\\\int_\\{([^}]+)\\}\\^\\{([^}]+)\\}", "∫_($1)^($2)");

        // 处理简单的上下标运算符
        processedContent = processedContent.replaceAll("\\\\sum_([A-Za-z0-9]+)\\^([A-Za-z0-9]+)", "Σ_$1^$2");
        processedContent = processedContent.replaceAll("\\\\prod_([A-Za-z0-9]+)\\^([A-Za-z0-9]+)", "Π_$1^$2");
        processedContent = processedContent.replaceAll("\\\\int_([A-Za-z0-9]+)\\^([A-Za-z0-9]+)", "∫_$1^$2");

        // 处理复杂的数学表达式
        // 处理平方表达式 x^2, (expression)^2
        processedContent = processedContent.replaceAll("([A-Za-z0-9]+)\\^2\\b", "$1²");
        processedContent = processedContent.replaceAll("\\(([^)]+)\\)\\^2\\b", "($1)²");
        processedContent = processedContent.replaceAll("([A-Za-z0-9]+)\\^3\\b", "$1³");
        processedContent = processedContent.replaceAll("\\(([^)]+)\\)\\^3\\b", "($1)³");

        // 处理更复杂的上标表达式（如 x^{2n+1}）
        processedContent = processedContent.replaceAll("([A-Za-z0-9]+)\\^\\{([^}]+)\\}", "$1^($2)");
        processedContent = processedContent.replaceAll("\\(([^)]+)\\)\\^\\{([^}]+)\\}", "($1)^($2)");

        // 处理常见的数学常数
        processedContent = processedContent.replaceAll("\\\\pi\\b", "π");
        processedContent = processedContent.replaceAll("\\\\e\\b", "e");

        // 处理绝对值
        processedContent = processedContent.replaceAll("\\\\left\\|([^|]+)\\\\right\\|", "|$1|");
        processedContent = processedContent.replaceAll("\\\\\\|([^|]+)\\\\\\|", "|$1|");

        // 处理分式中的根号（特殊情况）
        // 例如：\frac{4√(2)}{15} 或 \frac{4\sqrt{2}}{15}
        processedContent = processedContent.replaceAll("\\\\frac\\{([^}]*√[^}]*)\\}\\{([^}]+)\\}", "($1)/($2)");

        // 处理数字根号（如 4√2）
        processedContent = processedContent.replaceAll("(\\d+)√\\(([^)]+)\\)", "$1√($2)");
        processedContent = processedContent.replaceAll("(\\d+)√([A-Za-z0-9]+)", "$1√$2");

        // 处理分段函数 \begin{cases} ... \end{cases}
        // 使用更复杂的正则表达式来匹配整个分段函数结构
        Pattern casesPattern = Pattern.compile("\\\\begin\\{cases\\}(.*?)\\\\end\\{cases\\}", Pattern.DOTALL);
        Matcher casesMatcher = casesPattern.matcher(processedContent);

        while (casesMatcher.find()) {
            String casesContent = casesMatcher.group(1);
            // 处理分段函数内部的条件分隔符
            // 将 \\ 替换为换行，将 & 替换为适当的分隔符
            casesContent = casesContent.replaceAll("\\\\\\\\", ", ");
            casesContent = casesContent.replaceAll("&", " ");
            casesContent = casesContent.trim();

            // 替换整个分段函数
            String replacement = "{ " + casesContent + " }";
            processedContent = processedContent.replace(casesMatcher.group(0), replacement);
        }

        // 处理其他常见的LaTeX环境
        // 处理矩阵 \begin{matrix} ... \end{matrix}
        processedContent = processedContent.replaceAll(
                "\\\\begin\\{(matrix|pmatrix|bmatrix|vmatrix|Vmatrix)\\}([^\\\\]+)\\\\end\\{\\1\\}",
                "[$2]"
        );

        // 处理方程组 \begin{align} ... \end{align}
        processedContent = processedContent.replaceAll(
                "\\\\begin\\{(align|equation|eqnarray)\\*?\\}([^\\\\]+)\\\\end\\{\\1\\*?\\}",
                "$2"
        );

        log.debug("复杂数学表达式处理后的内容: {}", processedContent);

        // 注意：不要清理花括号，因为LaTeX中的\{和\}已经被转换为普通的{和}用于表示集合
        // 原来的代码: processedContent = processedContent.replaceAll("\\{([^}]*)\\}", "$1");
        // 这会错误地移除集合表示法中的花括号

        return processedContent;
    }

    /**
     * 测试LaTeX渲染功能
     * 用于验证集合符号等LaTeX表达式是否正确渲染
     */
    public String testLatexRendering(String latexContent) {
        log.info("测试LaTeX渲染 - 输入: {}", latexContent);
        String result = renderLatexFormulas(latexContent);
        log.info("测试LaTeX渲染 - 输出: {}", result);
        return result;
    }

    /**
     * 测试分段函数渲染
     */
    public String testCasesRendering() {
        String testCases = "f(x)=\\begin{cases}x + 2, x≤0\\\\2x, x>0\\end{cases}";
        log.info("测试分段函数渲染 - 输入: {}", testCases);
        String result = renderLatexFormulas(testCases);
        log.info("测试分段函数渲染 - 输出: {}", result);
        return result;
    }

    /**
     * 测试题型序号生成
     */
    public void testTypeNumbering() {
        String[] baseOrder = {"单选题", "多选题", "判断题", "填空题", "简答题", "主观题", "组合题"};
        String[] chineseNumbers = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};

        // 模拟只有部分题型存在的情况
        boolean[] typeExists = {true, false, true, false, true, false, false}; // 单选、判断、简答

        int typeIndex = 0;
        for (int i = 0; i < baseOrder.length; i++) {
            if (typeExists[i]) {
                String numberedTypeName = chineseNumbers[typeIndex] + "、" + baseOrder[i];
                log.info("题型序号测试 - {}", numberedTypeName);
                typeIndex++;
            }
        }
        // 预期输出：一、单选题，二、判断题，三、简答题
    }

    private Map<String, Integer> parseTypeScoreMapFromConfig(String configJson) {
        Map<String, Integer> typeScores = new HashMap<>();
        if (configJson == null || configJson.isEmpty()) return typeScores;
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});

            // 尝试从 globalTypeScoreMap 获取分值映射（新格式）
            Object rawTypeScoreMap = config.get("globalTypeScoreMap");
            if (rawTypeScoreMap == null) {
                // 回退到旧格式
                rawTypeScoreMap = config.get("typeScoreMap");
            }

            if (rawTypeScoreMap instanceof Map) {
                Map<?, ?> rawMap = (Map<?, ?>) rawTypeScoreMap;
                for (Map.Entry<?, ?> entry : rawMap.entrySet()) {
                    if (entry.getKey() instanceof String && entry.getValue() != null) {
                        try {
                            String frontendKey = (String) entry.getKey();
                            Integer score = Integer.parseInt(entry.getValue().toString());

                            // 将前端格式的键名转换为数据库格式
                            String dbKey = mapTopicType(frontendKey);
                            typeScores.put(dbKey, score);

                            log.debug("映射题型分值: {} ({}) -> {} 分", frontendKey, dbKey, score);
                        } catch (NumberFormatException nfe) {
                            log.warn("Skipping invalid score value in paper config's typeScoreMap for key '{}': {}",
                                    entry.getKey(), entry.getValue());
                        }
                    }
                }
            }

            log.info("解析配置得到的分值映射（数据库格式）: {}", typeScores);
        } catch (Exception e) {
            log.warn("Failed to parse paper config for score map during PDF generation: {}", e.getMessage());
        }
        return typeScores;
    }

    /**
     * 计算默认的题型分值映射
     * 当试卷配置中没有分值信息时，根据试卷总分和题目数量计算默认分值
     */
    private Map<String, Integer> calculateDefaultTypeScores(Map<String, List<Topic>> topicsByType, Integer totalScore) {
        Map<String, Integer> defaultScores = new HashMap<>();

        // 计算总题目数
        int totalQuestions = topicsByType.values().stream()
                .mapToInt(List::size)
                .sum();

        if (totalQuestions == 0) {
            return defaultScores;
        }

        // 计算平均分值
        int averageScore = totalScore / totalQuestions;
        if (averageScore < 1) {
            averageScore = 1; // 最少1分
        }

        // 为每种题型设置分值
        for (Map.Entry<String, List<Topic>> entry : topicsByType.entrySet()) {
            String typeName = entry.getKey();
            int questionCount = entry.getValue().size();

            // 根据题型设置不同的分值权重
            int scorePerQuestion = getDefaultScoreForType(typeName, averageScore);

            // 转换为内部格式的键
            String internalType = mapToInternalType(typeName);
            defaultScores.put(internalType, scorePerQuestion);

            log.info("设置默认分值 - 题型: {}, 内部类型: {}, 题目数: {}, 每题分值: {}",
                    typeName, internalType, questionCount, scorePerQuestion);
        }

        return defaultScores;
    }

    /**
     * 根据题型获取默认分值
     */
    private int getDefaultScoreForType(String typeName, int baseScore) {
        switch (typeName) {
            case "单选题":
            case "多选题":
            case "判断题":
                return Math.max(1, baseScore); // 选择题和判断题使用基础分值
            case "填空题":
                return Math.max(2, baseScore + 1); // 填空题稍高
            case "简答题":
            case "主观题":
                return Math.max(5, baseScore * 2); // 主观题分值更高
            case "组合题":
                return Math.max(10, baseScore * 3); // 组合题分值最高
            default:
                return Math.max(1, baseScore);
        }
    }

    @Override
    public Resource generateWordResource(Long id, String paperType) {
        log.info("开始为试卷 ID: {} 生成Word文档，类型: {}", id, paperType);

        Paper paper = paperRepository.findByIdAndIsDeletedFalse(id).orElse(null);
        if (paper == null) {
            log.error("未找到用于Word生成的试卷: id={}", id);
            return null;
        }

        // 如果没有指定类型，则使用试卷默认类型
        if (paperType == null || paperType.isEmpty()) {
            paperType = paper.getPaperType() != null ? paper.getPaperType() : "regular";
        }

        try {
            List<Topic> topics = getTopicsForPaper(paper);
            if (topics.isEmpty()) {
                log.warn("试卷 {} 中没有题目", id);
            }

            XWPFDocument document = new XWPFDocument();

            // 添加试卷标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText(paper.getTitle() != null ? paper.getTitle() : "无标题");
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            titleRun.setFontFamily("SimHei");

            // 添加试卷基本信息
            XWPFParagraph infoParagraph = document.createParagraph();
            infoParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun infoRun = infoParagraph.createRun();
            String paperInfoStr = String.format("总分: %s | 难度: %.2f | %s试卷 | 创建时间: %s",
                    paper.getTotalScore(),
                    paper.getDifficulty(),
                    getPaperTypeDisplayName(paperType),
                    paper.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
            infoRun.setText(paperInfoStr);
            infoRun.setFontSize(10);
            infoRun.setFontFamily("SimHei");

            // 添加姓名栏
            if ("regular".equals(paperType) || "standard".equals(paperType)) {
                XWPFParagraph nameParagraph = document.createParagraph();
                nameParagraph.setAlignment(ParagraphAlignment.RIGHT);
                XWPFRun nameRun = nameParagraph.createRun();
                nameRun.setText("姓名: ______________ 班级: ______________");
                nameRun.setFontSize(10);
                nameRun.setFontFamily("SimHei");
            }

            // 获取题型分值配置
            Map<String, Integer> typeScores = new HashMap<>();
            try {
                if (paper.getConfig() != null && !paper.getConfig().isEmpty()) {
                    typeScores = parseTypeScoreMapFromConfig(paper.getConfig());
                }
            } catch (Exception e) {
                log.warn("解析试卷配置时出错: {}", e.getMessage());
            }

            // 按题型分组
            Map<String, List<Topic>> topicsByType = topics.stream()
                    .collect(Collectors.groupingBy(Topic::getType));

            // 添加题型分值说明
            XWPFParagraph scoreExplanationParagraph = document.createParagraph();
            XWPFRun scoreExplanationRun = scoreExplanationParagraph.createRun();

            StringBuilder scoreExplanation = new StringBuilder();
            for (String type : Arrays.asList("choice", "multiple", "judge", "fill", "short", "group")) {
                if (topicsByType.containsKey(type) && !topicsByType.get(type).isEmpty() && typeScores.containsKey(type)) {
                    String typeName = getChineseTopicTypeName(type);
                    int singleScore = typeScores.get(type);
                    int totalTypeScore = topicsByType.get(type).size() * singleScore;

                    if (scoreExplanation.length() > 0) {
                        scoreExplanation.append("   ");
                    }
                    scoreExplanation.append(typeName).append("（每题").append(singleScore).append("分，共").append(totalTypeScore).append("分）");
                }
            }

            scoreExplanationRun.setText(scoreExplanation.toString());
            scoreExplanationRun.setFontSize(10);
            scoreExplanationRun.setFontFamily("SimHei");

            // 判断是否需要显示题目和答案
            boolean showQuestions = "regular".equals(paperType) || "standard".equals(paperType);
            boolean isTeacherOnly = "teacher".equals(paperType);  // 教师版只显示答案

            int questionNumber = 1;
            ObjectMapper mapper = new ObjectMapper();

            // 按顺序添加题目 - 使用数据库标准格式
            String[] orderedTypes = {"choice", "multiple", "judge", "fill", "short", "group"};

            // 如果是教师版，只显示答案和解析
            if (isTeacherOnly) {
                // 添加答案标题
                XWPFParagraph answerTitleParagraph = document.createParagraph();
                answerTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun answerTitleRun = answerTitleParagraph.createRun();
                answerTitleRun.setText("参考答案与解析");
                answerTitleRun.setBold(true);
                answerTitleRun.setFontSize(16);
                answerTitleRun.setFontFamily("SimHei");

                // 添加各题型的答案
                int answerNumber = 1;
                for (String type : orderedTypes) {
                    List<Topic> topicsOfType = topics.stream()
                            .filter(t -> mapTopicType(t.getType()).equals(type))
                            .collect(Collectors.toList());

                    if (topicsOfType.isEmpty()) {
                        continue;
                    }

                    String typeChineseName = getChineseTopicTypeName(type);

                    XWPFParagraph typeHeadingParagraph = document.createParagraph();
                    typeHeadingParagraph.setSpacingAfter(5);
                    typeHeadingParagraph.setSpacingBefore(10);
                    XWPFRun typeHeadingRun = typeHeadingParagraph.createRun();
                    typeHeadingRun.setText(typeChineseName + " 答案");
                    typeHeadingRun.setBold(true);
                    typeHeadingRun.setFontFamily("SimHei");
                    typeHeadingRun.setFontSize(12);

                    for (Topic topic : topicsOfType) {
                        // 答案段落
                        XWPFParagraph answerParagraph = document.createParagraph();
                        answerParagraph.setIndentationLeft(100);

                        XWPFRun numberRun = answerParagraph.createRun();
                        numberRun.setText(answerNumber++ + ". 答案：");
                        numberRun.setFontFamily("SimHei");
                        numberRun.setFontSize(10);

                        XWPFRun answerRun = answerParagraph.createRun();
                        answerRun.setText(topic.getAnswer() != null ? topic.getAnswer() : "（无答案）");
                        answerRun.setFontFamily("SimHei");
                        answerRun.setFontSize(10);
                        answerRun.setBold(true);
                        answerRun.setColor("FF0000"); // 红色

                        // 解析段落
                        if (topic.getAnalysis() != null && !topic.getAnalysis().isEmpty()) {
                            XWPFParagraph analysisParagraph = document.createParagraph();
                            analysisParagraph.setIndentationLeft(150);

                            XWPFRun analysisLabelRun = analysisParagraph.createRun();
                            analysisLabelRun.setText("解析：");
                            analysisLabelRun.setFontFamily("SimHei");
                            analysisLabelRun.setFontSize(10);

                            XWPFRun analysisContentRun = analysisParagraph.createRun();
                            analysisContentRun.setText(topic.getAnalysis());
                            analysisContentRun.setFontFamily("SimHei");
                            analysisContentRun.setFontSize(10);
                        }

                        // 添加空行
                        document.createParagraph();
                    }
                }
            } else if (showQuestions) {
                for (String type : orderedTypes) {
                    List<Topic> topicsOfType = topics.stream()
                            .filter(t -> mapTopicType(t.getType()).equals(type))
                            .collect(Collectors.toList());

                    if (topicsOfType.isEmpty()) {
                        continue;
                    }

                    String typeChineseName = getChineseTopicTypeName(type);

                    XWPFParagraph typeHeadingParagraph = document.createParagraph();
                    typeHeadingParagraph.setSpacingAfter(5);
                    typeHeadingParagraph.setSpacingBefore(10);
                    XWPFRun typeHeadingRun = typeHeadingParagraph.createRun();
                    typeHeadingRun.setText(typeChineseName + " (共" + topicsOfType.size() + "题)");
                    typeHeadingRun.setBold(true);
                    typeHeadingRun.setFontFamily("SimHei");
                    typeHeadingRun.setFontSize(12);

                    int qNum = questionNumber;
                    String topicInternalType = mapTopicType(type);

                    for (Topic topic : topicsOfType) {
                        XWPFParagraph contentParagraph = document.createParagraph();
                        contentParagraph.setSpacingAfter(5);
                        contentParagraph.setIndentationLeft(100);
                        XWPFRun contentRun = contentParagraph.createRun();
                        contentRun.setText(qNum++ + ". " + topic.getTitle());
                        contentRun.setFontFamily("SimHei");
                        contentRun.setFontSize(10);

                        if (("SINGLE_CHOICE".equals(topicInternalType) || "MULTIPLE_CHOICE".equals(topicInternalType)) &&
                                topic.getOptions() != null && !topic.getOptions().isEmpty()) {
                            try {
                                List<Map<String, String>> options = new ArrayList<>();
                                try {
                                    options = mapper.readValue(topic.getOptions(),
                                            new TypeReference<List<Map<String, String>>>() {});
                                } catch (Exception e) {
                                    log.debug("选项不是列表格式，尝试解析为Map: {}", e.getMessage());
                                    Map<String, String> optionsMap = mapper.readValue(topic.getOptions(),
                                            new TypeReference<Map<String, String>>() {});
                                    for (Map.Entry<String, String> entry : optionsMap.entrySet()) {
                                        Map<String, String> option = new HashMap<>();
                                        option.put("key", entry.getKey());
                                        option.put("name", entry.getValue());
                                        options.add(option);
                                    }
                                }

                                int optionCount = options.size();
                                float averageLength = (float) options.stream()
                                        .mapToInt(m -> m.getOrDefault("name", "").length())
                                        .average().orElse(0);

                                boolean useOneLine = optionCount <= 5 && averageLength < 8;
                                boolean useTwoColumns = !useOneLine && optionCount % 2 == 0 && averageLength < 15;

                                if (useOneLine) {
                                    XWPFParagraph optionsParagraph = document.createParagraph();
                                    optionsParagraph.setIndentationLeft(200);
                                    XWPFRun optionsRun = optionsParagraph.createRun();

                                    StringBuilder optionsText = new StringBuilder();
                                    for (Map<String, String> option : options) {
                                        if (optionsText.length() > 0) {
                                            optionsText.append("    ");
                                        }
                                        optionsText.append(option.getOrDefault("key", "")).append(". ")
                                                .append(option.getOrDefault("name", ""));
                                    }

                                    optionsRun.setText(optionsText.toString());
                                    optionsRun.setFontFamily("SimHei");
                                    optionsRun.setFontSize(10);

                                } else if (useTwoColumns) {
                                    XWPFTable optionsTable = document.createTable(optionCount / 2, 2);
                                    optionsTable.setWidth("95%");

                                    for (int i = 0; i < optionsTable.getNumberOfRows(); i++) {
                                        XWPFTableRow row = optionsTable.getRow(i);
                                        for (int j = 0; j < row.getTableCells().size(); j++) {
                                            XWPFTableCell cell = row.getCell(j);
                                            cell.getCTTc().addNewTcPr().addNewTcBorders();
                                        }
                                    }

                                    int row = 0, col = 0;
                                    for (Map<String, String> option : options) {
                                        XWPFTableCell cell = optionsTable.getRow(row).getCell(col);
                                        cell.setText(option.getOrDefault("key", "") + ". " + option.getOrDefault("name", ""));
                                        col++;
                                        if (col >= 2) {
                                            col = 0;
                                            row++;
                                        }
                                    }

                                } else {
                                    for (Map<String, String> option : options) {
                                        XWPFParagraph optionParagraph = document.createParagraph();
                                        optionParagraph.setIndentationLeft(200);
                                        XWPFRun optionRun = optionParagraph.createRun();
                                        optionRun.setText(option.getOrDefault("key", "") + ". " + option.getOrDefault("name", ""));
                                        optionRun.setFontFamily("SimHei");
                                        optionRun.setFontSize(10);
                                    }
                                }

                            } catch (Exception e) {
                                log.warn("解析题目选项JSON失败: {}", e.getMessage());
                                XWPFParagraph errorParagraph = document.createParagraph();
                                errorParagraph.setIndentationLeft(200);
                                XWPFRun errorRun = errorParagraph.createRun();
                                errorRun.setText("(选项解析失败或格式错误)");
                                errorRun.setFontFamily("SimHei");
                                errorRun.setFontSize(10);
                            }
                        } else if ("judge".equals(topicInternalType)) {
                            XWPFParagraph answerParagraph = document.createParagraph();
                            answerParagraph.setIndentationLeft(200);
                            XWPFRun answerRun = answerParagraph.createRun();
                            answerRun.setText("答案: (　　　)");
                            answerRun.setFontFamily("SimHei");
                            answerRun.setFontSize(10);
                        } else if ("fill".equals(topicInternalType)) {
                            XWPFParagraph answerParagraph = document.createParagraph();
                            answerParagraph.setIndentationLeft(200);
                            XWPFRun answerRun = answerParagraph.createRun();
                            answerRun.setText("答案: __________________");
                            answerRun.setFontFamily("SimHei");
                            answerRun.setFontSize(10);
                        } else if ("short".equals(topicInternalType)) {
                            XWPFParagraph answerParagraph = document.createParagraph();
                            answerParagraph.setIndentationLeft(200);
                            XWPFRun answerRun = answerParagraph.createRun();
                            answerRun.setText("答案:");
                            answerRun.setFontFamily("SimHei");
                            answerRun.setFontSize(10);

                            // 为简答题预留足够的答题空间
                            for (int i = 0; i < 5; i++) {
                                document.createParagraph();
                            }
                        }

                        if ("group".equals(topicInternalType) && topic.getSubs() != null && !topic.getSubs().isEmpty()) {
                            try {
                                List<Topic> subTopics = mapper.readValue(topic.getSubs(), new TypeReference<List<Topic>>() {});
                                if (!subTopics.isEmpty()) {
                                    int subNum = 1;
                                    for (Topic subTopic : subTopics) {
                                        XWPFParagraph subHeadingParagraph = document.createParagraph();
                                        subHeadingParagraph.setIndentationLeft(150);
                                        subHeadingParagraph.setSpacingBefore(10);
                                        XWPFRun subHeadingRun = subHeadingParagraph.createRun();
                                        subHeadingRun.setText("子题 " + subNum++ + ": ");
                                        subHeadingRun.setBold(true);
                                        subHeadingRun.setFontFamily("SimHei");
                                        subHeadingRun.setFontSize(10);

                                        XWPFParagraph subContentParagraph = document.createParagraph();
                                        subContentParagraph.setIndentationLeft(200);
                                        XWPFRun subContentRun = subContentParagraph.createRun();
                                        subContentRun.setText(subTopic.getTitle());
                                        subContentRun.setFontFamily("SimHei");
                                        subContentRun.setFontSize(10);
                                    }
                                }
                            } catch (Exception e) {
                                log.warn("解析子题目失败: {}", e.getMessage());
                            }
                        }

                        // 如果是标准版，在每题后添加答案和解析
                        if ("standard".equals(paperType)) {
                            // 添加答案
                            if (topic.getAnswer() != null && !topic.getAnswer().isEmpty()) {
                                XWPFParagraph answerParagraph = document.createParagraph();
                                answerParagraph.setIndentationLeft(200);
                                answerParagraph.setSpacingBefore(5);

                                XWPFRun answerLabelRun = answerParagraph.createRun();
                                answerLabelRun.setText("【答案】");
                                answerLabelRun.setFontFamily("SimHei");
                                answerLabelRun.setFontSize(10);
                                answerLabelRun.setBold(true);
                                answerLabelRun.setColor("0066CC"); // 蓝色

                                XWPFRun answerContentRun = answerParagraph.createRun();
                                answerContentRun.setText(topic.getAnswer());
                                answerContentRun.setFontFamily("SimHei");
                                answerContentRun.setFontSize(10);
                            }

                            // 添加解析
                            if (topic.getAnalysis() != null && !topic.getAnalysis().isEmpty()) {
                                XWPFParagraph analysisParagraph = document.createParagraph();
                                analysisParagraph.setIndentationLeft(200);
                                analysisParagraph.setSpacingBefore(3);

                                XWPFRun analysisLabelRun = analysisParagraph.createRun();
                                analysisLabelRun.setText("【解析】");
                                analysisLabelRun.setFontFamily("SimHei");
                                analysisLabelRun.setFontSize(10);
                                analysisLabelRun.setBold(true);
                                analysisLabelRun.setColor("666666"); // 灰色

                                XWPFRun analysisContentRun = analysisParagraph.createRun();
                                analysisContentRun.setText(topic.getAnalysis());
                                analysisContentRun.setFontFamily("SimHei");
                                analysisContentRun.setFontSize(10);
                            }
                        }

                        XWPFParagraph spacingParagraph = document.createParagraph();
                        spacingParagraph.setSpacingAfter(5);
                    }

                    questionNumber += topicsOfType.size();
                }
            }

            // 注意：教师版和标准版的答案处理已经在上面的逻辑中完成

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            document.write(out);
            document.close();

            return new ByteArrayResource(out.toByteArray());
        } catch (Exception e) {
            log.error("生成Word文档失败: {}", e.getMessage(), e);
            return null;
        }
    }

    private List<Topic> enforceTypeCounts(List<Topic> original, Map<String, Integer> typeCountsFromRequest) {
        if (original == null || original.isEmpty()) {
            log.warn("EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: {}",
                    original == null ? "null" : original.size());
            return original;
        }

        if (typeCountsFromRequest == null || typeCountsFromRequest.isEmpty()) {
            log.warn("EnforceTypeCounts: No type counts specified in request, returning all selected topics.");
            return original;
        }

        log.info("EnforceTypeCounts: Starting. Requested counts from frontend: {}", typeCountsFromRequest);

        for (Map.Entry<String, Integer> entry : typeCountsFromRequest.entrySet()) {
            String originalKey = entry.getKey();
            String mappedKey = mapTopicType(originalKey);
            log.info("Topic type mapping: Original key='{}' → Mapped key='{}'" , originalKey, mappedKey);
        }

        Map<String, Long> originalCountsPerType = original.stream()
                .collect(Collectors.groupingBy(t -> mapTopicType(t.getType()), Collectors.counting()));
        log.info("EnforceTypeCounts: Topic counts from GA (before enforcement): {}", originalCountsPerType);

        Map<String, Integer> remainingCounts = new HashMap<>();
        for (Map.Entry<String, Integer> e : typeCountsFromRequest.entrySet()) {
            String dbType = mapTopicType(e.getKey());
            remainingCounts.put(dbType, e.getValue());
        }
        log.info("EnforceTypeCounts: Remaining counts after mapping: {}", remainingCounts);

        Map<String, Long> availablePerType = new HashMap<>();
        StringBuilder typeSummary = new StringBuilder("\n题型可用性详细统计：");
        boolean hasMissingTypes = false;
        boolean hasNoAvailableTypes = false;

        Map<String, String> typeChineseNames = new HashMap<>();
        typeChineseNames.put("choice", "单选题");
        typeChineseNames.put("multiple", "多选题");
        typeChineseNames.put("judge", "判断题");
        typeChineseNames.put("fill", "填空题");
        typeChineseNames.put("short", "简答题");
        typeChineseNames.put("subjective", "主观题");
        typeChineseNames.put("group", "组合题");

        for (Map.Entry<String, Integer> entry : remainingCounts.entrySet()) {
            String type = entry.getKey();
            int requested = entry.getValue();

            List<Topic> topicsOfType = original.stream()
                    .filter(t -> mapTopicType(t.getType()).equals(type))
                    .collect(Collectors.toList());

            long available = topicsOfType.size();
            availablePerType.put(type, available);

            String typeName = typeChineseNames.getOrDefault(type, type);
            typeSummary.append("\n- ").append(typeName);
            typeSummary.append(": 请求").append(requested);
            typeSummary.append("/实际").append(available);

            if (requested > 0 && available == 0) {
                typeSummary.append(" [警告: 此题型在题库中完全不存在!]");
                log.warn("EnforceTypeCounts: Type '{}' (requested {}) has NO available questions in the database.", type, requested);
                hasNoAvailableTypes = true;
            } else if (available < requested) {
                typeSummary.append(" [警告: 缺少").append(requested - available).append("题]");
                log.warn("EnforceTypeCounts: Type '{}' (requested {}) has insufficient questions available ({}). Deficit: {}",
                        type, requested, available, (requested - available));
                hasMissingTypes = true;
            }
        }

        log.info(typeSummary.toString());

        if (hasNoAvailableTypes) {
            log.warn("EnforceTypeCounts: CRITICAL ISSUE - Some requested topic types have ZERO available questions in the database. Check the knowledge point's question bank.");
        } else if (hasMissingTypes) {
            log.warn("EnforceTypeCounts: Some topic types do not have enough questions available. Will use all available topics of requested types.");

            // 提供智能建议
            StringBuilder suggestions = new StringBuilder("\n智能建议：");
            for (Map.Entry<String, Integer> entry : remainingCounts.entrySet()) {
                String type = entry.getKey();
                int requested = entry.getValue();
                long available = availablePerType.getOrDefault(type, 0L);

                if (requested > 0 && available < requested) {
                    String typeName = typeChineseNames.getOrDefault(type, type);
                    suggestions.append("\n- ").append(typeName).append("：");
                    suggestions.append("当前可用").append(available).append("题，需要").append(requested).append("题");
                    suggestions.append("，建议：");

                    if (available == 0) {
                        suggestions.append("选择包含此题型的其他知识点");
                    } else {
                        suggestions.append("将需求调整为").append(available).append("题，或增加更多知识点");
                    }
                }
            }
            log.info(suggestions.toString());
        }

        Map<String, List<Topic>> topicsByType = original.stream()
                .collect(Collectors.groupingBy(t -> mapTopicType(t.getType())));

        List<Topic> result = new ArrayList<>();

        String[] standardOrder = {"choice", "multiple", "judge", "fill", "short", "subjective", "group"};
        for (String type : standardOrder) {
            Integer requestedCount = remainingCounts.get(type);
            List<Topic> typeTopics = topicsByType.get(type);

            if (requestedCount != null && requestedCount > 0 && typeTopics != null && !typeTopics.isEmpty()) {
                int toTake = Math.min(requestedCount, typeTopics.size());
                result.addAll(typeTopics.subList(0, toTake));
                log.info("EnforceTypeCounts: Added {} topics of type '{}' (requested {})",
                        toTake, type, requestedCount);
            }
        }

        for (Map.Entry<String, List<Topic>> entry : topicsByType.entrySet()) {
            String type = entry.getKey();
            if (!Arrays.asList(standardOrder).contains(type)) {
                Integer requestedCount = remainingCounts.get(type);
                List<Topic> typeTopics = entry.getValue();

                if (requestedCount != null && requestedCount > 0 && typeTopics != null && !typeTopics.isEmpty()) {
                    int toTake = Math.min(requestedCount, typeTopics.size());
                    result.addAll(typeTopics.subList(0, toTake));
                    log.info("EnforceTypeCounts: Added {} topics of non-standard type '{}' (requested {})",
                            toTake, type, requestedCount);
                }
            }
        }

        Map<String, Long> actualFinalCounts = result.stream()
                .collect(Collectors.groupingBy(t -> mapTopicType(t.getType()), Collectors.counting()));
        log.info("EnforceTypeCounts: Finished. Original GA output size: {}. Final result size: {}. Actual counts per type after enforcement: {}",
                original.size(), result.size(), actualFinalCounts);

        for(Map.Entry<String, Integer> requestedEntry : typeCountsFromRequest.entrySet()) {
            String requestedTypeKey = mapTopicType(requestedEntry.getKey());
            long requestedCount = requestedEntry.getValue();
            long actualCount = actualFinalCounts.getOrDefault(requestedTypeKey, 0L);
            if (actualCount < requestedCount) {
                log.warn("EnforceTypeCounts: Discrepancy for type '{}' (requested key '{}'). Requested: {}, Actual: {}. 知识点中可能没有足够此类型的题目.",
                        requestedTypeKey, requestedEntry.getKey(), requestedCount, actualCount);
            }
        }

        return result;
    }

    /**
     * 映射题型到前端标准格式（用于PDF生成）
     * 使用统一的TopicTypeMapper工具类
     */
    private String mapTopicType(String typeKey) {
        if (typeKey == null) {
            return null;
        }
        log.debug("mapTopicType called with typeKey: '{}'", typeKey);

        // 先转换为数据库标准格式，再转换为前端格式（PDF生成需要前端格式）
        String dbFormat = TopicTypeMapper.toDbFormat(typeKey);
        String result = TopicTypeMapper.toFrontendFormat(dbFormat);

        log.debug("mapTopicType result for '{}': '{}' (db: '{}')", typeKey, result, dbFormat);
        return result;
    }

    private String getChineseTopicTypeName(String internalTopicType) {
        if (internalTopicType == null) return "未知题型";
        // 先转换为数据库格式，再获取中文名称
        String dbFormat = TopicTypeMapper.toDbFormat(internalTopicType);
        return TopicTypeMapper.getChineseName(dbFormat);
    }

    private int calculateExpectedScore(Map<String, Integer> typeScoreMap, Map<String, Integer> typeCounts) {
        if (typeScoreMap == null || typeCounts == null) {
            return 0;
        }

        return typeCounts.entrySet().stream()
                .mapToInt(entry -> {
                    String type = entry.getKey();
                    Integer count = entry.getValue();
                    Integer score = typeScoreMap.getOrDefault(type, 0);
                    return count * score;
                })
                .sum();
    }

    /**
     * 计算试卷总分数
     * 基于题型数量和每题分值计算总分
     *
     * @param typeCountMap 题型数量映射（例如 {"SINGLE_CHOICE": 10, "MULTIPLE_CHOICE": 5}）
     * @param typeScoreMap 题型分值映射（例如 {"SINGLE_CHOICE": 2, "MULTIPLE_CHOICE": 4}）
     * @return 计算出的总分，如果计算结果为0，则返回默认值100
     */
    private int calculateTotalScore(Map<String, Integer> typeCountMap, Map<String, Integer> typeScoreMap) {
        // 防御性编程，处理空输入
        if (typeCountMap == null || typeCountMap.isEmpty() || typeScoreMap == null || typeScoreMap.isEmpty()) {
            log.warn("计算总分时发现空的题型数量或分值映射，返回默认值100");
            return 100;
        }

        int totalScore = 0;
        StringBuilder detailLog = new StringBuilder("计算总分详情:\n");

        for (Map.Entry<String, Integer> entry : typeCountMap.entrySet()) {
            String type = entry.getKey();
            Integer count = entry.getValue();
            Integer scorePerQuestion = typeScoreMap.get(type);

            // 跳过数量为0或者分值不存在的题型
            if (count == null || count <= 0 || scorePerQuestion == null || scorePerQuestion <= 0) {
                continue;
            }

            int typeScore = count * scorePerQuestion;
            totalScore += typeScore;

            // 记录详细的计算过程，便于调试
            detailLog.append(String.format("  %s: %d题 * %d分 = %d分\n",
                    getChineseTopicTypeName(type), count, scorePerQuestion, typeScore));
        }

        detailLog.append(String.format("  总分: %d分", totalScore));
        log.info(detailLog.toString());

        // 如果计算出的总分为0，返回默认值100
        if (totalScore <= 0) {
            log.warn("计算的总分为0，可能是因为题型分值配置有问题，返回默认值100");
            return 100;
        }

        return totalScore;
    }

    private BaseFont loadFontFromResource(String fontPath, String fontName) throws IOException, com.itextpdf.text.DocumentException {
        try {
            log.info("正在加载{}字体，路径: {}", fontName, fontPath);

            // 尝试从classpath加载字体
            try (InputStream fontStream = getClass().getClassLoader().getResourceAsStream(fontPath)) {
                if (fontStream == null) {
                    throw new IOException("字体文件未找到: " + fontPath);
                }

                // 读取字体数据到字节数组
                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                byte[] data = new byte[1024];
                int nRead;
                while ((nRead = fontStream.read(data, 0, data.length)) != -1) {
                    buffer.write(data, 0, nRead);
                }
                byte[] fontData = buffer.toByteArray();
                log.info("成功读取{}字体数据，大小: {} bytes", fontName, fontData.length);

                // 使用字节数组创建BaseFont
                return BaseFont.createFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, true, fontData, null);
            }
        } catch (Exception e) {
            log.error("加载{}字体失败: {}", fontName, e.getMessage());
            throw e;
        }
    }

    /**
     * 预检查知识点是否有简答题
     * 在生成试卷前检查知识点是否有简答题，避免重复警告
     *
     * @param configs 知识点配置列表
     * @return 没有简答题的知识点ID列表
     */
    private List<Long> preCheckKnowledgePointsForShortAnswerQuestions(List<KnowledgePointConfigRequest> configs) {
        if (configs == null || configs.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取需要包含简答题的知识点ID列表
        List<Long> knowledgeIdsRequiringShortAnswer = configs.stream()
                .filter(config -> config.getIncludeShortAnswer() != null && config.getIncludeShortAnswer())
                .map(KnowledgePointConfigRequest::getKnowledgeId)
                .collect(Collectors.toList());

        if (knowledgeIdsRequiringShortAnswer.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用缓存服务检查哪些知识点有简答题
        List<Long> knowledgeIdsWithShortAnswer = knowledgePointQuestionService
                .getKnowledgePointsWithShortAnswerQuestions(knowledgeIdsRequiringShortAnswer);

        // 找出没有简答题的知识点
        List<Long> knowledgeIdsWithoutShortAnswer = knowledgeIdsRequiringShortAnswer.stream()
                .filter(id -> !knowledgeIdsWithShortAnswer.contains(id))
                .collect(Collectors.toList());

        // 如果有知识点没有简答题，记录一条汇总警告
        if (!knowledgeIdsWithoutShortAnswer.isEmpty()) {
            log.warn("以下知识点要求包含简答题但没有可用的简答题: {}", knowledgeIdsWithoutShortAnswer);
        }

        return knowledgeIdsWithoutShortAnswer;
    }

    /**
     * 处理自定义知识点配置的题目选择
     *
     * @param request 自定义试卷请求
     * @param preCheckedKnowledgePointsWithoutShortAnswer 预检查的没有简答题的知识点ID列表
     * @return 选择的题目列表
     */
    private List<Topic> processCustomTopic(CustomPaperRequest request, List<Long> preCheckedKnowledgePointsWithoutShortAnswer) {
        List<KnowledgePointConfigRequest> knowledgeConfigs = request.getKnowledgePointConfigs();
        if (knowledgeConfigs == null || knowledgeConfigs.isEmpty()) {
            log.warn("自定义试卷请求中没有知识点配置");
            return Collections.emptyList();
        }

        List<Topic> selectedTopics = new ArrayList<>();
        List<Long> knowledgePointsWithoutQuestions = new ArrayList<>();

        // 使用预检查的没有简答题的知识点列表，避免重复检查
        List<Long> knowledgePointsWithoutShortAnswer = new ArrayList<>(preCheckedKnowledgePointsWithoutShortAnswer);

        // 按知识点选择题目
        for (KnowledgePointConfigRequest config : knowledgeConfigs) {
            Long knowledgeId = config.getKnowledgeId();
            if (knowledgeId == null) {
                continue;
            }

            Integer questionCount = config.getQuestionCount();
            if (questionCount == null || questionCount <= 0) {
                continue;
            }

            // 查询该知识点下的题目
            List<Topic> topicsForKnowledge = topicMapper.selectFromBakByKnowId(knowledgeId.intValue());
            if (topicsForKnowledge == null || topicsForKnowledge.isEmpty()) {
                log.warn("知识点 {} 没有可用的题目", knowledgeId);
                knowledgePointsWithoutQuestions.add(knowledgeId);
                continue;
            }

            // 按题型分组 - 修复：使用本地mapTopicType方法确保一致性
            Map<String, List<Topic>> typeToTopicsMap = topicsForKnowledge.stream()
                    .collect(Collectors.groupingBy(t -> mapTopicType(t.getType())));

            //  修复：正确处理简答题配置，支持精确数量控制
            if (config.hasShortAnswerConfiguration()) {
                List<Topic> shortAnswerTopics = typeToTopicsMap.getOrDefault("SHORT", Collections.emptyList());
                int requiredShortAnswerCount = config.getShortAnswerCount();

                log.info("知识点 {} 需要 {} 道简答题，可用简答题数量: {}",
                        knowledgeId, requiredShortAnswerCount, shortAnswerTopics.size());

                if (shortAnswerTopics.isEmpty()) {
                    // 如果该知识点没有简答题，记录警告
                    if (!knowledgePointsWithoutShortAnswer.contains(knowledgeId)) {
                        knowledgePointsWithoutShortAnswer.add(knowledgeId);
                    }
                    log.warn("知识点 {} 开启了简答题但题库中没有简答题", knowledgeId);
                } else {
                    //  根据用户配置的数量选择简答题
                    Collections.shuffle(shortAnswerTopics);
                    int actualShortAnswerCount = Math.min(requiredShortAnswerCount, shortAnswerTopics.size());

                    for (int i = 0; i < actualShortAnswerCount; i++) {
                        selectedTopics.add(shortAnswerTopics.get(i));
                    }

                    log.info("知识点 {} 成功选择了 {} 道简答题（需要 {} 道）",
                            knowledgeId, actualShortAnswerCount, requiredShortAnswerCount);

                    //  重要：简答题是额外的，不减少基础题目数量
                    // questionCount 保持不变，因为简答题是独立计算的

                    if (actualShortAnswerCount < requiredShortAnswerCount) {
                        log.warn("知识点 {} 简答题不足：需要 {} 道，实际选择 {} 道",
                                knowledgeId, requiredShortAnswerCount, actualShortAnswerCount);
                    }
                }
            }

            //  选择基础题目（排除简答题类型，避免重复）
            if (questionCount > 0) {
                // 获取非简答题类型的题目
                List<Topic> basicTopics = typeToTopicsMap.entrySet().stream()
                        .filter(entry -> !"SHORT".equals(entry.getKey())) // 排除简答题类型
                        .flatMap(entry -> entry.getValue().stream())
                        .filter(t -> !selectedTopics.contains(t)) // 排除已选的题目
                        .collect(Collectors.toList());

                log.info("知识点 {} 需要选择 {} 道基础题目，可用基础题目数量: {}",
                        knowledgeId, questionCount, basicTopics.size());

                // 随机打乱题目顺序
                Collections.shuffle(basicTopics);

                // 选择指定数量的基础题目
                int toSelect = Math.min(questionCount, basicTopics.size());
                for (int i = 0; i < toSelect; i++) {
                    selectedTopics.add(basicTopics.get(i));
                }

                log.info("知识点 {} 成功选择了 {} 道基础题目（需要 {} 道）",
                        knowledgeId, toSelect, questionCount);

                if (toSelect < questionCount) {
                    log.warn("知识点 {} 基础题目不足：需要 {} 道，实际选择 {} 道",
                            knowledgeId, questionCount, toSelect);
                }
            }
        }

        // 应用难度分布约束
        Map<String, Double> difficultyDistribution = request.getDifficultyDistribution();
        if (difficultyDistribution != null && !difficultyDistribution.isEmpty()) {
            // 按照难度分布选择题目
            // 如果有难度分布要求，尝试平衡题目难度
            // 这里只是简单排序，实际项目中可能有更复杂的实现
            selectedTopics.sort(Comparator.comparing(topic ->
                    topic.getDifficulty() != null ? topic.getDifficulty() : 3.0));
        }

        // 记录没有任何题目的知识点
        if (!knowledgePointsWithoutQuestions.isEmpty()) {
            log.warn("以下知识点没有任何可用题目: {}", knowledgePointsWithoutQuestions);
        }

        return selectedTopics;
    }

    @Override
    @Transactional
    public PaperGenerationResponse generateCustomPaper(CustomPaperRequest customRequest) {
        log.info("处理自由组卷请求: 标题='{}', 知识点配置={}, 题型分值={}, 难度分布={}",
                customRequest.getTitle(),
                customRequest.getKnowledgePointConfigs(),
                customRequest.getTypeScoreMap(),
                customRequest.getDifficultyDistribution());

        // 使用现有字段
        Map<String, Integer> typeScoreMap = customRequest.getTypeScoreMap();

        // 从知识点配置计算全局题型数量
        Map<String, Integer> globalTypeCounts = new HashMap<>();
        if (customRequest.getKnowledgePointConfigs() != null) {
            for (KnowledgePointConfigRequest config : customRequest.getKnowledgePointConfigs()) {
                Integer questionCount = config.getQuestionCount();
                if (questionCount != null && questionCount > 0) {
                    // 假设每个知识点的题目都是单选题，实际应该根据具体需求调整
                    globalTypeCounts.put("singleChoice",
                            globalTypeCounts.getOrDefault("singleChoice", 0) + questionCount);
                }
            }
        }

        log.info("计算得到的全局题型数量: {}", globalTypeCounts);

        // 检查typeScoreMap是否为空，这是必需的
        if (typeScoreMap == null || typeScoreMap.isEmpty()) {
            PaperGenerationResponse errorResponse = new PaperGenerationResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("题型分值映射不能为空");
            return errorResponse;
        }

        // 检查globalTypeCounts是否为空，这是必需的
        if (globalTypeCounts == null || globalTypeCounts.isEmpty()) {
            PaperGenerationResponse errorResponse = new PaperGenerationResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("全局题型数量配置不能为空");
            return errorResponse;
        }

        // 注意：CustomPaperRequest没有setGlobalTypeCounts方法，我们直接使用局部变量

        // 预检查知识点是否有简答题，避免重复警告
        List<Long> knowledgePointsWithoutShortAnswer = preCheckKnowledgePointsForShortAnswerQuestions(
                customRequest.getKnowledgePointConfigs());

        // 创建标准的PaperGenerationRequest
        PaperGenerationRequest request = new PaperGenerationRequest();
        request.setTitle(customRequest.getTitle());
        request.setKnowledgePointConfigs(customRequest.getKnowledgePointConfigs());
        request.setTypeScoreMap(typeScoreMap);
        request.setDifficultyCriteria(customRequest.getDifficultyDistribution());

        // 使用全局题型配置作为题型数量
        request.setTopicTypeCounts(globalTypeCounts);

        // 打印最终请求信息，确保题型数量正确设置
        log.info("最终生成试卷请求：题型数量={}, 题型分数={}", request.getTopicTypeCounts(), request.getTypeScoreMap());

        // 根据题型数量和分值计算总分
        int calculatedTotalScore = calculateExpectedScore(typeScoreMap, globalTypeCounts);
        request.setTotalScore(calculatedTotalScore);

        log.info("自由组卷转换为标准请求: 题型数量={}, 题型分值={}, 预计总分={}", globalTypeCounts, typeScoreMap, calculatedTotalScore);

        // 调用引擎处理组卷
        PaperGenerationResponse response = new PaperGenerationResponse();

        // 按照要求使用自定义知识点配置获取题目
        List<Topic> selectedTopics = processCustomTopic(customRequest, knowledgePointsWithoutShortAnswer);

        // 如果没有足够题目，返回错误
        if (selectedTopics.isEmpty()) {
            response.setSuccess(false);
            response.setErrorMessage("无法获取足够题目，请调整知识点配置");
            return response;
        }

        // 计算实际的题型分布 (actualTypeCounts is now calculated correctly by the helper)
        Map<String, Integer> actualTypeCounts = calculateTypeCountsFromTopics(selectedTopics);

        // 验证是否满足每个知识点的简答题要求
        Map<Long, Boolean> shortAnswerRequirements = new HashMap<>();
        if (customRequest.getKnowledgePointConfigs() != null) {
            for (KnowledgePointConfigRequest kpConfig : customRequest.getKnowledgePointConfigs()) {
                if (kpConfig.getIncludeShortAnswer()) {
                    shortAnswerRequirements.put(kpConfig.getKnowledgeId(), Boolean.FALSE); // 初始化为未满足
                }
            }
        }

        for (Topic topic : selectedTopics) {
            Integer knowId = topic.getKnowId();
            if (knowId != null && "short".equals(topic.getType())) {
                Long knowledgeId = Long.valueOf(knowId);
                if (shortAnswerRequirements.containsKey(knowledgeId)) {
                    shortAnswerRequirements.put(knowledgeId, Boolean.TRUE); // 标记为已满足
                }
            }
        }

        // 构建警告信息
        StringBuilder warningMsg = new StringBuilder();

        List<Long> missingShortAnswerKnowledgeIds = shortAnswerRequirements.entrySet().stream()
                .filter(entry -> !entry.getValue())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (!missingShortAnswerKnowledgeIds.isEmpty()) {
            warningMsg.append("警告：部分指定需包含简答题的知识点未能满足要求，可能是因为题库中缺少对应的简答题。");
            warningMsg.append("涉及知识点ID: ").append(missingShortAnswerKnowledgeIds.stream().map(String::valueOf).collect(Collectors.joining(", ")));
            warningMsg.append("\n");
            log.warn("未能满足简答题要求的知识点: {}", missingShortAnswerKnowledgeIds);
        }

        // 统计题型数量差异 (using global type counts from request as target)
        Map<String, Integer> targetTypeCounts = globalTypeCounts;
        if (targetTypeCounts != null) {
            for (Map.Entry<String, Integer> targetEntry : targetTypeCounts.entrySet()) {
                String type = targetEntry.getKey();
                int targetCount = targetEntry.getValue();
                int actualCount = actualTypeCounts.getOrDefault(type, 0);

                if (actualCount < targetCount) {
                    String typeName = getChineseTopicTypeName(type);
                    warningMsg.append(String.format("警告：%s实际选择了%d题，少于目标数量%d题。\n", typeName, actualCount, targetCount));
                }
            }
        }

        // 创建并保存试卷
        Paper paperToSave = new Paper();
        paperToSave.setTitle(customRequest.getTitle());
        paperToSave.setType(customRequest.getType());

        // 计算试卷总分 - 始终基于目标题型数量而非实际题型数量
        // 这确保即使题库中缺少某些题型，总分仍然与前端设置一致
        int actualTotalScore = 0;
        int expectedTotalScore = 0;

        // 先计算基于实际题型数量的总分，用于比较和调试
        int actualTypeBasedScore = calculateTotalScore(actualTypeCounts, customRequest.getTypeScoreMap());
        log.info("基于实际题型数量计算总分: {}", actualTypeBasedScore);

        if (targetTypeCounts != null && !targetTypeCounts.isEmpty()) {
            // 使用目标题型数量计算总分（这是前端设置的题型数量）
            expectedTotalScore = calculateTotalScore(targetTypeCounts, customRequest.getTypeScoreMap());
            log.info("基于目标题型数量计算总分: {}", expectedTotalScore);

            // 始终使用目标总分，确保与前端设置一致
            actualTotalScore = expectedTotalScore;

            // 打印警告信息，如果实际总分与目标总分不一致
            if (actualTypeBasedScore != expectedTotalScore) {
                log.warn("警告: 实际题目总分({}) 与目标总分({}) 不一致", actualTypeBasedScore, expectedTotalScore);
                warningMsg.append(String.format("\n注意: 由于题库限制，实际题目总分为%d分，但试卷总分保持为%d分。\n",
                        actualTypeBasedScore, expectedTotalScore));
            }
        } else {
            // 如果没有目标题型数量，则使用实际题型数量（很少发生）
            actualTotalScore = actualTypeBasedScore;
            log.info("未指定目标题型数量，使用实际题型数量计算总分");
        }

        // 记录最终计算的总分
        log.info("最终试卷总分设置为: {}", actualTotalScore);
        paperToSave.setTotalScore(actualTotalScore);

        // 评估试卷质量
        double paperQualityScore = calculatePaperDifficulty(selectedTopics);

        // 设置试卷内容（题目ID列表）
        String topicIdsContent = selectedTopics.stream()
                .map(t -> String.valueOf(t.getId()))
                .collect(Collectors.joining(","));
        paperToSave.setContent(topicIdsContent);

        // 保存配置信息
        Map<String, Object> configMap = new HashMap<>();
        configMap.put("difficultyDistribution", customRequest.getDifficultyDistribution());
        configMap.put("typeScoreMap", typeScoreMap);
        configMap.put("globalTypeCounts", globalTypeCounts);
        // configMap.put("globalTypeConfigs", null); // CustomPaperRequest没有此字段
        configMap.put("knowledgePointConfigs", customRequest.getKnowledgePointConfigs());
        configMap.put("actualTypeCounts", actualTypeCounts);

        String configJson;
        try {
            configJson = mapper.writeValueAsString(configMap);
        } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
            log.error("Error serializing paper config to JSON for title '{}': {}", customRequest.getTitle(), configMap, e);
            configJson = "{\"error\": \"failed to serialize config\"}";
        }

        paperToSave.setConfig(configJson);
        paperToSave.setDifficulty(paperQualityScore);
        paperToSave.setCreateTime(LocalDateTime.now());
        paperToSave.setUpdateTime(LocalDateTime.now());
        paperToSave.setIsDeleted(false);

        // 保存试卷
        Paper savedPaper = paperRepository.save(paperToSave);
        log.info("已保存自由组卷试卷，ID: {}, 标题: {}", savedPaper.getId(), savedPaper.getTitle());

        // 构建响应
        response.setSuccess(true);
        response.setId(savedPaper.getId());
        response.setTitle(savedPaper.getTitle());
        response.setSelectedTopics(selectedTopics);
        response.setPaperDifficulty(paperQualityScore);

        // 添加警告信息
        if (warningMsg.length() > 0) {
            response.setWarningMessage(warningMsg.toString());
        }

        // 添加题型数量统计
        response.setTypeCountMap(actualTypeCounts);

        return response;
    }

    /**
     * 计算试卷的难度系数
     */
    private double calculatePaperDifficulty(List<Topic> topics) {
        if (topics == null || topics.isEmpty()) {
            return 3.0; // 默认中等难度
        }

        // 计算平均难度
        double totalDifficulty = 0.0;
        int validCount = 0;

        for (Topic topic : topics) {
            Double difficulty = topic.getDifficulty();
            if (difficulty != null && difficulty > 0) {
                totalDifficulty += difficulty;
                validCount++;
            }
        }

        if (validCount == 0) {
            return 3.0; // 默认中等难度
        }

        return totalDifficulty / validCount;
    }



    /**
     * Calculates a difficulty distribution map based on an overall paper difficulty score.
     * Assumes overallDifficulty corresponds to levels like 1.0 (Easy), 2.0 (Medium), 3.0 (Hard).
     * The returned map uses keys "1", "2", "3" for these levels.
     *
     * @param overallDifficulty The overall difficulty score of the paper.
     * @param topics The list of topics selected for the paper (currently unused in this basic implementation).
     * @return A map representing the difficulty distribution (e.g., {"1": 0.6, "2": 0.3, "3": 0.1}).
     */
    private Map<String, Double> calculateDifficultyDistribution(Double overallDifficulty, List<Topic> topics) {
        Map<String, Double> distribution = new LinkedHashMap<>();

        if (overallDifficulty == null) {
            log.warn("Overall difficulty is null, returning a default balanced distribution.");
            // Default balanced distribution for "1" (Easy), "2" (Medium), "3" (Hard)
            distribution.put("1", 0.33);
            distribution.put("2", 0.34);
            distribution.put("3", 0.33);
            return distribution;
        }

        double overallDifficultyValue = overallDifficulty.doubleValue();

        // These thresholds and proportions are illustrative and should be adjusted
        // based on your system's specific difficulty scale and desired distributions.
        // Using a small epsilon for double comparison
        double epsilon = 0.01;
        if (Math.abs(overallDifficultyValue - 1.0) < epsilon) { // Overall difficulty is Easy (1.0)
            distribution.put("1", 0.60); // 60% Easy
            distribution.put("2", 0.30); // 30% Medium
            distribution.put("3", 0.10); // 10% Hard
        } else if (Math.abs(overallDifficultyValue - 2.0) < epsilon) { // Overall difficulty is Medium (2.0)
            distribution.put("1", 0.30); // 30% Easy
            distribution.put("2", 0.40); // 40% Medium
            distribution.put("3", 0.30); // 30% Hard
        } else if (Math.abs(overallDifficultyValue - 3.0) < epsilon) { // Overall difficulty is Hard (3.0)
            distribution.put("1", 0.10); // 10% Easy
            distribution.put("2", 0.30); // 30% Medium
            distribution.put("3", 0.60); // 60% Hard
        } else {
            log.warn("Overall difficulty {} does not match predefined levels (1.0, 2.0, 3.0). Using default balanced distribution.", overallDifficultyValue);
            distribution.put("1", 0.33);
            distribution.put("2", 0.34);
            distribution.put("3", 0.33);
        }

        log.info("Calculated difficulty distribution for overall difficulty {}: {}", overallDifficultyValue, distribution);
        return distribution;
    }

    private Map<String, Integer> calculateTypeCountsFromTopics(List<Topic> topics) {
        Map<String, Integer> typeCounts = new HashMap<>();
        if (topics == null) {
            return typeCounts;
        }
        for (Topic topic : topics) {
            String type = topic.getType();
            if (type != null) {
                // 修复：使用映射后的题型名称确保一致性
                String mappedType = mapTopicType(type);
                typeCounts.put(mappedType, typeCounts.getOrDefault(mappedType, 0) + 1);
            }
        }
        return typeCounts;
    }

    @Override
    public PaperPreviewResponse previewPaper(PaperPreviewRequest request) {
        log.info("开始生成试卷预览，知识点配置数量: {}, 题型配置: {}",
                request.getKnowledgePointConfigs() != null ? request.getKnowledgePointConfigs().size() : 0,
                request.getTypeCountMap());

        PaperPreviewResponse response = new PaperPreviewResponse();
        response.setTitle(request.getTitle());
        response.setRequestedCountsByType(request.getTypeCountMap());
        response.setScoresByType(request.getTypeScoreMap());

        // 初始化响应数据结构
        Map<String, List<Topic>> previewTopicsByType = new HashMap<>();
        Map<String, Integer> availableCountsByType = new HashMap<>();
        List<String> warnings = new ArrayList<>();

        // 统计信息
        int totalRequestedQuestions = 0;
        int totalAvailableQuestions = 0;
        int expectedTotalScore = 0;
        int actualTotalScore = 0;

        try {
            // 从知识点配置获取题目
            List<Topic> allAvailableTopics = new ArrayList<>();
            if (request.getKnowledgePointConfigs() != null) {
                for (KnowledgePointConfigRequest config : request.getKnowledgePointConfigs()) {
                    if (config.getKnowledgeId() != null) {
                        List<Topic> topicsForKnowledge = topicMapper.selectFromBakByKnowId(config.getKnowledgeId().intValue());
                        if (topicsForKnowledge != null) {
                            allAvailableTopics.addAll(topicsForKnowledge);
                        }
                    }
                }
            }

            // 按题型分组
            Map<String, List<Topic>> topicsByType = allAvailableTopics.stream()
                    .collect(Collectors.groupingBy(t -> mapTopicType(t.getType())));

            // 处理每种题型
            for (Map.Entry<String, Integer> entry : request.getTypeCountMap().entrySet()) {
                String typeKey = entry.getKey();
                Integer requestedCount = entry.getValue();

                if (requestedCount == null || requestedCount <= 0) {
                    continue;
                }

                // 映射题型名称
                String internalType = mapTopicType(typeKey);
                List<Topic> typeTopics = topicsByType.getOrDefault(internalType, new ArrayList<>());

                // 统计可用题目数
                int availableCount = typeTopics.size();
                availableCountsByType.put(typeKey, availableCount);

                // 选择预览题目
                int previewLimit = Math.min(request.getPreviewLimit() != null ? request.getPreviewLimit() : 3,
                        Math.min(requestedCount, availableCount));

                List<Topic> previewTopics = new ArrayList<>();
                if (!typeTopics.isEmpty() && previewLimit > 0) {
                    // 随机选择预览题目
                    Collections.shuffle(typeTopics);
                    previewTopics = typeTopics.subList(0, previewLimit);
                }

                previewTopicsByType.put(typeKey, previewTopics);

                // 统计数据
                totalRequestedQuestions += requestedCount;
                totalAvailableQuestions += availableCount;

                Integer scorePerQuestion = request.getTypeScoreMap().get(typeKey);
                if (scorePerQuestion != null) {
                    expectedTotalScore += requestedCount * scorePerQuestion;
                    actualTotalScore += Math.min(requestedCount, availableCount) * scorePerQuestion;
                }

                // 检查题目不足的情况
                if (availableCount < requestedCount) {
                    String typeName = getChineseTopicTypeName(internalType);
                    warnings.add(String.format("%s题目不足：需要%d题，可用%d题",
                            typeName, requestedCount, availableCount));
                }
            }

            // 设置响应数据
            response.setPreviewTopicsByType(previewTopicsByType);
            response.setAvailableCountsByType(availableCountsByType);
            response.setWarnings(warnings);

            // 设置统计信息
            PaperPreviewResponse.PreviewStats stats = new PaperPreviewResponse.PreviewStats();
            stats.setTotalRequestedQuestions(totalRequestedQuestions);
            stats.setTotalAvailableQuestions(totalAvailableQuestions);
            stats.setExpectedTotalScore(expectedTotalScore);
            stats.setActualTotalScore(actualTotalScore);

            response.setStats(stats);

            log.info("试卷预览生成完成，总请求题目: {}, 总可用题目: {}, 警告数量: {}",
                    totalRequestedQuestions, totalAvailableQuestions, warnings.size());

        } catch (Exception e) {
            log.error("生成试卷预览时出错: {}", e.getMessage(), e);
            warnings.add("预览生成失败: " + e.getMessage());
            response.setWarnings(warnings);
        }

        return response;
    }

    /**
     *  为教师版添加题目答案和解析（显示题目标题、答案和解析）
     */
    private int addTeacherAnswerToDocument(com.itextpdf.text.Document document, Topic topic, Font textFont, Font scoreFont, int qNum) throws com.itextpdf.text.DocumentException {
        log.info("🔍 教师版题目 {} 数据检查: 标题='{}', 答案='{}', 解析='{}'",
                topic.getId(), topic.getTitle(), topic.getAnswer(), topic.getAnalysis());

        PdfPTable answerTable = new PdfPTable(1);
        answerTable.setWidthPercentage(100);
        answerTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
        answerTable.setSpacingAfter(15f);

        //  添加题目标题（教师版也需要看到题目内容）
        Phrase titlePhrase = new Phrase();
        titlePhrase.add(new Chunk(qNum + ". ", scoreFont));
        if (topic.getTitle() != null && !topic.getTitle().isEmpty()) {
            titlePhrase.add(createContentPhrase(topic.getTitle(), textFont, 10f, TeXConstants.STYLE_TEXT));
        } else {
            titlePhrase.add(new Chunk("(无题目标题)", textFont));
        }

        PdfPCell titleCell = new PdfPCell(titlePhrase);
        titleCell.setBorder(PdfPCell.NO_BORDER);
        titleCell.setPaddingBottom(8f);
        titleCell.setBackgroundColor(new BaseColor(245, 245, 245)); // 浅灰色背景
        answerTable.addCell(titleCell);

        //  添加答案
        Phrase answerPhrase = new Phrase();
        answerPhrase.add(new Chunk("【答案】", scoreFont));
        if (topic.getAnswer() != null && !topic.getAnswer().isEmpty()) {
            answerPhrase.add(new Chunk(" " + topic.getAnswer(), textFont));
        } else {
            answerPhrase.add(new Chunk(" (未提供答案)", textFont));
        }

        PdfPCell answerCell = new PdfPCell(answerPhrase);
        answerCell.setBorder(PdfPCell.NO_BORDER);
        answerCell.setPaddingLeft(15f);
        answerCell.setPaddingBottom(5f);
        answerCell.setBackgroundColor(new BaseColor(240, 248, 255)); // 浅蓝色背景
        answerTable.addCell(answerCell);

        //  添加解析
        if (topic.getAnalysis() != null && !topic.getAnalysis().isEmpty()) {
            Phrase analysisPhrase = new Phrase();
            analysisPhrase.add(new Chunk("【解析】", scoreFont));
            analysisPhrase.add(new Chunk(" ", textFont));
            analysisPhrase.add(createContentPhrase(topic.getAnalysis(), textFont, 10f, TeXConstants.STYLE_TEXT));

            PdfPCell analysisCell = new PdfPCell(analysisPhrase);
            analysisCell.setBorder(PdfPCell.NO_BORDER);
            analysisCell.setPaddingLeft(15f);
            analysisCell.setPaddingBottom(10f);
            analysisCell.setBackgroundColor(new BaseColor(248, 255, 248)); // 浅绿色背景
            answerTable.addCell(analysisCell);
        } else {
            // 即使没有解析也显示一个提示
            Phrase noAnalysisPhrase = new Phrase();
            noAnalysisPhrase.add(new Chunk("【解析】", scoreFont));
            noAnalysisPhrase.add(new Chunk(" (未提供解析)", textFont));

            PdfPCell noAnalysisCell = new PdfPCell(noAnalysisPhrase);
            noAnalysisCell.setBorder(PdfPCell.NO_BORDER);
            noAnalysisCell.setPaddingLeft(15f);
            noAnalysisCell.setPaddingBottom(10f);
            noAnalysisCell.setBackgroundColor(new BaseColor(248, 248, 248)); // 浅灰色背景
            answerTable.addCell(noAnalysisCell);
        }

        document.add(answerTable);
        return qNum + 1;
    }

    /**
     *  批量生成试卷
     */
    @Override
    @Transactional
    public BatchPaperGenerationResponse generateBatchPapers(BatchPaperGenerationRequest request) {
        log.info("开始批量生成试卷: 标题='{}', 套数={}", request.getTitle(), request.getPaperCount());

        BatchPaperGenerationResponse batchResponse = new BatchPaperGenerationResponse();

        for (int i = 1; i <= request.getPaperCount(); i++) {
            try {
                // 为每套试卷创建单独的标题
                String currentTitle = request.getPaperCount() > 1 ?
                        request.getTitle() + " (第" + i + "套)" : request.getTitle();

                // 构建单套试卷生成请求
                PaperGenerationRequest singleRequest = new PaperGenerationRequest();
                singleRequest.setTitle(currentTitle);
                singleRequest.setKnowledgePointConfigs(request.getKnowledgePointConfigs());

                // 类型转换：Double -> Integer
                if (request.getTotalScore() != null) {
                    singleRequest.setTotalScore(request.getTotalScore().intValue());
                }

                singleRequest.setTopicTypeCounts(request.getTopicTypeCounts());
                singleRequest.setTypeScoreMap(request.getTypeScoreMap());

                // 类型转换：DifficultyDistribution -> Map<String, Double>
                if (request.getDifficultyCriteria() != null) {
                    Map<String, Double> difficultyMap = new HashMap<>();
                    difficultyMap.put("easy", request.getDifficultyCriteria().getEasy());
                    difficultyMap.put("medium", request.getDifficultyCriteria().getMedium());
                    difficultyMap.put("hard", request.getDifficultyCriteria().getHard());
                    singleRequest.setDifficultyCriteria(difficultyMap);
                }

                // 生成单套试卷
                PaperGenerationResponse singleResponse = generatePaper(singleRequest);

                if (singleResponse.getSuccess()) {
                    batchResponse.addSuccessResult(currentTitle, singleResponse.getId(), singleResponse);
                    log.info("第 {} 套试卷生成成功: {}", i, currentTitle);
                } else {
                    batchResponse.addFailedResult(currentTitle, singleResponse.getErrorMessage());
                    log.warn("第 {} 套试卷生成失败: {}, 错误: {}", i, currentTitle, singleResponse.getErrorMessage());
                }

            } catch (Exception e) {
                String currentTitle = request.getTitle() + " (第" + i + "套)";
                batchResponse.addFailedResult(currentTitle, "生成异常: " + e.getMessage());
                log.error("第 {} 套试卷生成异常: {}", i, currentTitle, e);
            }
        }

        // 完成批量生成
        batchResponse.complete();

        log.info("批量生成完成: 总计 {} 套, 成功 {} 套, 失败 {} 套",
                batchResponse.getTotalCount(), batchResponse.getSuccessCount(), batchResponse.getFailedCount());

        return batchResponse;
    }
}