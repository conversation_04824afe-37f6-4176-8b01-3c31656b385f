package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.dto.TopicQueryDTO;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.service.TopicService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map; // Added import
import java.util.stream.Collectors;

@Service
@Validated
public class TopicServiceImpl extends ServiceImpl<TopicMapper, Topic> implements TopicService {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public void validateAndSaveTopics(List<TopicDTO> topics) {
        List<Topic> topicEntities = new ArrayList<>();
        for (TopicDTO dto : topics) {
            validateTopic(dto); 
            Topic topic = convertToEntity(dto); 
            topicEntities.add(topic);
        }
        this.saveBatch(topicEntities);
    }

    @Override
    public IPage<Topic> listTopics(TopicQueryDTO queryDTO) {
        Page<Topic> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<Topic> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(queryDTO.getKeyword())) {
            String keyword = queryDTO.getKeyword();
            queryWrapper.and(qw -> qw.like("title", keyword)
                                     .or().like("options", keyword)
                                     .or().like("parse", keyword));
        }

        if (StringUtils.hasText(queryDTO.getType())) {
            queryWrapper.eq("type", queryDTO.getType());
        }

        if (queryDTO.getDifficultyMin() != null) {
            queryWrapper.ge("difficulty", queryDTO.getDifficultyMin());
        }
        if (queryDTO.getDifficultyMax() != null) {
            queryWrapper.le("difficulty", queryDTO.getDifficultyMax());
        }

        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }

        if (queryDTO.getKnowId() != null) {
            queryWrapper.eq("know_id", queryDTO.getKnowId());
        }

        // Dynamic sorting
        String sortBy = queryDTO.getSortBy();
        String sortOrder = queryDTO.getSortOrder();

        // Whitelist of allowed sortable columns (actual database column names)
        List<String> allowedSortColumns = java.util.Arrays.asList("id", "know_id", "type", "title", "score", "difficulty", "created_at");

        if (StringUtils.hasText(sortBy) && allowedSortColumns.contains(sortBy.toLowerCase()) && StringUtils.hasText(sortOrder)) {
            if ("ASC".equalsIgnoreCase(sortOrder)) {
                queryWrapper.orderByAsc(sortBy);
            } else if ("DESC".equalsIgnoreCase(sortOrder)) {
                queryWrapper.orderByDesc(sortBy);
            } else {
                // Default sort if sortOrder is invalid but sortBy is provided
                queryWrapper.orderByDesc("created_at"); 
            }
        } else {
            // Default sort if no valid sortBy is provided
            queryWrapper.orderByDesc("created_at");
        }

        return this.page(page, queryWrapper);
    }

    private void validateTopic(@Valid TopicDTO topic) {
        if (("choice".equals(topic.getType()) || "multiple".equals(topic.getType()))
                && (topic.getOptions() == null || topic.getOptions().isEmpty())) {
            throw new IllegalArgumentException("选择题必须包含选项");
        }

        if ("judge".equals(topic.getType()) && topic.getAnswer() != null
                && !("是".equals(topic.getAnswer()) || "否".equals(topic.getAnswer()))) {
            throw new IllegalArgumentException("判断题答案必须是'是'或'否'");
        }

        if (("choice".equals(topic.getType()) || "multiple".equals(topic.getType()))
                && topic.getAnswer() != null) {
            if (!topic.getAnswer().matches("^[A-Z]+$")) {
                throw new IllegalArgumentException("选择题答案必须是大写字母");
            }
            String sortedAnswer = topic.getAnswer().chars()
                    .mapToObj(ch -> String.valueOf((char) ch))
                    .sorted()
                    .collect(Collectors.joining());
            if (!topic.getAnswer().equals(sortedAnswer)) {
                throw new IllegalArgumentException("选择题答案必须按字母顺序排列");
            }
        }
    }

        private Topic convertToEntity(TopicDTO dto) {
        Topic topic = new Topic();
        topic.setKnowId(dto.getKnowId());
        topic.setType(dto.getType());
        topic.setTitle(dto.getTitle());
        topic.setCreatedAt(LocalDateTime.now());

        if (StringUtils.hasText(dto.getTags())) {
            topic.setTags(dto.getTags());
        } else {
            throw new IllegalArgumentException("标签不能为空");
        }

        if (dto.getOptions() != null && !dto.getOptions().isEmpty()) {
            try {
                topic.setOptions(objectMapper.writeValueAsString(dto.getOptions()));
            } catch (Exception e) {
                throw new IllegalArgumentException("选项格式转换失败: " + e.getMessage(), e);
            }
        } else {
            topic.setOptions("[]");
        }

        if ("group".equals(dto.getType())) {
            topic.setSubs(dto.getSubs());
        } else {
            topic.setSubs("[]");
        }

        topic.setAnswer(dto.getAnswer());
        topic.setParse(dto.getParse());
        topic.setScore(dto.getScore() != null ? dto.getScore() : 3);
        topic.setSource(dto.getSource());
        topic.setSort(dto.getSort() != null ? dto.getSort() : 1);
        topic.setDifficulty(dto.getDifficulty());
        return topic;
    }

    @Override
    public List<Map<String, Object>> countTopicsByTypeForKnowledgePoints(List<Integer> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return new ArrayList<>(); // Return empty list if input is empty
        }
        // Assuming baseMapper (TopicMapper) has this method correctly defined
        return baseMapper.countTopicsByTypeForKnowledgePoints(knowledgeIds);
    }

    @Override
    public List<Map<String, Object>> getTopicStatistics(String type, LocalDate startDate, LocalDate endDate) {
        return baseMapper.getTopicStatistics(type, startDate, endDate);
    }
}