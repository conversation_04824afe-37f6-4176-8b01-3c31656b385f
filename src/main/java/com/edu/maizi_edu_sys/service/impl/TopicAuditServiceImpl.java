package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.entity.TopicRejected;
import com.edu.maizi_edu_sys.mapper.TopicAuditMapper;
import com.edu.maizi_edu_sys.mapper.TopicRejectedMapper;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.service.SystemMessageService;
import com.edu.maizi_edu_sys.service.TopicAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 题目审核服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TopicAuditServiceImpl implements TopicAuditService {

    private final TopicAuditMapper topicAuditMapper;
    private final TopicRejectedMapper topicRejectedMapper;
    private final TopicMapper topicMapper;
    private final SystemMessageService systemMessageService;

    @Override
    @Transactional
    public void submitTopicForAudit(TopicDTO topicDTO, Long userId) {
        TopicAudit audit = new TopicAudit();
        
        // 复制基本信息
        audit.setUserId(userId);
        audit.setKnowId(topicDTO.getKnowId());
        audit.setType(topicDTO.getType());
        audit.setTitle(topicDTO.getTitle());
        audit.setOptions(topicDTO.getOptions().toString());
        audit.setSubs(topicDTO.getSubs());
        audit.setAnswer(topicDTO.getAnswer());
        audit.setParse(topicDTO.getParse());
        audit.setScore(topicDTO.getScore() != null ? topicDTO.getScore() : 3);
        audit.setSource(topicDTO.getSource());
        audit.setDifficulty(topicDTO.getDifficulty());
        audit.setTags(topicDTO.getTags());
        
        // 设置审核状态
        audit.setAuditStatus(TopicAudit.AuditStatus.PENDING.getCode());
        audit.setAutoApproved(false);
        audit.setSubmitTime(LocalDateTime.now());
        
        topicAuditMapper.insert(audit);
        log.info("题目提交审核成功: userId={}, title={}", userId, topicDTO.getTitle());
    }

    @Override
    @Transactional
    public void submitTopicsForAudit(List<TopicDTO> topicDTOs, Long userId) {
        for (TopicDTO topicDTO : topicDTOs) {
            submitTopicForAudit(topicDTO, userId);
        }
        log.info("批量提交题目审核成功: userId={}, count={}", userId, topicDTOs.size());
    }

    @Override
    public IPage<TopicAudit> getAuditList(int pageNum, int pageSize, Integer auditStatus, String keyword) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TopicAudit> queryWrapper = new LambdaQueryWrapper<>();
        
        if (auditStatus != null) {
            queryWrapper.eq(TopicAudit::getAuditStatus, auditStatus);
        }
        
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(TopicAudit::getTitle, keyword);
        }
        
        queryWrapper.orderByDesc(TopicAudit::getSubmitTime);
        
        return topicAuditMapper.selectAuditPageWithUsernames(page, queryWrapper);
    }

    @Override
    @Transactional
    public void approveTopicAudit(Long auditId, Long auditorId, String comment) {
        TopicAudit audit = topicAuditMapper.selectById(auditId);
        if (audit == null) {
            throw new IllegalArgumentException("审核记录不存在");
        }
        
        if (!TopicAudit.AuditStatus.PENDING.getCode().equals(audit.getAuditStatus())) {
            throw new IllegalArgumentException("该题目已经审核过了");
        }
        
        // 更新审核状态
        audit.setAuditStatus(TopicAudit.AuditStatus.APPROVED.getCode());
        audit.setAuditorId(auditorId);
        audit.setAuditTime(LocalDateTime.now());
        audit.setAuditComment(comment);
        topicAuditMapper.updateById(audit);
        
        // 将题目添加到正式题库
        addTopicToDatabase(audit);
        
        // 发送审核通过消息
        systemMessageService.sendAuditApprovedMessage(audit.getUserId(), auditId, audit.getTitle());
        
        log.info("题目审核通过: auditId={}, auditorId={}", auditId, auditorId);
    }

    @Override
    @Transactional
    public void rejectTopicAudit(Long auditId, Long auditorId, String rejectReason) {
        TopicAudit audit = topicAuditMapper.selectById(auditId);
        if (audit == null) {
            throw new IllegalArgumentException("审核记录不存在");
        }
        
        if (!TopicAudit.AuditStatus.PENDING.getCode().equals(audit.getAuditStatus())) {
            throw new IllegalArgumentException("该题目已经审核过了");
        }
        
        // 更新审核状态
        audit.setAuditStatus(TopicAudit.AuditStatus.REJECTED.getCode());
        audit.setAuditorId(auditorId);
        audit.setAuditTime(LocalDateTime.now());
        audit.setAuditComment(rejectReason);
        topicAuditMapper.updateById(audit);
        
        // 将拒绝的题目保存到拒绝表
        saveRejectedTopic(audit, rejectReason);
        
        // 发送审核拒绝消息
        systemMessageService.sendAuditRejectedMessage(audit.getUserId(), auditId, audit.getTitle(), rejectReason);
        
        log.info("题目审核拒绝: auditId={}, auditorId={}, reason={}", auditId, auditorId, rejectReason);
    }

    @Override
    public TopicAudit getAuditDetail(Long auditId) {
        return topicAuditMapper.selectById(auditId);
    }

    @Override
    public Long getPendingAuditCount() {
        return topicAuditMapper.countPendingAudits();
    }

    @Override
    public Long getUserTodaySubmissionCount(Long userId) {
        return topicAuditMapper.countUserTodaySubmissions(userId);
    }

    @Override
    public TopicAuditMapper.TopicAuditStats getUserAuditStats(Long userId) {
        return topicAuditMapper.getUserAuditStats(userId);
    }

    @Override
    @Transactional
    public void autoApproveOverdueTopics() {
        LocalDateTime weekAgo = LocalDateTime.now().minusDays(7);
        List<TopicAudit> overdueAudits = topicAuditMapper.selectOverdueAudits(weekAgo);
        
        for (TopicAudit audit : overdueAudits) {
            // 更新为自动通过
            audit.setAuditStatus(TopicAudit.AuditStatus.APPROVED.getCode());
            audit.setAutoApproved(true);
            audit.setAuditTime(LocalDateTime.now());
            audit.setAuditComment("系统自动审核通过（超过7天未处理）");
            topicAuditMapper.updateById(audit);
            
            // 添加到题库
            addTopicToDatabase(audit);
            
            // 发送通知
            systemMessageService.sendAutoApprovedMessage(audit.getUserId(), audit.getId(), audit.getTitle());
        }
        
        log.info("自动审核通过超时题目: count={}", overdueAudits.size());
    }

    @Override
    public IPage<TopicAudit> getUserAuditList(Long userId, int pageNum, int pageSize, Integer auditStatus) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TopicAudit> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(TopicAudit::getUserId, userId);
        
        if (auditStatus != null) {
            queryWrapper.eq(TopicAudit::getAuditStatus, auditStatus);
        }
        
        queryWrapper.orderByDesc(TopicAudit::getSubmitTime);
        
        return topicAuditMapper.selectPage(page, queryWrapper);
    }

    /**
     * 将审核通过的题目添加到正式题库
     */
    private void addTopicToDatabase(TopicAudit audit) {
        Topic topic = new Topic();

        // 手动复制字段，确保类型兼容
        topic.setKnowId(audit.getKnowId());
        topic.setType(audit.getType());
        topic.setTitle(audit.getTitle());
        topic.setOptions(audit.getOptions());
        topic.setSubs(audit.getSubs());
        topic.setAnswer(audit.getAnswer());
        topic.setParse(audit.getParse());
        topic.setScore(audit.getScore());
        topic.setSource(audit.getSource());
        topic.setDifficulty(audit.getDifficulty());
        topic.setTags(audit.getTags());
        topic.setSort(1); // 默认排序
        topic.setCreatedAt(LocalDateTime.now());

        topicMapper.insert(topic);
    }

    /**
     * 保存被拒绝的题目
     */
    private void saveRejectedTopic(TopicAudit audit, String rejectReason) {
        TopicRejected rejected = new TopicRejected();

        // 手动复制字段，确保类型兼容
        rejected.setAuditId(audit.getId());
        rejected.setUserId(audit.getUserId());
        rejected.setKnowId(audit.getKnowId());
        rejected.setType(audit.getType());
        rejected.setTitle(audit.getTitle());
        rejected.setOptions(audit.getOptions());
        rejected.setSubs(audit.getSubs());
        rejected.setAnswer(audit.getAnswer());
        rejected.setParse(audit.getParse());
        rejected.setScore(audit.getScore());
        rejected.setSource(audit.getSource());
        rejected.setDifficulty(audit.getDifficulty());
        rejected.setTags(audit.getTags());
        rejected.setAuditorId(audit.getAuditorId());
        rejected.setRejectReason(rejectReason);
        rejected.setRejectTime(LocalDateTime.now());
        rejected.setSubmitTime(audit.getSubmitTime());

        topicRejectedMapper.insert(rejected);
    }
}
