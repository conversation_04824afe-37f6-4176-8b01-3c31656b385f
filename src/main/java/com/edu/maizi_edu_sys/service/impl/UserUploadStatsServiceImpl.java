package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.maizi_edu_sys.entity.UserUploadStats;
import com.edu.maizi_edu_sys.repository.UserUploadStatsMapper;
import com.edu.maizi_edu_sys.service.UserUploadStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户上传统计服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserUploadStatsServiceImpl extends ServiceImpl<UserUploadStatsMapper, UserUploadStats> 
        implements UserUploadStatsService {

    private final UserUploadStatsMapper userUploadStatsMapper;
    
    // 每日上传限制
    private static final Integer DAILY_UPLOAD_LIMIT = 5000;

    @Override
    @Transactional
    public void recordUserUpload(Long userId, Integer count) {
        if (userId == null || count == null || count <= 0) {
            return;
        }
        
        try {
            LocalDate today = LocalDate.now();
            userUploadStatsMapper.incrementUserUploadCount(userId, today, count);
            log.debug("记录用户上传统计成功，用户ID: {}, 数量: {}, 日期: {}", userId, count, today);
        } catch (Exception e) {
            log.error("记录用户上传统计失败，用户ID: {}, 数量: {}", userId, count, e);
            throw new RuntimeException("记录上传统计失败", e);
        }
    }

    @Override
    public Integer getUserTodayUploadCount(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        try {
            LocalDate today = LocalDate.now();
            Integer count = userUploadStatsMapper.getUserUploadCountByDate(userId, today);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取用户今日上传数量失败，用户ID: {}", userId, e);
            return 0;
        }
    }

    @Override
    public Integer getUserUploadCountByDate(Long userId, LocalDate date) {
        if (userId == null || date == null) {
            return 0;
        }
        
        try {
            Integer count = userUploadStatsMapper.getUserUploadCountByDate(userId, date);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取用户指定日期上传数量失败，用户ID: {}, 日期: {}", userId, date, e);
            return 0;
        }
    }

    @Override
    public boolean isExceedDailyLimit(Long userId, Integer additionalCount) {
        if (userId == null || additionalCount == null || additionalCount <= 0) {
            return false;
        }
        
        try {
            Integer todayCount = getUserTodayUploadCount(userId);
            return (todayCount + additionalCount) > DAILY_UPLOAD_LIMIT;
        } catch (Exception e) {
            log.error("检查每日上传限制失败，用户ID: {}, 额外数量: {}", userId, additionalCount, e);
            return true; // 出错时保守处理，认为超过限制
        }
    }

    @Override
    public Map<String, Object> getUserUploadOverview(Long userId) {
        Map<String, Object> overview = new HashMap<>();
        
        if (userId == null) {
            return overview;
        }
        
        try {
            // 今日上传数量
            Integer todayCount = getUserTodayUploadCount(userId);
            overview.put("todayCount", todayCount);
            
            // 本周上传数量
            LocalDate startOfWeek = LocalDate.now().minusDays(LocalDate.now().getDayOfWeek().getValue() - 1);
            LocalDate endOfWeek = startOfWeek.plusDays(6);
            List<Map<String, Object>> weekStats = userUploadStatsMapper.getUserUploadStatsByDateRange(
                    userId, startOfWeek, endOfWeek);
            Integer weekCount = weekStats.stream()
                    .mapToInt(stat -> ((Number) stat.get("topic_count")).intValue())
                    .sum();
            overview.put("weekCount", weekCount);
            
            // 本月上传数量
            LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
            LocalDate endOfMonth = startOfMonth.plusMonths(1).minusDays(1);
            List<Map<String, Object>> monthStats = userUploadStatsMapper.getUserUploadStatsByDateRange(
                    userId, startOfMonth, endOfMonth);
            Integer monthCount = monthStats.stream()
                    .mapToInt(stat -> ((Number) stat.get("topic_count")).intValue())
                    .sum();
            overview.put("monthCount", monthCount);
            
            // 总上传数量
            Integer totalCount = userUploadStatsMapper.getUserTotalUploadCount(userId);
            overview.put("totalCount", totalCount != null ? totalCount : 0);
            
            // 每日限制和剩余量
            overview.put("dailyLimit", DAILY_UPLOAD_LIMIT);
            overview.put("remainingToday", Math.max(0, DAILY_UPLOAD_LIMIT - todayCount));
            
            // 连续上传天数（简单实现，可以优化）
            overview.put("consecutiveDays", calculateConsecutiveDays(userId));
            
        } catch (Exception e) {
            log.error("获取用户上传概览失败，用户ID: {}", userId, e);
        }
        
        return overview;
    }

    @Override
    public List<Map<String, Object>> getUserUploadTrend(Long userId, Integer days) {
        if (userId == null || days == null || days <= 0) {
            return Collections.emptyList();
        }

        try {
            return userUploadStatsMapper.getUploadTrend(userId, days);
        } catch (Exception e) {
            log.error("获取用户上传趋势失败，用户ID: {}, 天数: {}", userId, days, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Map<String, Object>> getUserUploadStatsByDateRange(Long userId, LocalDate startDate, LocalDate endDate) {
        if (userId == null || startDate == null || endDate == null) {
            return Collections.emptyList();
        }

        try {
            return userUploadStatsMapper.getUserUploadStatsByDateRange(userId, startDate, endDate);
        } catch (Exception e) {
            log.error("获取用户日期范围上传统计失败，用户ID: {}, 开始日期: {}, 结束日期: {}",
                    userId, startDate, endDate, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, Object> getAdminUploadOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        try {
            LocalDate today = LocalDate.now();
            
            // 今日全站上传数量
            List<Map<String, Object>> todayStats = userUploadStatsMapper.getTotalUploadStatsByDateRange(today, today);
            Integer todayTotal = todayStats.isEmpty() ? 0 : 
                    ((Number) todayStats.get(0).get("total_count")).intValue();
            overview.put("todayTotal", todayTotal);
            
            // 今日活跃用户数
            Integer todayActiveUsers = todayStats.isEmpty() ? 0 : 
                    ((Number) todayStats.get(0).get("user_count")).intValue();
            overview.put("todayActiveUsers", todayActiveUsers);
            
            // 本周统计
            LocalDate startOfWeek = today.minusDays(today.getDayOfWeek().getValue() - 1);
            List<Map<String, Object>> weekStats = userUploadStatsMapper.getTotalUploadStatsByDateRange(startOfWeek, today);
            Integer weekTotal = weekStats.stream()
                    .mapToInt(stat -> ((Number) stat.get("total_count")).intValue())
                    .sum();
            overview.put("weekTotal", weekTotal);
            
            // 本月统计
            LocalDate startOfMonth = today.withDayOfMonth(1);
            List<Map<String, Object>> monthStats = userUploadStatsMapper.getTotalUploadStatsByDateRange(startOfMonth, today);
            Integer monthTotal = monthStats.stream()
                    .mapToInt(stat -> ((Number) stat.get("total_count")).intValue())
                    .sum();
            overview.put("monthTotal", monthTotal);
            
            // 用户排行榜（本周）
            List<Map<String, Object>> weeklyRanking = userUploadStatsMapper.getUserUploadRanking(startOfWeek, today, 10);
            overview.put("weeklyRanking", weeklyRanking);
            
        } catch (Exception e) {
            log.error("获取管理员上传概览失败", e);
        }
        
        return overview;
    }

    @Override
    public List<Map<String, Object>> getTotalUploadTrend(Integer days) {
        if (days == null || days <= 0) {
            return Collections.emptyList();
        }

        try {
            return userUploadStatsMapper.getUploadTrend(null, days);
        } catch (Exception e) {
            log.error("获取全站上传趋势失败，天数: {}", days, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Map<String, Object>> getUserUploadRanking(LocalDate startDate, LocalDate endDate, Integer limit) {
        if (startDate == null || endDate == null || limit == null || limit <= 0) {
            return Collections.emptyList();
        }

        try {
            return userUploadStatsMapper.getUserUploadRanking(startDate, endDate, limit);
        } catch (Exception e) {
            log.error("获取用户上传排行榜失败，开始日期: {}, 结束日期: {}, 限制: {}",
                    startDate, endDate, limit, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Map<String, Object>> getTotalUploadStatsByDate(LocalDate date) {
        if (date == null) {
            return Collections.emptyList();
        }

        try {
            return userUploadStatsMapper.getAllUsersUploadStatsByDate(date);
        } catch (Exception e) {
            log.error("获取指定日期全站上传统计失败，日期: {}", date, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Map<String, Object>> getTotalUploadStatsByDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return Collections.emptyList();
        }

        try {
            return userUploadStatsMapper.getTotalUploadStatsByDateRange(startDate, endDate);
        } catch (Exception e) {
            log.error("获取日期范围全站上传统计失败，开始日期: {}, 结束日期: {}", startDate, endDate, e);
            return Collections.emptyList();
        }
    }

    /**
     * 计算连续上传天数（简单实现）
     */
    private Integer calculateConsecutiveDays(Long userId) {
        try {
            List<Map<String, Object>> recentStats = userUploadStatsMapper.getUploadTrend(userId, 30);
            
            int consecutiveDays = 0;
            LocalDate currentDate = LocalDate.now();
            
            for (Map<String, Object> stat : recentStats) {
                LocalDate statDate = (LocalDate) stat.get("upload_date");
                Integer count = ((Number) stat.get("topic_count")).intValue();
                
                if (statDate.equals(currentDate) && count > 0) {
                    consecutiveDays++;
                    currentDate = currentDate.minusDays(1);
                } else {
                    break;
                }
            }
            
            return consecutiveDays;
        } catch (Exception e) {
            log.error("计算连续上传天数失败，用户ID: {}", userId, e);
            return 0;
        }
    }
}
