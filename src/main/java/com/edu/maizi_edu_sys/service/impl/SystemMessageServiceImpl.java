package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.SystemMessage;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.mapper.SystemMessageMapper;
import com.edu.maizi_edu_sys.service.SystemMessageService;
import com.edu.maizi_edu_sys.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统消息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemMessageServiceImpl implements SystemMessageService {

    private final SystemMessageMapper systemMessageMapper;
    private final UserService userService;

    @Override
    @Transactional
    public void sendAuditApprovedMessage(Long userId, Long auditId, String topicTitle) {
        SystemMessage message = new SystemMessage();
        message.setUserId(userId);
        message.setMessageType(SystemMessage.MessageType.AUDIT_APPROVED.getCode());
        message.setTitle("题目审核通过");
        message.setContent(String.format("您提交的题目《%s》已通过审核，现已加入题库。", topicTitle));
        message.setRelatedId(auditId);
        message.setRelatedType(SystemMessage.RelatedType.TOPIC_AUDIT.getCode());
        message.setIsRead(false);
        message.setPriority(SystemMessage.Priority.NORMAL.getCode());
        
        systemMessageMapper.insert(message);
        log.info("发送审核通过消息: userId={}, auditId={}", userId, auditId);
    }

    @Override
    @Transactional
    public void sendAuditRejectedMessage(Long userId, Long auditId, String topicTitle, String rejectReason) {
        SystemMessage message = new SystemMessage();
        message.setUserId(userId);
        message.setMessageType(SystemMessage.MessageType.AUDIT_REJECTED.getCode());
        message.setTitle("题目审核未通过");
        message.setContent(String.format("您提交的题目《%s》审核未通过。\n拒绝原因：%s\n请根据反馈意见修改后重新提交。", 
                topicTitle, rejectReason));
        message.setRelatedId(auditId);
        message.setRelatedType(SystemMessage.RelatedType.TOPIC_AUDIT.getCode());
        message.setIsRead(false);
        message.setPriority(SystemMessage.Priority.IMPORTANT.getCode());
        
        systemMessageMapper.insert(message);
        log.info("发送审核拒绝消息: userId={}, auditId={}", userId, auditId);
    }

    @Override
    @Transactional
    public void sendAutoApprovedMessage(Long userId, Long auditId, String topicTitle) {
        SystemMessage message = new SystemMessage();
        message.setUserId(userId);
        message.setMessageType(SystemMessage.MessageType.AUDIT_APPROVED.getCode());
        message.setTitle("题目自动审核通过");
        message.setContent(String.format("您提交的题目《%s》已自动审核通过（超过7天未处理），现已加入题库。", topicTitle));
        message.setRelatedId(auditId);
        message.setRelatedType(SystemMessage.RelatedType.TOPIC_AUDIT.getCode());
        message.setIsRead(false);
        message.setPriority(SystemMessage.Priority.NORMAL.getCode());
        
        systemMessageMapper.insert(message);
        log.info("发送自动审核通过消息: userId={}, auditId={}", userId, auditId);
    }

    @Override
    @Transactional
    public void sendSystemNotice(Long userId, String title, String content, Integer priority) {
        SystemMessage message = new SystemMessage();
        message.setUserId(userId);
        message.setMessageType(SystemMessage.MessageType.SYSTEM_NOTICE.getCode());
        message.setTitle(title);
        message.setContent(content);
        message.setIsRead(false);
        message.setPriority(priority != null ? priority : SystemMessage.Priority.NORMAL.getCode());
        
        systemMessageMapper.insert(message);
        log.info("发送系统通知: userId={}, title={}", userId, title);
    }

    @Override
    @Transactional
    public void sendSystemNoticeToAll(String title, String content, Integer priority) {
        List<User> allUsers = userService.list();
        for (User user : allUsers) {
            sendSystemNotice(user.getId(), title, content, priority);
        }
        log.info("批量发送系统通知: title={}, userCount={}", title, allUsers.size());
    }

    @Override
    public IPage<SystemMessage> getUserMessages(Long userId, int pageNum, int pageSize, Boolean isRead) {
        Page<SystemMessage> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SystemMessage> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(SystemMessage::getUserId, userId);
        
        if (isRead != null) {
            queryWrapper.eq(SystemMessage::getIsRead, isRead);
        }
        
        queryWrapper.orderByDesc(SystemMessage::getPriority)
                   .orderByDesc(SystemMessage::getCreatedAt);
        
        return systemMessageMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Long getUnreadMessageCount(Long userId) {
        return systemMessageMapper.countUnreadMessages(userId);
    }

    @Override
    @Transactional
    public void markMessageAsRead(Long messageId, Long userId) {
        int updated = systemMessageMapper.markAsRead(messageId, userId);
        if (updated > 0) {
            log.info("标记消息已读: messageId={}, userId={}", messageId, userId);
        }
    }

    @Override
    @Transactional
    public void markAllMessagesAsRead(Long userId) {
        int updated = systemMessageMapper.markAllAsRead(userId);
        log.info("标记所有消息已读: userId={}, count={}", userId, updated);
    }

    @Override
    public SystemMessage getMessageDetail(Long messageId, Long userId) {
        LambdaQueryWrapper<SystemMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemMessage::getId, messageId)
                   .eq(SystemMessage::getUserId, userId);
        
        SystemMessage message = systemMessageMapper.selectOne(queryWrapper);
        
        // 自动标记为已读
        if (message != null && !message.getIsRead()) {
            markMessageAsRead(messageId, userId);
            message.setIsRead(true);
            message.setReadTime(LocalDateTime.now());
        }
        
        return message;
    }

    @Override
    @Transactional
    public void deleteMessage(Long messageId, Long userId) {
        LambdaQueryWrapper<SystemMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemMessage::getId, messageId)
                   .eq(SystemMessage::getUserId, userId);
        
        int deleted = systemMessageMapper.delete(queryWrapper);
        if (deleted > 0) {
            log.info("删除消息: messageId={}, userId={}", messageId, userId);
        }
    }

    @Override
    public List<SystemMessage> getRecentNotices(Long userId, int limit) {
        return systemMessageMapper.selectRecentNotices(userId, limit);
    }
}
