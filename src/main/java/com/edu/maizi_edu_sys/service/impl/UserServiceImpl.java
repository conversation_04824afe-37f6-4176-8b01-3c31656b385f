package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.maizi_edu_sys.controller.UserController;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.dto.LoginRequest;
import com.edu.maizi_edu_sys.entity.dto.RegisterRequest;
import com.edu.maizi_edu_sys.mapper.UserMapper;
import com.edu.maizi_edu_sys.service.CaptchaService;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.io.FilenameUtils;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final BCryptPasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final CaptchaService captchaService;
    @Value("${file.upload.avatar.path}")
    private String avatarPath;

    @Override
    public ApiResponse<?> login(LoginRequest request) {
        // 验证验证码
        if (!captchaService.verifyCaptcha(request.getSessionId(), request.getCaptchaCode())) {
            log.warn("验证码验证失败，用户: {}, IP: {}", request.getUsername(), request.getIp());
            return ApiResponse.error("验证码错误或已过期");
        }

        User user = this.getOne(new QueryWrapper<User>()
                .eq("username", request.getUsername())
                .eq("deleted", 0));

        if (user == null || !passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            log.warn("登录失败，用户名或密码错误，用户: {}, IP: {}", request.getUsername(), request.getIp());
            return ApiResponse.error("用户名或密码错误");
        }

        // 更新登录信息
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(request.getIp());
        this.updateById(user);

        // 生成token
        String token = jwtUtil.generateToken(user);

        // 返回用户信息（排除敏感字段）
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("role", user.getRole());
        userInfo.put("lastLoginTime", user.getLastLoginTime());

        Map<String, Object> data = new HashMap<>();
        data.put("token", token);
        data.put("user", userInfo);

        return ApiResponse.success(data);
    }

    @Override
    public ApiResponse<String> register(RegisterRequest request) {
        // 检查用户名是否已存在
        if (this.count(new QueryWrapper<User>()
                .eq("username", request.getUsername())) > 0) {
            return ApiResponse.error("用户名已存在");
        }

        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setRole(1);
        user.setStatus(1);

        this.save(user);
        return ApiResponse.success("注册成功");
    }

    @Override
    public ApiResponse<String> logout(String token) {
        try {
            // 这里可以实现token黑名单等逻辑
            // 如果使用Redis，可以将token加入黑名单
            return ApiResponse.success("退出成功");
        } catch (Exception e) {
            log.error("退出失败", e);
            return ApiResponse.error("退出失败");
        }
    }

    @Override
    public ApiResponse<User> getCurrentUser(String token) {
        String username = jwtUtil.getUsernameFromToken(token);
        User user = this.getOne(new QueryWrapper<User>()
                .eq("username", username)
                .eq("deleted", 0));

        return user != null ? ApiResponse.success(user) : ApiResponse.error("用户不存在");
    }

    @Override
    public ApiResponse<String> updateAvatar(MultipartFile file, String token) {
        if (file.isEmpty()) {
            return ApiResponse.error("请选择文件");
        }

        try {
            // 验证文件类型
            String contentType = file.getContentType();
            if (!Arrays.asList("image/jpeg", "image/png", "image/gif").contains(contentType)) {
                return ApiResponse.error("不支持的文件类型");
            }

            // 验证文件大小（5MB）
            if (file.getSize() > 5 * 1024 * 1024) {
                return ApiResponse.error("文件大小不能超过5MB");
            }

            // 获取当前用户
            String username = jwtUtil.getUsernameFromToken(token);
            User user = this.getOne(new QueryWrapper<User>()
                    .eq("username", username)
                    .eq("deleted", 0));

            if (user == null) {
                return ApiResponse.error("用户不存在");
            }

            // 获取文件后缀
            String originalFilename = file.getOriginalFilename();
            String extension = FilenameUtils.getExtension(originalFilename);

            // 获取项目根目录的绝对路径
            String projectRoot = new File(".").getAbsolutePath();
            String absoluteAvatarPath = new File(projectRoot, avatarPath).getAbsolutePath();

            // 生成新的文件名
            String newFileName = String.format("%s_%s.%s",
                DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()),
                UUID.randomUUID().toString().substring(0, 8),
                extension);

            // 使用绝对路径
            File avatarDir = new File(absoluteAvatarPath);
            if (!avatarDir.exists()) {
                boolean created = avatarDir.mkdirs();
                if (!created) {
                    throw new IOException("Failed to create avatar directory");
                }
            }

            // 删除旧头像
            if (user.getAvatar() != null && !user.getAvatar().equals("default-avatar.png")) {
                String oldAvatarPath = user.getAvatar().replace("avatars/", "");
                File oldFile = new File(avatarDir, oldAvatarPath);
                if (oldFile.exists()) {
                    oldFile.delete();
                }
            }

            // 保存新文件
            File newFile = new File(avatarDir, newFileName);
            log.info("Saving avatar to: {}", newFile.getAbsolutePath());
            file.transferTo(newFile);

            // 更新用户头像信息 - 只存储相对路径
            // 确保路径格式一致，避免重复的 "avatars/" 前缀
            String avatarPath = "avatars/" + newFileName;
            user.setAvatar(avatarPath);
            this.updateById(user);

            log.info("Avatar updated successfully. User: {}, New avatar: {}", user.getUsername(), avatarPath);
            log.info("Avatar URL will be: /uploads/{}", avatarPath);
            return ApiResponse.success(avatarPath);
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return ApiResponse.error("头像上传失败：" + e.getMessage());
        }
    }

    @Override
    public ApiResponse<?> updateProfile(UserController.UserUpdateRequest updateRequest, String token) {
        try {
            // 验证token并获取用户
            if (!jwtUtil.validateToken(token)) {
                return ApiResponse.error("token无效或已过期");
            }

            String username = jwtUtil.getUsernameFromToken(token);
            User user = this.getByUsername(username);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }

            // 更新用户信息
            boolean updated = false;
            if (updateRequest.getEmail() != null && !updateRequest.getEmail().trim().isEmpty()) {
                user.setEmail(updateRequest.getEmail().trim());
                updated = true;
            }
            if (updateRequest.getPhone() != null && !updateRequest.getPhone().trim().isEmpty()) {
                user.setPhone(updateRequest.getPhone().trim());
                updated = true;
            }
            if (updateRequest.getBio() != null) {
                user.setBio(updateRequest.getBio().trim());
                updated = true;
            }

            if (updated) {
                this.updateById(user);
                log.info("用户信息更新成功: {}", username);
                return ApiResponse.success("用户信息更新成功");
            } else {
                return ApiResponse.error("没有需要更新的信息");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return ApiResponse.error("更新用户信息失败：" + e.getMessage());
        }
    }

    @Override
    public User getByUsername(String username) {
        return this.getOne(new QueryWrapper<User>()
            .eq("username", username)
            .eq("deleted", 0));
    }
}