package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.edu.maizi_edu_sys.entity.RolePermission;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.entity.UserPermission;
import com.edu.maizi_edu_sys.mapper.RolePermissionMapper;
import com.edu.maizi_edu_sys.mapper.UserPermissionMapper;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final UserPermissionMapper userPermissionMapper;
    private final RolePermissionMapper rolePermissionMapper;
    private final UserService userService;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        try {
            // 检查用户是否为管理员，管理员拥有所有权限
            User user = userService.getById(userId);
            if (user != null && user.getRole() == 1) {
                log.debug("管理员用户 {} 拥有所有权限: {}", userId, permissionCode);
                return true;
            }

            // 首先检查用户特定权限
            boolean hasUserPermission = userPermissionMapper.hasPermission(userId, permissionCode);
            if (hasUserPermission) {
                return true;
            }

            // 然后检查角色权限
            if (user != null) {
                return hasRolePermission(user.getRole(), permissionCode);
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户权限失败: userId={}, permissionCode={}", userId, permissionCode, e);
            return false;
        }
    }

    @Override
    public boolean canAccessApi(Long userId, String apiPath) {
        try {
            // 检查用户是否为管理员，管理员可以访问所有API
            User user = userService.getById(userId);
            if (user != null && user.getRole() == 1) {
                log.debug("管理员用户 {} 可以访问所有API: {}", userId, apiPath);
                return true;
            }

            // 获取用户可访问的API路径
            List<String> userApiPaths = getUserApiPaths(userId);

            // 获取用户角色可访问的API路径
            if (user != null) {
                List<String> roleApiPaths = getRoleApiPaths(user.getRole());
                userApiPaths.addAll(roleApiPaths);
            }

            // 使用Ant路径匹配器检查权限
            return userApiPaths.stream()
                    .anyMatch(pattern -> pathMatcher.match(pattern, apiPath));
        } catch (Exception e) {
            log.error("检查API访问权限失败: userId={}, apiPath={}", userId, apiPath, e);
            return false;
        }
    }

    @Override
    public List<UserPermission> getUserPermissions(Long userId) {
        return userPermissionMapper.selectUserPermissions(userId);
    }

    @Override
    public List<RolePermission> getRolePermissions(Integer roleId) {
        return rolePermissionMapper.selectRolePermissions(roleId);
    }

    @Override
    public void grantPermissionToUser(Long userId, String permissionCode, Long grantedBy) {
        try {
            // 检查权限是否已存在
            LambdaQueryWrapper<UserPermission> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserPermission::getUserId, userId)
                       .eq(UserPermission::getPermissionCode, permissionCode);
            
            UserPermission existingPermission = userPermissionMapper.selectOne(queryWrapper);
            
            if (existingPermission != null) {
                // 更新现有权限
                existingPermission.setIsActive(true);
                existingPermission.setGrantedBy(grantedBy);
                existingPermission.setGrantedTime(LocalDateTime.now());
                userPermissionMapper.updateById(existingPermission);
            } else {
                // 创建新权限
                UserPermission newPermission = new UserPermission();
                newPermission.setUserId(userId);
                newPermission.setPermissionCode(permissionCode);
                newPermission.setPermissionName(getPermissionName(permissionCode));
                newPermission.setResourceType("API");
                newPermission.setGrantedBy(grantedBy);
                newPermission.setGrantedTime(LocalDateTime.now());
                newPermission.setIsActive(true);
                userPermissionMapper.insert(newPermission);
            }
            
            log.info("用户权限授予成功: userId={}, permissionCode={}, grantedBy={}", userId, permissionCode, grantedBy);
        } catch (Exception e) {
            log.error("授予用户权限失败: userId={}, permissionCode={}", userId, permissionCode, e);
            throw new RuntimeException("授予权限失败", e);
        }
    }

    @Override
    public void revokePermissionFromUser(Long userId, String permissionCode) {
        try {
            LambdaQueryWrapper<UserPermission> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserPermission::getUserId, userId)
                       .eq(UserPermission::getPermissionCode, permissionCode);
            
            UserPermission permission = userPermissionMapper.selectOne(queryWrapper);
            if (permission != null) {
                permission.setIsActive(false);
                userPermissionMapper.updateById(permission);
                log.info("用户权限撤销成功: userId={}, permissionCode={}", userId, permissionCode);
            }
        } catch (Exception e) {
            log.error("撤销用户权限失败: userId={}, permissionCode={}", userId, permissionCode, e);
            throw new RuntimeException("撤销权限失败", e);
        }
    }

    @Override
    public boolean hasRolePermission(Integer roleId, String permissionCode) {
        return rolePermissionMapper.hasRolePermission(roleId, permissionCode);
    }

    @Override
    public List<String> getUserApiPaths(Long userId) {
        return userPermissionMapper.selectUserApiPaths(userId);
    }

    @Override
    public List<String> getRoleApiPaths(Integer roleId) {
        return rolePermissionMapper.selectRoleApiPaths(roleId);
    }

    @Override
    public boolean isAdmin(Long userId) {
        User user = userService.getById(userId);
        return user != null && user.getRole() == 1; // 角色1为管理员
    }

    @Override
    public boolean isSuperAdmin(Long userId) {
        User user = userService.getById(userId);
        return user != null && user.getRole() == 1; // 管理员拥有所有权限
    }

    @Override
    public List<String> getAllPermissionCodes() {
        return rolePermissionMapper.selectAllPermissionCodes();
    }

    @Override
    public boolean canEditTopic(Long userId) {
        try {
            User user = userService.getById(userId);
            if (user == null) {
                return false;
            }

            // 管理员可以编辑所有题目
            if (user.getRole() == 1) {
                return true;
            }

            // 教师和普通用户不能编辑题目（根据需求）
            return false;

        } catch (Exception e) {
            log.error("检查题目编辑权限失败", e);
            return false;
        }
    }

    @Override
    public boolean canDeleteTopic(Long userId) {
        // 只有管理员可以删除题目
        return isAdmin(userId);
    }

    @Override
    public boolean canDeletePaper(Long userId) {
        // 只有管理员可以删除试卷
        return isAdmin(userId);
    }

    /**
     * 根据权限代码获取权限名称
     */
    private String getPermissionName(String permissionCode) {
        // 这里可以从数据库或配置文件中获取权限名称
        // 简单实现，实际项目中应该有完整的权限字典
        switch (permissionCode) {
            case "TOPIC_SUBMIT": return "提交题目";
            case "TOPIC_VIEW": return "查看题目";
            case "TOPIC_AUDIT": return "审核题目";
            case "PAPER_GENERATE": return "生成试卷";
            case "PAPER_DOWNLOAD": return "下载试卷";
            case "USER_MANAGE": return "用户管理";
            case "ADMIN_MANAGE": return "管理员管理";
            default: return permissionCode;
        }
    }
}
