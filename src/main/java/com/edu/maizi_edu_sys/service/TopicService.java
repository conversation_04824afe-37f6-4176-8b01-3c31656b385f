package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.dto.TopicQueryDTO;
import com.edu.maizi_edu_sys.entity.Topic;

import java.time.LocalDate;
import java.util.List;
import java.util.Map; // Added import

public interface TopicService extends IService<Topic> {
    void validateAndSaveTopics(List<TopicDTO> topics);

    IPage<Topic> listTopics(TopicQueryDTO queryDTO);

    List<Map<String, Object>> countTopicsByTypeForKnowledgePoints(List<Integer> knowledgeIds); // Added method

    List<Map<String, Object>> getTopicStatistics(String type, LocalDate startDate, LocalDate endDate);

    /**
     * 保存或更新题目
     */
    void saveOrUpdateTopic(TopicDTO topicDTO, Long userId);

    /**
     * 获取用户今日上传数量
     */
    long getTodayUploadCount(Long userId);

    /**
     * 获取用户上传统计
     */
    Map<String, Object> getUserUploadStats(Long userId);

    /**
     * 获取管理员上传统计（按日/周/月）
     */
    Map<String, Object> getAdminUploadStats(String period, LocalDate startDate, LocalDate endDate);
}