package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.mapper.TopicAuditMapper;

import java.util.List;

/**
 * 题目审核服务接口
 */
public interface TopicAuditService {

    /**
     * 提交题目到审核队列
     */
    void submitTopicForAudit(TopicDTO topicDTO, Long userId);

    /**
     * 批量提交题目到审核队列
     */
    void submitTopicsForAudit(List<TopicDTO> topicDTOs, Long userId);

    /**
     * 分页查询审核列表
     */
    IPage<TopicAudit> getAuditList(int pageNum, int pageSize, Integer auditStatus, String keyword);

    /**
     * 审核通过
     */
    void approveTopicAudit(Long auditId, Long auditorId, String comment);

    /**
     * 审核拒绝
     */
    void rejectTopicAudit(Long auditId, Long auditorId, String rejectReason);

    /**
     * 获取审核详情
     */
    TopicAudit getAuditDetail(Long auditId);

    /**
     * 获取待审核题目数量
     */
    Long getPendingAuditCount();

    /**
     * 获取用户今日提交数量
     */
    Long getUserTodaySubmissionCount(Long userId);

    /**
     * 获取用户审核统计
     */
    TopicAuditMapper.TopicAuditStats getUserAuditStats(Long userId);

    /**
     * 自动审核超时题目
     */
    void autoApproveOverdueTopics();

    /**
     * 分页查询用户提交的审核记录
     */
    IPage<TopicAudit> getUserAuditList(Long userId, int pageNum, int pageSize, Integer auditStatus);
}
